# coding: utf-8
# Project：car_auto_test
# File：DbOperate.py
# Author：杨郑健
# Date ：2025/4/9 14:00
import sqlite3
from utils.LoggingSystem.Logger import logger


class DbOperate:
    def __init__(self, db_path):
        self.db_path = db_path

    def openDb(self, dbPath):
        conn = sqlite3.connect(database=dbPath, )
        cursor = conn.cursor()
        return conn, cursor

    def closeDb(self, conn, cursor):
        cursor.close()
        self.close = conn.close()

    def sql_execute(self, sql):
        """
        执行sql语句
        :param sql: sql语句
        :return: True/False 执行结果
        e.g. f'UPDATE Bet SET betCount=betCount+1 WHERE wxId=? AND roomId=?', (wxId, roomId)
        """
        conn, cursor = self.openDb(self.db_path)
        try:
            cursor.execute(sql)
            result = cursor.fetchall()
            conn.commit()
            self.closeDb(conn, cursor)
            return result
        except Exception as e:
            logger.info(f'sql语句执行失败: {e}')
            return False

    


