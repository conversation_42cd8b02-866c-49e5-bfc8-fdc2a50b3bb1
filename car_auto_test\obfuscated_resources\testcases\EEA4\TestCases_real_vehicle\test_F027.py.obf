# coding: utf-8
# Project：car_auto_test
# File：test_F027.py
# Author：杨郑健
# Date ：2025/6/24 13:33
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级结果")
@AllureDecorators.title("升级成功，下电失败点击确定")
def test_F027(driver, launch_app):
    """
    测试步骤
    1、“升级主页面”点击“立即升级”，“检测升级前置条件”检测通过，进入“更新进行中”
    2、制造下电失败，“升级成功，下电失败”点击“确定”，查看后台埋点数据

    预期结果
    2、“升级成功，下电失败结果页面“确认“按钮点击”
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        "tbox_status": "NOTREAD",
        "power": ["READY", "OFF"],
        "task": "normal",
        "gear": "P",
        "handbrake": "OFF"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'READY':
            objects.get_task()
            logger.info("READY挡验证完成")
            assert True
        elif current_state == 'OFF':
            logger.info("OFF挡升级成功显示，查看埋点数据")
            serial_tool.set_file("remote_poweroff", 1)
            objects.update_success()
            objects.click_element("success_poweroff_fail")
            serial_tool.set_file("remote_poweroff", 0)
            burying_points = Cloud("奇瑞汽车").get_burying_point()
            result = False
            for burying_point in burying_points:
                if burying_point["msg"] == '升级成功，下电失败结果页面"确认"按钮点击':
                    result = True
            assert result
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()