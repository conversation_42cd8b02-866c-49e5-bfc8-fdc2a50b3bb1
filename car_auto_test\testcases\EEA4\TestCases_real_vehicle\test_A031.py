# coding: utf-8
# Project：car_auto_test
# File：test_A031.py
# Author：杨郑健
# Date ：2025/6/10 17:22
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("无网络时获取不到pki证书，mda不会崩溃")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_A031(driver, launch_app):
    """
    测试步骤
    1、data/data/com.carota.chery.v4/scm/cert删除所有证书
    2、中断tbox、wifi网络连接
    3、车辆休眠后解防，查看无网络状态获取不到pki证书时主控是否崩溃
    4、恢复tbox网络，启动车辆

    预期结果
    3、无网络时pki证书获取失败，程序不会崩溃
    4、正常请求证书，发起检测"
    """

    pass