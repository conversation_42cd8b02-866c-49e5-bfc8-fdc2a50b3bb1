import logging
from logging.handlers import RotatingFileHandler
import os
import time

base_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.chdir(base_path)


class LogManager:
    def __init__(self, log_name, log_path,
                 max_file_size=5 * 1024 * 1024,
                 max_total_size=100 * 1024 * 1024):
        """
        日志管理类

        :param log_name: 日志名称
        :param log_path: 日志存储路径
        :param max_file_size: 单个日志文件最大大小（默认5MB）
        :param max_total_size: 日志总存储限制（默认100MB）
        """
        self.log_name = log_name
        self.log_path = log_path
        self.max_file_size = max_file_size
        self.max_total_size = max_total_size

        # 创建日志目录
        os.makedirs(log_path, exist_ok=True)

        # 初始化日志记录器
        self.logger = logging.getLogger(log_name)
        self.logger.setLevel(logging.DEBUG)

        # 清除现有处理器避免重复
        self._clear_existing_handlers()

        # 配置日志格式
        self._setup_formatter()

        # 添加控制台输出
        self._add_console_handler()

        # 添加文件输出
        self._add_file_handler()

        # 清理旧日志
        self._cleanup_old_logs()

    def _clear_existing_handlers(self):
        """清除现有处理器"""
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

    def _setup_formatter(self):
        """配置日志格式"""
        self.log_format = "%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
        self.date_format = "%Y-%m-%d %H:%M:%S"
        self.formatter = logging.Formatter(
            fmt=self.log_format,
            datefmt=self.date_format
        )

    def _add_console_handler(self):
        """添加控制台处理器"""
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(self.formatter)
        self.logger.addHandler(console_handler)

    def _add_file_handler(self):
        """添加文件处理器"""
        # 计算最大备份文件数
        backup_count = (self.max_total_size // self.max_file_size) - 1
        backup_count = max(backup_count, 0)  # 保证非负数

        # 配置滚动日志
        log_file = os.path.join(self.log_path, f"{self.log_name}.txt")
        file_handler = RotatingFileHandler(
            filename=log_file,
            maxBytes=self.max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(self.formatter)
        self.logger.addHandler(file_handler)

    def _cleanup_old_logs(self):
        """清理超出总大小限制的旧日志"""
        log_files = []
        total_size = 0

        # 收集所有相关日志文件
        for filename in os.listdir(self.log_path):
            if filename.startswith(f"{self.log_name}.txt"):
                filepath = os.path.join(self.log_path, filename)
                if os.path.isfile(filepath):
                    file_size = os.path.getsize(filepath)
                    mtime = os.path.getmtime(filepath)
                    log_files.append((filepath, mtime, file_size))
                    total_size += file_size

        # 按修改时间排序（最旧在前）
        log_files.sort(key=lambda x: x[1])

        # 删除最旧文件直到满足大小限制
        while total_size > self.max_total_size and len(log_files) > 1:
            oldest_file, oldest_mtime, oldest_size = log_files.pop(0)
            try:
                os.remove(oldest_file)
                total_size -= oldest_size
                print(f"清理旧日志: {oldest_file}")
            except Exception as e:
                print(f"清理日志失败: {str(e)}")


# 初始化日志管理器
log_manager = LogManager(
    log_name="Logger",
    log_path="./testreport/Logs",
    max_file_size=5 * 1024 * 1024,  # 5MB
    max_total_size=100 * 1024 * 1024  # 100MB
)

# 获取日志记录器
logger = log_manager.logger
