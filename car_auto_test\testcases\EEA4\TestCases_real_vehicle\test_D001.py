# coding: utf-8
# Project：car_auto_test
# File：test_D001.py
# Author：杨郑健
# Date ：2025/6/12 9:40
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("前置条件检测")
@AllureDecorators.title("前置条件检测")
@static_condition(conditions=
{
    "power": "ON",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_D001(driver, launch_app):
    pass