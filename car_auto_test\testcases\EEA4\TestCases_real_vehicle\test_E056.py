# coding: utf-8
# Project：car_auto_test
# File：test_E056.py
# Author：杨郑健
# Date ：2025/6/20 10:05
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("埋点数据")
@AllureDecorators.title("设置完预约时间后点击确定,查看预约成功埋点")
def test_E056(driver, launch_app):
    """
    测试步骤
    1、进入“升级主页面”
    2、点击“预约升级”，“免责声明”点击“同意”
    3、“预约时间设置”设置预约升级时间，点击“确定”，进入“预约成功”，查看后台埋点数据

    预期结果
    3、（code110）“预约成功显示”

    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        "tbox_status": "NOTREAD",
        "power": ["READY", "OFF"],
        "task": "normal",
        "gear": "P",
        "handbrake": "OFF"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'READY':
            objects.get_task()
            logger.info("READY挡验证完成")
            assert True
        elif current_state == 'OFF':
            logger.info("OFF挡预约升级，查看埋点数据")
            objects.tomorrow_time_set_pass()
            burying_points = Cloud("奇瑞汽车").get_burying_point()
            result = False
            for burying_point in burying_points:
                if burying_point["msg"] == '(code 110) 预约成功显示':
                    result = True
            assert result
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()