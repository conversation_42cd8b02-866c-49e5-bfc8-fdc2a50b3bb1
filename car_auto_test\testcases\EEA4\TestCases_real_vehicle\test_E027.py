# coding: utf-8
# Project：car_auto_test
# File：test_E027.py
# Author：杨郑健
# Date ：2025/6/13 18:01
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("埋点数据")
@AllureDecorators.title("前置条件检测主动取消")
def test_E027(driver, launch_app):
    """
    测试步骤
    1、“升级主页面”，点击“立即升级”
    2、“检测升级前置条件”车况不符合检测要求
    3、“检测升级前置条件”点击“取消120s”，查看后台埋点数据

    预期结果
    3、（code61）“前置条件页面点击“取消”按钮”
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        "tbox_status": "NOTREAD",
        "power": ["READY", "OFF"],
        "task": "normal",
        "gear": "P",
        "handbrake": "OFF"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'READY':
            objects.get_task()
            logger.info("READY挡验证完成")
            assert True
        elif current_state == 'OFF':
            logger.info("OFF挡制造进OTA模式失败，查看埋点数据")
            objects.disclaimer_click()
            objects.cancel_preconditions()
            burying_points = Cloud("奇瑞汽车").get_burying_point()
            result = False
            for burying_point in burying_points:
                if burying_point["msg"] == '(code 61) 前置条件页面点击"取消"按钮':
                    result = True
            assert result
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()