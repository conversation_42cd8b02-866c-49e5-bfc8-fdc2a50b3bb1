# coding: utf-8
# Project：car_auto_test
# File：test_C019.py
# Author：杨郑健
# Date ：2025/6/11 18:19
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级主页面/小红点")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
@AllureDecorators.title("连续点击预约升级")
def test_C019(driver, launch_app):
    """    目前连点机制不可用,用例先不开放
    测试步骤
    1、进入“升级主页面”
    2、三秒内连续点击“预约升级”
    3、三秒后点击“预约升级”"

    预期结果
    2、三秒内连续点击“预约升级”，无法触发“注意事项”，页面无闪烁
    3、三秒后点击“预约升级”，正常弹框“注意事项”"
    """
    objects = Step_object(driver)
    objects.get_task()
    result = objects.appointment_upgrade_fast_click()
    assert result