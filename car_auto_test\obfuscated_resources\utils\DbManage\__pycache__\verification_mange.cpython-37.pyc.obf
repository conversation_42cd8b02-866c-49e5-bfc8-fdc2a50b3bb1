Qg0NCgAAAACYvlNocQIAAOMAAAAAAAAAAAAAAAADAAAAQAAAAHMyAAAAZABkAWwAbQFaAQEAZABkAmwCbQNaAwEAZABkA2wEWgRHAGQEZAWEAGQFgwJaBWQDUwApBukAAAAAKQHaCURiT3BlcmF0ZSkB2gtTUUxJVEVfUEFUSE5jAAAAAAAAAAAAAAAAAgAAAEAAAABzHAAAAGUAWgFkAFoCZAFkAoQAWgNkA2QEhABaBGQFUwApBtoRVmVyaWZpY2F0aW9uTWFuZ2VjAQAAAAAAAAABAAAAAwAAAEMAAABzEgAAAHQAdAFkARcAgwF8AF8CZABTACkCTnoSXFZlcmlmaWNhdGlvbkRCLmRiKQNyAgAAAHIDAAAA2gJkYikB2gRzZWxmqQByBwAAAPpYQzpcVXNlcnNcY2Fyb3RhLnVzZXJcRGVza3RvcFx3dWh1Y29kZVxjYXJfYXV0b190ZXN0XHV0aWxzXERiTWFuYWdlXHZlcmlmaWNhdGlvbl9tYW5nZS5wedoIX19pbml0X18MAAAAcwIAAAAAAXoaVmVyaWZpY2F0aW9uTWFuZ2UuX19pbml0X19jAgAAAAAAAAAEAAAAAwAAAEMAAABzOgAAAGQBfAGbAJ0CfQJ8AGoAoAF8AqEBfQN8A3MeZAJTAHwDZAMZAGQDGQB8A2QDGQBkBBkAZAWcAmcBUwApBk56KXNlbGVjdCAqIGZyb20gdmVyaWZpY2F0aW9uIHdoZXJlIGBrZXlgID0gRnIBAAAA6QEAAAApAtoDa2V52gV2YWx1ZSkCcgUAAABaC3NxbF9leGVjdXRlKQRyBgAAAFoIY3Vycl9rZXnaA3NxbNoDcmVzcgcAAAByBwAAAHIIAAAA2gtzZWxlY3RfY29kZQ8AAABzCgAAAAABCgEMAQQABAF6HVZlcmlmaWNhdGlvbk1hbmdlLnNlbGVjdF9jb2RlTikF2ghfX25hbWVfX9oKX19tb2R1bGVfX9oMX19xdWFsbmFtZV9fcgkAAAByDwAAAHIHAAAAcgcAAAByBwAAAHIIAAAAcgQAAAALAAAAcwQAAAAIAQgDcgQAAAApBloYdXRpbHMuRGJNYW5hZ2UuRGJPcGVyYXRlcgIAAADaDWNvbmZpZy5Db25maWdyAwAAANoEdGltZXIEAAAAcgcAAAByBwAAAHIHAAAAcggAAADaCDxtb2R1bGU+BwAAAHMGAAAADAEMAQgC