# coding: utf-8
# Project：car_auto_test
# File：test_E012.py
# Author：杨郑健
# Date ：2025/6/13 10:02
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("埋点数据")
@AllureDecorators.title("注意事项")
@static_condition(conditions=
{
    "power": "ON",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_E012(driver, launch_app):
    """
    测试步骤
    1、进入“升级主页面”
    2、点击“立即升级”，进入“注意事项”
    3、“注意事项”点击“继续升级”，进入“免责声明”，查看后台埋点数据"

    预期结果
    3、（code20）“免责页面显示”
    """
    objects = Step_object(driver)
    objects.get_task()
    objects.matter_click()
    burying_points = Cloud("奇瑞汽车").get_burying_point()
    result = False
    for burying_point in burying_points:
        if burying_point["msg"] == '注意事项页面"继续升级"按钮点击':
            result = True
    assert result