'''导出ui截图测试离线报告API'''
import os
import json
import shutil
import zipfile
from datetime import datetime
from flask import Blueprint, jsonify, send_file, request

report_bp = Blueprint('report', __name__)



@report_bp.route('/api/export_offline_page', methods=['POST'])
def export_offline_page():
    """导出完整的离线页面，包含所有静态资源"""
    try:
        # 创建临时导出目录
        export_dir = 'exports'
        os.makedirs(export_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建临时工作目录
        temp_dir = os.path.join(export_dir, f'temp_{timestamp}')
        os.makedirs(temp_dir, exist_ok=True)
        
        # 读取模板文件
        with open('templates/dashboard.html', 'r', encoding='utf-8') as f:
            template = f.read()
        with open('static/js/dashboard.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        with open('static/js/report.js', 'r', encoding='utf-8') as f:
            report_js_content = f.read()
        with open('static/css/dashboard.css', 'r', encoding='utf-8') as f:
            css_content = f.read()

        # 获取测试数据
        data = request.json
        screenshots = data.get("screenshots", [])
        
        # 创建images目录
        images_dir = os.path.join(temp_dir, 'images')
        os.makedirs(images_dir, exist_ok=True)

        # 处理图片路径并复制图片
        for i, screenshot in enumerate(screenshots):
            if screenshot.get("image_path"):
                # 获取原始图片路径
                original_path = screenshot["image_path"]
                print(f"处理图片路径: {original_path}")
                
                # 尝试多种可能的源文件路径
                possible_source_paths = []
                
                # 1. 如果路径以 /static/images/ 或 static/images/ 开头，直接映射到 static/images/ 目录
                if original_path.startswith('/static/images/'):
                    relative_path = original_path[1:]  # 去掉第一个斜杠
                    possible_source_paths.append(relative_path)
                elif original_path.startswith('static/images/'):
                    possible_source_paths.append(original_path)
                else:
                    possible_source_paths.append(original_path)  # 兜底
                
                # 尝试所有可能的源文件路径
                file_copied = False
                for source_path in possible_source_paths:
                    source_path = os.path.normpath(source_path)
                    print(f"尝试源文件路径: {source_path}")
                    
                    if os.path.exists(source_path):
                        try:
                            # 提取 static/images/ 后的相对路径
                            if 'static/images/' in source_path:
                                idx = source_path.find('static/images/')
                                relative_part = source_path[idx + len('static/images/'):]
                            elif '\\static\\images\\' in source_path:
                                idx = source_path.find('\\static\\images\\')
                                relative_part = source_path[idx + len('\\static\\images\\'):]
                            else:
                                relative_part = os.path.basename(source_path)

                            print(f"提取的相对路径: {relative_part}")
                            
                            # 在images目录下创建对应的子目录结构
                            target_subdir = os.path.join(images_dir, os.path.dirname(relative_part))
                            os.makedirs(target_subdir, exist_ok=True)
                            
                            # 复制文件到对应的子目录
                            target_path = os.path.join(target_subdir, os.path.basename(source_path))
                            shutil.copy2(source_path, target_path)
                            
                            # 更新图片路径为相对路径
                            screenshot["image_path"] = f"images/{relative_part}"
                            print(f"成功复制图片: {source_path} -> {target_path}")
                            file_copied = True
                            break
                        except Exception as e:
                            print(f"复制文件失败: {source_path} -> {target_path}, 错误: {str(e)}")
                
                if not file_copied:
                    print(f"警告: 无法找到或复制图片文件: {original_path}")
                    # 将图片路径设置为空，避免前端显示错误
                    screenshot["image_path"] = ""

        test_data = {
            "screenshots": screenshots,
            "test_status": "completed",
            "start_time": screenshots[0].get("timestamp", "") if screenshots else "",
            "end_time": screenshots[-1].get("timestamp", "") if screenshots else "",
            "config": {
                "itemsPerPage": 10,
                "refreshInterval": 0
            }
        }

        # 注入初始数据
        init_script = f'''
        <script>
            window.CONFIG = {{
                itemsPerPage: 10,
                refreshInterval: 0
            }};
            window.initialTestData = {json.dumps(test_data, ensure_ascii=False)};
        </script>
        '''

        # 修改模板
        template = template.replace(
            '<link rel="stylesheet" href="{{ url_for(\'static\', filename=\'css/dashboard.css\') }}">',
            f'<style>{css_content}</style>'
        )
        template = template.replace(
            '<script src="{{ url_for(\'static\', filename=\'js/dashboard.js\') }}"></script>',
            f'<script>{js_content}</script>\n<script>{report_js_content}</script>\n{init_script}'
        )

        # 保存HTML文件
        html_path = os.path.join(temp_dir, 'report.html')
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(template)

        # 创建zip文件
        zip_filename = f'offline_report_{timestamp}.zip'
        zip_path = os.path.join(export_dir, zip_filename)

        # 打印调试信息
        print(f"临时目录: {temp_dir}")
        print(f"图片目录: {images_dir}")
        print("图片文件列表:")
        for root, dirs, files in os.walk(images_dir):
            for file in files:
                print(f"  {os.path.join(root, file)}")

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 添加HTML文件
            zipf.write(html_path, 'report.html')
            # 添加images文件夹中的文件，保持目录结构
            for root, dirs, files in os.walk(images_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    # 计算相对于images_dir的路径
                    relative_path = os.path.relpath(file_path, images_dir)
                    arcname = os.path.join('images', relative_path)
                    zipf.write(file_path, arcname)
                    print(f"添加到zip: {file_path} -> {arcname}")

        # 清理临时目录
        shutil.rmtree(temp_dir)

        return send_file(
            zip_path,
            as_attachment=True,
            download_name=zip_filename,
            mimetype='application/zip'
        )

    except Exception as e:
        # 确保清理临时目录
        if 'temp_dir' in locals():
            shutil.rmtree(temp_dir, ignore_errors=True)
        print(f"导出失败: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"导出离线页面失败: {str(e)}"
        }), 500
