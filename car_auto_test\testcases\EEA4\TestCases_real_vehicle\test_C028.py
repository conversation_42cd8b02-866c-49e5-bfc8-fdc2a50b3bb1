# coding: utf-8
# Project：car_auto_test
# File：test_C028.py
# Author：杨郑健
# Date ：2025/6/11 18:41
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级主页面/小红点")
@AllureDecorators.title("免责声明:同意倒计时过程中点击取消")
@static_condition(conditions=
{
    "power": "ON",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_C028(driver, launch_app):
    """
    测试步骤
    1、替换发行说明检测到升级任务并下载完成
    2、检查点击立即升级后注意事项页面点击继续升级弹框触发
    3、同意倒计时过程中点击取消后检查弹框是否存在

    预期结果
    3、“免责声明”弹框关闭
    """
    objects = Step_object(driver)
    objects.get_task()
    objects.disclaimer_cancel_click()
    res = objects.check_element_exists("disclaimer_cancel_frame")
    assert not res