# coding: utf-8
# Project：car_auto_test
# File：verification_mange.py
# Author：杨郑健
# Date ：2025/4/28 16:21

from utils.DbManage.DbOperate import DbOperate
from config.Config import SQLITE_PATH
import time

class VerificationMange:
    def __init__(self):
        self.db = DbOperate(SQLITE_PATH + r"\VerificationDB.db")
    # [{'key': '167', 'value': 'x'}]
    def select_code(self, curr_key):
        sql = f"select * from verification where `key` = {curr_key}"
        res = self.db.sql_execute(sql)
        if not res: return False
        return [{'key': res[0][0], "value": res[0][1]}]

