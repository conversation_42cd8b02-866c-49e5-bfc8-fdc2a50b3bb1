'''将base1 复制 到 base2 中，  如果相同就覆盖'''

import os
import shutil

src_base = r'T26_TASK_FAIL'
dst_base = r'T26PHEV'

for name in os.listdir(src_base):
    src_dir = os.path.join(src_base, name)
    dst_dir = os.path.join(dst_base, name)
    if os.path.isdir(src_dir):
        if not os.path.exists(dst_dir):
            os.makedirs(dst_dir)
        for file in os.listdir(src_dir):
            src_file = os.path.join(src_dir, file)
            dst_file = os.path.join(dst_dir, file)
            shutil.copy2(src_file, dst_file)  # copy2会覆盖同名文件
        print(f"已复制 {src_dir} 到 {dst_dir}")

print("全部复制完成！")
