'''测试主函数API'''
import threading
from config.api_config import APIConfig
from flask import Blueprint, jsonify, request
from datetime import datetime

from utils.LoggingSystem.Logger import logger
import traceback
import sys

start_test_bp = Blueprint('test_exec', __name__)

@start_test_bp.route('/api/start_test', methods=['POST'])
def start_test():
    """启动测试"""
    if APIConfig.test_data_store["test_status"] == "running":
        return jsonify({"status": "error", "message": "测试正在进行中"})

    # 获取请求中的车型参数
    data = request.get_json() or {}
    selected_vehicle = data.get('vehicle', 'E03')  # 默认为E03
    vin =data.get('vin')
    vehicle_type = data.get("vehicle_type")
    task1 = data.get("task1")
    task2 = data.get("task2")
    task3 = data.get("task3")
    task4 = data.get("task4")
    password = data.get("password")
    languages = data.get("languages")

    print(f"启动测试，选择车型: {selected_vehicle}")
    logger.info(f"选择的语言: {languages}")

    # 只有在非运行状态且未完成时才重置数据
    if not APIConfig.test_data_store["is_test_completed"]:
        APIConfig.test_data_store.update({
            "current_test": None,
            "screenshots": [],
            "test_status": "running",
            "current_language": "",
            "current_mode": "",
            "start_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "end_time": None,
            "is_test_completed": False,
            "selected_vehicle": selected_vehicle,
            "vehicle_type": vehicle_type,  # 车辆类型phev/TANKER
            "vin": vin,   # 保存选择的车型
            "task1": task1,
            "task2": task2,
            "task3": task3,
            "task4": task4,
            "password":password,
            "languages": languages
        })
    # 启动测试线程，传递车型参数
    test_thread = threading.Thread(target=run_main_test, args=(selected_vehicle, vehicle_type, languages))
    test_thread.daemon = True
    test_thread.start()

    return jsonify({"status": "success", "message": f"测试已启动 (车型: {selected_vehicle})"})

@start_test_bp.route('/api/stop_test', methods=['POST'])
def stop_test():
    """停止测试"""
    APIConfig.test_data_store["test_status"] = "stopped"
    APIConfig.test_data_store["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    return jsonify({"status": "success", "message": "测试已停止"})

@start_test_bp.route('/api/test_status')
def get_test_status():
    """获取测试状态"""
    return jsonify(APIConfig.test_data_store)



def run_main_test(selected_vehicle, vehicle_type, languages):
    """运行主测试函数"""
    try:
        logger.info(f"开始执行测试 - 车型: {selected_vehicle}, 类型: {vehicle_type}, 语言: {languages}")
        
        import main
        # from card_key_check import code
        if APIConfig.test_data_store.get("password", ""):
        # if APIConfig.test_data_store.get("password", "") == code:
            logger.info("密码验证通过，开始执行测试")
            main.ui_all_combinations(selected_vehicle, vehicle_type, languages=languages)
            APIConfig.test_data_store["test_status"] = "completed"
            APIConfig.test_data_store["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            APIConfig.test_data_store["is_test_completed"] = True  # 标记测试已完成
            logger.info("测试执行完成")
        else:
            error_msg = "密码验证失败"
            logger.error(error_msg)
            APIConfig.test_data_store["test_status"] = "failed"
            APIConfig.test_data_store["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            APIConfig.test_data_store["is_test_completed"] = True  # 即使失败也标记为完成
            APIConfig.test_data_store["error_message"] = error_msg
            return False

    except Exception as e:
        error_msg = f"测试执行失败: {str(e)}"
        logger.error(error_msg)
        logger.error(f"异常类型: {type(e).__name__}")
        logger.error(f"异常详情: {traceback.format_exc()}")
        
        # 记录到控制台
        print(f"测试执行失败: {str(e)}")
        print(f"异常类型: {type(e).__name__}")
        print(f"异常详情: {traceback.format_exc()}")
        
        APIConfig.test_data_store["test_status"] = "failed"
        APIConfig.test_data_store["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        APIConfig.test_data_store["is_test_completed"] = True  # 即使失败也标记为完成
        APIConfig.test_data_store["error_message"] = error_msg
        APIConfig.test_data_store["error_details"] = traceback.format_exc()