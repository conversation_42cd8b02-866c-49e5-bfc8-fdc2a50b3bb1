# coding: utf-8
# Project：car_auto_test
# File：test_E008.py
# Author：杨郑健
# Date ：2025/6/12 16:52
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("埋点数据")
@AllureDecorators.title("埋点数据：点击预约升级")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_E008(driver, launch_app):
    """
    测试步骤
    1、进入“升级主页面”
    2、点击“预约升级”，查看后台埋点数据

    预期结果
    2、（code12）“点击主页面预约升级按钮”
    """
    objects = Step_object(driver)
    objects.get_task()
    objects.appointment_upgrade_click()
    burying_points = Cloud("奇瑞汽车").get_burying_point()
    result = False
    for burying_point in burying_points:
        if burying_point["msg"] == '(code 12) 点击主页面"预约升级"按钮':
            result = True
    assert result