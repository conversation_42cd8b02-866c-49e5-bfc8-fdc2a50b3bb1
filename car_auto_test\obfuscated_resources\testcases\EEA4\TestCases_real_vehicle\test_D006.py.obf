# coding: utf-8
# Project：car_auto_test
# File：test_D006.py
# Author：杨郑健
# Date ：2025/6/12 14:40
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("前置条件检测")
@AllureDecorators.title("车速≤2km/h")
def test_D006(driver, launch_app):
    """
    测试步骤
    1、行驶车辆，车速大于2km/h，查看“检测升级前置条件”
    2、行驶车辆，车速小于等于2km/h，查看“检测升级前置条件”"

    预期结果
    1、“车速≤2km/h”检测不通过
    2、“车速≤2km/h”检测通过"
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        'speed': ['50', '0'],  # 将挡位放在列表中
        "task": "normal"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == '50':
            logger.info("车速为50，检测前置条件不过")
            objects.get_task()
            objects.disclaimer_click()
            sleep(5)
            result = objects.check_element_exists("precondition")
            assert result
        elif current_state == '0':
            logger.info("车速为0，检测前置条件通过")
            sleep(5)
            result = objects.check_element_exists("precondition")
            assert result == False
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()