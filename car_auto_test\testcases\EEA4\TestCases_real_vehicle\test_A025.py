# coding: utf-8
# Project：car_auto_test
# File：test_A025.py
# Author：杨郑健
# Date ：2025/6/10 16:46
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from time import sleep
import pytest

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("*>目标版本任务检测")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_A025(driver, launch_app):
    """
    测试步骤
    1、电源挡位切换为READY，后台配置*>目标版本任务
    2、任务检测结束后检查拿到任务的状态
    3、查看是否能检测到任务并正常下载

    预期结果
    3、可正常检测到升级任务
    """
    objects = Step_object(driver)
    get_task_result = objects.get_task()
    assert get_task_result