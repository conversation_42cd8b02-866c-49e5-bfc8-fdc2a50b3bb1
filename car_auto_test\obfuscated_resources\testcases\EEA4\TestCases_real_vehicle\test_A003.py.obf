# coding: utf-8
# Project：car_auto_test
# File：test_A003.py
# Author：杨郑健
# Date ：2025/6/10 14:09
from testcases.EEA4.PageObjects.TestObject import Step_object
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from utils.LoggingSystem.Logger import logger
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.SerialTool.serial_tool import serial_tool
from utils.CloudOperation.chery_operation import Cloud
from utils.ReportGenerator.Allure import *

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("监听OBD诊断状态")
def test_A003(driver, launch_app):
    """
    测试步骤
    1、ACC挡位模拟OBD口占用
    2、切换至READY检测任务
    3、检查是否能检测到任务以及是否有OBD被占用关键字（DIAGNOSE_CONNECTED）

    预期结果
    3、obd诊断状态无法发起检测，监听obd诊断状态，实时日志中有 DIAGNOSE_CONNECTED 状态输出
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        'power': ['ACC', 'READY'],  # 将挡位放在列表中
        "task": "normal"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'ACC':
            serial_tool.set_file("obd_status", 1)
            logger.info("ACC挡时切换OBD状态")
            assert True
        elif current_state == 'READY':
            logger.info("READY验证任务检测状态和OBD被占用日志关键字")
            get_task_result = objects.get_task()
            result = otaLogAnalysis.find_keywords(["DIAGNOSE_CONNECTED"])
            logger.info(f"实时日志检测结果:{result}")
            serial_tool.set_file("obd_status", 0)
            assert result and not get_task_result
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()