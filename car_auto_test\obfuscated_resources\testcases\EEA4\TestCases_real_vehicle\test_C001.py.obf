# coding: utf-8
# Project：car_auto_test
# File：test_C001.py
# Author：杨郑健
# Date ：2025/6/11 14:53
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级主页面/小红点")
@AllureDecorators.title("出厂状态：小红点")
@static_condition(conditions=
{
    "power": "ACC",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_C001(driver, reset_app):
    """
    测试步骤
    1、工程模式点击“清除数据”
    2、车端未检测到升级任务
    3、查看ota应用图标

    预期结果
    3、无小红点显示
    """
    Object = Step_object(driver)
    adb = AdbExec()
    currhash = Object.get_app_hash()
    adb.execute(f"adb shell am start {FOUR_APP_PACKAGE}/{FOUR_APP_PACKAGE_ACTIVITY}")
    adb.home()
    newhash = Object.get_app_hash()
    assert currhash == newhash