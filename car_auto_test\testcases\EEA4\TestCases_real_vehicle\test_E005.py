# coding: utf-8
# Project：car_auto_test
# File：test_E005.py
# Author：杨郑健
# Date ：2025/6/12 16:03
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("埋点数据")
@AllureDecorators.title("发行说明：点击详情")
@static_condition(conditions=
{
    # "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_E005(driver, launch_app):
    """
    测试步骤
    1、进入“升级主页面”
    2、点击“详情”，查看后台埋点数据"

    预期结果
    2、“升级策略页面显示”
    """
    objects = Step_object(driver)
    objects.get_task()
    objects.check_details_home()
    burying_point = Cloud("奇瑞汽车").get_burying_point()
    result = False
    for i in burying_point:
        if i["msg"] == '升级策略页面显示':
            result = True
    assert result