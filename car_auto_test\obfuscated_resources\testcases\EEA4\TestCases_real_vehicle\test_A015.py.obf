# coding: utf-8
# Project：car_auto_test
# File：test_A015.py
# Author：杨郑健
# Date ：2025/6/10 15:55
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from time import sleep
import pytest

@pytest.mark.skip
@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("检测中断")
def test_A015(driver, launch_app):
    """   缺少三次重试关键字标志
    测试步骤
    1、车辆休眠后解防，启动车辆，待tbox诊断完成，发起检测
    2、切换电源至off档打断检测
    3、切换电源至acc档，待三次检测重试结束"

    预期结果
    3、检测中断，本次上电周期内不再发起检测
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        'power': ['READY', 'ACC'],  # 将挡位放在列表中
        "tbox_status": "NOTREAD"
    },
        delay=None
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'READY':
            logger.info("READY挡时检测任务")
            result = otaLogAnalysis.find_keywords(["TEL_DIAGNOSE_NOT_READ"])
            objects.get_task_noback()
            assert result
        elif current_state == 'ACC':
            logger.info("ACC挡时检查任务检测结果")
            get_task_result = objects.check_update_status
            result = otaLogAnalysis.find_keywords([""])
            assert result and not get_task_result
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()