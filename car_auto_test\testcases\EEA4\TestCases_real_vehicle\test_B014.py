# coding: utf-8
# Project：car_auto_test
# File：test_B014.py
# Author：杨郑健
# Date ：2025/6/11 14:22
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级包下载模块")
@AllureDecorators.title("升级包下载完成后：主页面显示")
@static_condition(conditions=
{
    "power": "ACC",
    "tbox_status": "READ",
    "task": "normal"
}
)
def test_B014(driver, launch_app):
    """
    测试步骤
    1、后台配置“用户确认”升级任务
    2、车辆解防上电，车端检测到升级任务并下载完成
    3、查看“升级主页面”

    预期结果
    3、“升级主页面”显示：“发现新版本”、升级时长、“发行说明”，“预约升级”(高亮)“立即升级”按钮均可点击
    """
    objects = Step_object(driver)
    objects.get_task()
    check_result = objects.check_home_button()
    assert check_result