# coding: utf-8
# Project：car_auto_test
# File：test_E015.py
# Author：杨郑健
# Date ：2025/6/13 13:17
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("埋点数据")
@AllureDecorators.title("免责声明点击“取消”")
@static_condition(conditions=
{
    "power": "ON",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_E015(driver, launch_app):
    """
    测试步骤
    1、“升级主页面”，点击“立即升级”，“免责声明”点击“同意”
    2、进入“车况检查中”，查看后台埋点数据

    预期结果
    2、“车况检查中页面显示”
    """
    objects = Step_object(driver)
    objects.get_task()
    objects.disclaimer_click()
    objects.update_success()
    burying_points = Cloud("奇瑞汽车").get_burying_point()
    result = False
    for burying_point in burying_points:
        if burying_point["msg"] == '车况检查中页面显示':
            result = True
    assert result