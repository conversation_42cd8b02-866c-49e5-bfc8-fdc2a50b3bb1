# -*- coding:utf-8 -*-
#  zlgcan.py

#
#  Language: Python 2.7, 3.6
#  ------------------------------------------------------------------
#
import os
import time
from ctypes import *
import platform
from utils.LoggingSystem.Logger import logger
from config.Config import BASE_DIR,ZLGLOG_PATH
import threading
ZCAN_DEVICE_TYPE = c_uint

INVALID_DEVICE_HANDLE = 0
INVALID_CHANNEL_HANDLE = 0

'''
 Device Type
'''
ZCAN_USBCANFD_400U = ZCAN_DEVICE_TYPE(76)
ZCAN_USBCANFD_200U = ZCAN_DEVICE_TYPE(41)
ZCAN_USBCANFD_100U = ZCAN_DEVICE_TYPE(42)
ZCAN_USBCANFD_MINI = ZCAN_DEVICE_TYPE(43)
ZCAN_CANFDCOM_100IE = ZCAN_DEVICE_TYPE(44)


'''
 Interface return status
'''
ZCAN_STATUS_ERR = 0
ZCAN_STATUS_OK = 1
ZCAN_STATUS_ONLINE = 2
ZCAN_STATUS_OFFLINE = 3
ZCAN_STATUS_UNSUPPORTED = 4

'''
 CAN type
'''
ZCAN_TYPE_CAN = c_uint(0)
ZCAN_TYPE_CANFD = c_uint(1)

'''
 Device information
'''


class ZCAN_DEVICE_INFO(Structure):
    _fields_ = [("hw_Version", c_ushort),
                ("fw_Version", c_ushort),
                ("dr_Version", c_ushort),
                ("in_Version", c_ushort),
                ("irq_Num", c_ushort),
                ("can_Num", c_ubyte),
                ("str_Serial_Num", c_ubyte * 20),
                ("str_hw_Type", c_ubyte * 40),
                ("reserved", c_ushort * 4)]

    def __str__(self):
        return "Hardware Version:%s\nFirmware Version:%s\nDriver Interface:%s\nInterface Interface:%s\nInterrupt Number:%d\nCAN Number:%d\nSerial:%s\nHardware Type:%s\n" % ( \
            self.hw_version, self.fw_version, self.dr_version, self.in_version, self.irq_num, self.can_num, self.serial,
            self.hw_type)

    def _version(self, version):
        return ("V%02x.%02x" if version // 0xFF >= 9 else "V%d.%02x") % (version // 0xFF, version & 0xFF)

    @property
    def hw_version(self):
        return self._version(self.hw_Version)

    @property
    def fw_version(self):
        return self._version(self.fw_Version)

    @property
    def dr_version(self):
        return self._version(self.dr_Version)

    @property
    def in_version(self):
        return self._version(self.in_Version)

    @property
    def irq_num(self):
        return self.irq_Num

    @property
    def can_num(self):
        return self.can_Num

    @property
    def serial(self):
        serial = ''
        for c in self.str_Serial_Num:
            if c > 0:
                serial += chr(c)
            else:
                break
        return serial

    @property
    def hw_type(self):
        hw_type = ''
        for c in self.str_hw_Type:
            if c > 0:
                hw_type += chr(c)
            else:
                break
        return hw_type


class _ZCAN_CHANNEL_CAN_INIT_CONFIG(Structure):
    _fields_ = [("acc_code", c_uint),
                ("acc_mask", c_uint),
                ("reserved", c_uint),
                ("filter", c_ubyte),
                ("timing0", c_ubyte),
                ("timing1", c_ubyte),
                ("mode", c_ubyte)]


class _ZCAN_CHANNEL_CANFD_INIT_CONFIG(Structure):
    _fields_ = [("acc_code", c_uint),
                ("acc_mask", c_uint),
                ("abit_timing", c_uint),
                ("dbit_timing", c_uint),
                ("brp", c_uint),
                ("filter", c_ubyte),
                ("mode", c_ubyte),
                ("pad", c_ushort),
                ("reserved", c_uint)]


class _ZCAN_CHANNEL_INIT_CONFIG(Union):
    _fields_ = [("can", _ZCAN_CHANNEL_CAN_INIT_CONFIG), ("canfd", _ZCAN_CHANNEL_CANFD_INIT_CONFIG)]


class ZCAN_CHANNEL_INIT_CONFIG(Structure):
    _fields_ = [("can_type", c_uint),
                ("config", _ZCAN_CHANNEL_INIT_CONFIG)]


class ZCAN_CHANNEL_ERR_INFO(Structure):
    _fields_ = [("error_code", c_uint),
                ("passive_ErrData", c_ubyte * 3),
                ("arLost_ErrData", c_ubyte)]


class ZCAN_CHANNEL_STATUS(Structure):
    _fields_ = [("errInterrupt", c_ubyte),
                ("regMode", c_ubyte),
                ("regStatus", c_ubyte),
                ("regALCapture", c_ubyte),
                ("regECCapture", c_ubyte),
                ("regEWLimit", c_ubyte),
                ("regRECounter", c_ubyte),
                ("regTECounter", c_ubyte),
                ("Reserved", c_ubyte)]


class ZCAN_CAN_FRAME(Structure):
    _fields_ = [("can_id", c_uint, 29),
                ("err", c_uint, 1),
                ("rtr", c_uint, 1),
                ("eff", c_uint, 1),
                ("can_dlc", c_ubyte),
                ("__pad", c_ubyte),
                ("__res0", c_ubyte),
                ("__res1", c_ubyte),
                ("data", c_ubyte * 8)]


class ZCAN_CANFD_FRAME(Structure):
    _fields_ = [("can_id", c_uint, 29),
                ("err", c_uint, 1),
                ("rtr", c_uint, 1),
                ("eff", c_uint, 1),
                ("len", c_ubyte),
                ("brs", c_ubyte, 1),
                ("esi", c_ubyte, 1),
                ("__res", c_ubyte, 6),
                ("__res0", c_ubyte),
                ("__res1", c_ubyte),
                ("data", c_ubyte * 64)]


class ZCAN_Transmit_Data(Structure):
    _fields_ = [("frame", ZCAN_CAN_FRAME), ("transmit_type", c_uint)]


class ZCAN_Receive_Data(Structure):
    _fields_ = [("frame", ZCAN_CAN_FRAME), ("timestamp", c_ulonglong)]


class ZCAN_TransmitFD_Data(Structure):
    _fields_ = [("frame", ZCAN_CANFD_FRAME), ("transmit_type", c_uint)]


class ZCAN_ReceiveFD_Data(Structure):
    _fields_ = [("frame", ZCAN_CANFD_FRAME), ("timestamp", c_ulonglong)]


class ZCAN_AUTO_TRANSMIT_OBJ(Structure):
    _fields_ = [("enable", c_ushort),
                ("index", c_ushort),
                ("interval", c_uint),
                ("obj", ZCAN_Transmit_Data)]


class ZCANFD_AUTO_TRANSMIT_OBJ(Structure):
    _fields_ = [("enable", c_ushort),
                ("index", c_ushort),
                ("interval", c_uint),
                ("obj", ZCAN_TransmitFD_Data)]


class IProperty(Structure):
    _fields_ = [("SetValue", c_void_p),
                ("GetValue", c_void_p),
                ("GetPropertys", c_void_p)]


class ZCAN(object):
    def __init__(self):
        if platform.system() == "Windows":

            self.__dll = windll.LoadLibrary(BASE_DIR + "./utils/ZlgCan/zlgcan.dll")
        else:
            print("No support now!")
        if self.__dll == None:
            print("DLL couldn't be loaded!")

    def OpenDevice(self, device_type, device_index, reserved):
        try:
            return self.__dll.ZCAN_OpenDevice(device_type, device_index, reserved)
        except:
            print("Exception on OpenDevice!")
            raise

    def CloseDevice(self, device_handle):
        try:
            return self.__dll.ZCAN_CloseDevice(device_handle)
        except:
            print("Exception on CloseDevice!")
            raise

    def GetDeviceInf(self, device_handle):
        try:
            info = ZCAN_DEVICE_INFO()
            ret = self.__dll.ZCAN_GetDeviceInf(device_handle, byref(info))
            return info if ret == ZCAN_STATUS_OK else None
        except:
            print("Exception on ZCAN_GetDeviceInf")
            raise

    def DeviceOnLine(self, device_handle):
        try:
            return self.__dll.ZCAN_IsDeviceOnLine(device_handle)
        except:
            print("Exception on ZCAN_ZCAN_IsDeviceOnLine!")
            raise

    def InitCAN(self, device_handle, can_index, init_config):
        try:
            return self.__dll.ZCAN_InitCAN(device_handle, can_index, byref(init_config))
        except:
            print("Exception on ZCAN_InitCAN!")
            raise

    def StartCAN(self, chn_handle):
        try:
            return self.__dll.ZCAN_StartCAN(chn_handle)
        except:
            print("Exception on ZCAN_StartCAN!")
            raise

    def ResetCAN(self, chn_handle):
        try:
            return self.__dll.ZCAN_ResetCAN(chn_handle)
        except:
            print("Exception on ZCAN_ResetCAN!")
            raise

    def ClearBuffer(self, chn_handle):
        try:
            return self.__dll.ZCAN_ClearBuffer(chn_handle)
        except:
            print("Exception on ZCAN_ClearBuffer!")
            raise

    def ReadChannelErrInfo(self, chn_handle):
        try:
            ErrInfo = ZCAN_CHANNEL_ERR_INFO()
            ret = self.__dll.ZCAN_ReadChannelErrInfo(chn_handle, byref(ErrInfo))
            return ErrInfo if ret == ZCAN_STATUS_OK else None
        except:
            print("Exception on ZCAN_ReadChannelErrInfo!")
            raise

    def ReadChannelStatus(self, chn_handle):
        try:
            status = ZCAN_CHANNEL_STATUS()
            ret = self.__dll.ZCAN_ReadChannelStatus(chn_handle, byref(status))
            return status if ret == ZCAN_STATUS_OK else None
        except:
            print("Exception on ZCAN_ReadChannelStatus!")
            raise

    def GetReceiveNum(self, chn_handle, can_type=ZCAN_TYPE_CAN):
        try:
            return self.__dll.ZCAN_GetReceiveNum(chn_handle, can_type)
        except:
            print("Exception on ZCAN_GetReceiveNum!")
            raise

    def Transmit(self, chn_handle, std_msg, len):
        try:
            return self.__dll.ZCAN_Transmit(chn_handle, byref(std_msg), len)
        except:
            print("Exception on ZCAN_Transmit!")
            raise

    def Receive(self, chn_handle, rcv_num, wait_time=c_int(-1)):
        try:
            rcv_can_msgs = (ZCAN_Receive_Data * rcv_num)()
            ret = self.__dll.ZCAN_Receive(chn_handle, byref(rcv_can_msgs), rcv_num, wait_time)
            return rcv_can_msgs, ret
        except:
            print("Exception on ZCAN_Receive!")
            raise

    def TransmitFD(self, chn_handle, fd_msg, len):
        try:
            return self.__dll.ZCAN_TransmitFD(chn_handle, byref(fd_msg), len)
        except:
            print("Exception on ZCAN_TransmitFD!")
            raise

    def ReceiveFD(self, chn_handle, rcv_num, wait_time=c_int(-1)):
        try:
            rcv_canfd_msgs = (ZCAN_ReceiveFD_Data * rcv_num)()
            ret = self.__dll.ZCAN_ReceiveFD(chn_handle, byref(rcv_canfd_msgs), rcv_num, wait_time)
            return rcv_canfd_msgs, ret
        except:
            print("Exception on ZCAN_ReceiveFD!")
            raise

    def GetIProperty(self, device_handle):
        try:
            self.__dll.GetIProperty.restype = POINTER(IProperty)
            return self.__dll.GetIProperty(device_handle)
        except:
            print("Exception on ZCAN_GetIProperty!")
            raise

    def SetValue(self, iproperty, path, value):
        try:
            func = CFUNCTYPE(c_uint, c_char_p, c_char_p)(iproperty.contents.SetValue)
            return func(c_char_p(path.encode("utf-8")), c_char_p(value.encode("utf-8")))
        except:
            print("Exception on IProperty SetValue")
            raise

    def GetValue(self, iproperty, path):
        try:
            func = CFUNCTYPE(c_char_p, c_char_p)(iproperty.contents.GetValue)
            return func(c_char_p(path.encode))
        except:
            print("Exception on IProperty GetValue")
            raise

    def ReleaseIProperty(self, iproperty):
        try:
            return self.__dll.ReleaseIProperty(iproperty)
        except:
            print("Exception on ZCAN_ReleaseIProperty!")
            raise


###############################################################################
'''
USBCANFD-MINI Demo
'''
def transmit_can(chn_handle, stdorext, id, data, len):
    transmit_num = 1
    msgs = (ZCAN_Transmit_Data * transmit_num)()
    for i in range(transmit_num):
        msgs[i].transmit_type = 0 #Send Self
        msgs[i].frame.eff     = 0
        if stdorext:
            msgs[i].frame.eff = 1 #extern frame
        msgs[i].frame.rtr     = 0 #remote frame
        msgs[i].frame.can_id  = id
        msgs[i].frame.can_dlc = len
        for j in range(msgs[i].frame.can_dlc):
            msgs[i].frame.data[j] = data[j]
    ret = zcanlib.Transmit(chn_handle, msgs, transmit_num)
    # print("Tranmit Num: %d." % ret)

def can_start(zcanlib, device_handle, chn):
    ip = zcanlib.GetIProperty(device_handle)
    ret = zcanlib.SetValue(ip, str(chn) + "/clock", "60000000")
    if ret != ZCAN_STATUS_OK:
        print("Set CH%d CANFD clock failed!" % (chn))
    ret = zcanlib.SetValue(ip, str(chn) + "/canfd_standard", "0")
    if ret != ZCAN_STATUS_OK:
        print("Set CH%d CANFD standard failed!" % (chn))
    ret = zcanlib.SetValue(ip, str(chn) + "/initenal_resistance", "1")
    if ret != ZCAN_STATUS_OK:
        print("Open CH%d resistance failed!" % (chn))
    zcanlib.ReleaseIProperty(ip)

    chn_init_cfg = ZCAN_CHANNEL_INIT_CONFIG()
    chn_init_cfg.can_type = ZCAN_TYPE_CANFD
    chn_init_cfg.config.canfd.abit_timing = 104286  # 1Mbps
    chn_init_cfg.config.canfd.dbit_timing = 4260362  # 1Mbps  101166
    chn_init_cfg.config.canfd.mode = 0
    chn_handle = zcanlib.InitCAN(device_handle, chn, chn_init_cfg)
    if chn_handle is None:
        return None
    zcanlib.StartCAN(chn_handle)
    return chn_handle


can_signal = {"gear":  # 挡位
                    {"P": [0x301, [0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]],
                     "R": [0x301, [0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]],
                     "N": [0x301, [0x00, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]],
                     "D": [0x301, [0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]],
                     "M": [0x301, [0x00, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]]},
              "handbrake":  # 手刹
                    {"ON": [0x4F6, [0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]],
                     "OFF": [0x4F6, [0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]]},
              "speed":  # 车速
                    {"50": [0x2C0, [0x00, 0x03, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00]],
                     "0": [0x2C0, [0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]]},
              "power":  # 电源挡位
                    {"ON": [0x2EA, [0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]],
                     "ACC": [0x2EA, [0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]],
                     "OFF": [0x2EA, [0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]],
                     "READY": [0x2EA, [0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]]},
              "vehicle_mode":  # 车辆模式    这个状态也是ON挡位状态
                    {"FACTORY": [0x2EA, [0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]],
                     "FACTORY_PAUSE": [0x2EA, [0x2A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]],
                     "NORMAL": [0x2EA, [0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]],
                     "TRANSPORT": [0x2EA, [0x0A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]],
                     "TRANSPORT_PAUSE": [0x2EA, [0x34, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]]},
              "tbox_status":   # tbox诊断状态
                    {"NOTREAD": [0x520, [0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0x00, 0x00]],
                     "READ": [0x520, [0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00]]
                     },
              "obd_status":
                    {"ON": [],
                     "OFF": []}
              }



# if __name__ == "__main__":

zcanlib = ZCAN()

def open_devices():
    # 设备型号  索引
    global handle
    for i in range(3):
        handle = zcanlib.OpenDevice(ZCAN_USBCANFD_200U, 0, 0)
        if handle != INVALID_DEVICE_HANDLE:
            break
        time.sleep(1)
    if handle == INVALID_DEVICE_HANDLE:
        logger.info("连接周立功设备失败！")
        return False
    # Start CAN
    chn_handle = can_start(zcanlib, handle, 0)  # access 通道0
    chn_handle1 = can_start(zcanlib, handle, 1)  # access 通道1
    if chn_handle or chn_handle1:
        logger.info("周立功初始化成功！")
        return {0: [handle, chn_handle], 1: [handle, chn_handle1]}
    else:
        logger.info("周立功初始化失败！")
        return False

# Send CAN Messages
def send_can(datalist, canid, chn_handle):
    transmit_num = 1
    msgs = (ZCAN_Transmit_Data * transmit_num)()

    for i in range(transmit_num):
        msgs[i].transmit_type = 0  # 发送方式，0=正常发送，1=单次发送，2=自发自收，3=单次自发自收。
        msgs[i].frame.eff = 0  # extern frame
        msgs[i].frame.rtr = 0  # remote frame
        msgs[i].frame.can_id = canid  # 0x72a
        msgs[i].frame.can_dlc = len(datalist)
        for j in range(msgs[i].frame.can_dlc):
            msgs[i].frame.data[j] = datalist[j]
    ret = zcanlib.Transmit(chn_handle, msgs, transmit_num)
    time.sleep(0.1)
    # print("已发送报文数量: %d." % ret)
    # logger.info("已发送报文数量: %d." % ret)

    # time.sleep(0.02)
    # # 添加流控帧
    # lk_list = [0x30, 0x00, 0x0A, 0x00, 0x00, 0x00, 0x00, 0x00]
    # msgs = (ZCAN_Transmit_Data * transmit_num)()
    #
    # for i in range(transmit_num):
    #     msgs[i].transmit_type = 0  # 发送方式，0=正常发送，1=单次发送，2=自发自收，3=单次自发自收。
    #     msgs[i].frame.eff = 0  # extern frame
    #     msgs[i].frame.rtr = 0  # remote frame
    #     msgs[i].frame.can_id = canid  # 0x72a
    #     msgs[i].frame.can_dlc = len(lk_list)
    #     for j in range(msgs[i].frame.can_dlc):
    #         msgs[i].frame.data[j] = lk_list[j]
    # zcanlib.Transmit(chn_handle, msgs, transmit_num)


def send_canfd(datalist, canid, chn_handle):
    # Send CANFD Messages
    sendlist = [0x03, 0x22, 0xf1, 0x89, 0, 0, 0, 0]
    transmit_canfd_num = 1
    canfd_msgs = (ZCAN_TransmitFD_Data * transmit_canfd_num)()

    for i in range(transmit_canfd_num):
        canfd_msgs[i].transmit_type = 0  # 发送方式，0=正常发送，1=单次发送，2=自发自收，3=单次自发自收。
        canfd_msgs[i].frame.eff = 0  # extern frame
        canfd_msgs[i].frame.rtr = 0  # remote frame
        canfd_msgs[i].frame.brs = 1  # BRS
        canfd_msgs[i].frame.can_id = canid  # 0x72a
        canfd_msgs[i].frame.len = len(datalist)
        for j in range(canfd_msgs[i].frame.len):
            canfd_msgs[i].frame.data[j] = datalist[j]
    ret = zcanlib.TransmitFD(chn_handle, canfd_msgs, transmit_canfd_num)
    print("已发送报文数量: %d." % ret)

def Receive_msg(chn_handle, access, file_path):
    f = open(file_path, "a+")
    global exit_flag
    while threading.current_thread().name not in exit_flag:
        rcv_num = zcanlib.GetReceiveNum(chn_handle, ZCAN_TYPE_CAN)
        rcv_canfd_num = zcanlib.GetReceiveNum(chn_handle, ZCAN_TYPE_CANFD)
        if rcv_num:
            rcv_msg, rcv_num = zcanlib.Receive(chn_handle, rcv_num)
            for i in range(rcv_num):
                f.write("timestamps:%.4fs, access:%s, type:CAN, %s ,id:%s, len:%d, data:%s\n" % (
                    rcv_msg[i].timestamp / 1000000,
                    access,
                    "tx" if (rcv_msg[i].frame.__pad & 0x20) else "rx",
                    hex(rcv_msg[i].frame.can_id), rcv_msg[i].frame.can_dlc,
                    ''.join(hex(rcv_msg[i].frame.data[j]) + ' ' for j in range(rcv_msg[i].frame.can_dlc))))

        elif rcv_canfd_num:
            rcv_canfd_msgs, rcv_canfd_num = zcanlib.ReceiveFD(chn_handle, rcv_canfd_num)
            for i in range(rcv_canfd_num):
                f.write("timestamp:%6fs, access:%s, type:canfd, %s ,id:%s, len:%d, data:%s\n" % (
                    rcv_canfd_msgs[i].timestamp / 1000000,
                    access,
                    "tx" if (rcv_canfd_msgs[i].frame.__pad & 0x8) else "rx",
                    hex(rcv_canfd_msgs[i].frame.can_id), rcv_canfd_msgs[i].frame.len,
                    ''.join(hex(rcv_canfd_msgs[i].frame.data[j]) + ' ' for j in range(rcv_canfd_msgs[i].frame.len))))
    f.close()
    logger.info(f"{threading.current_thread().name}-录制报文线程终止！")



def close_zlg(handle, chn_handle):
    logger.info("周立功连接关闭！")
    zcanlib.ResetCAN(chn_handle)
    zcanlib.CloseDevice(handle)



# 定义全局事件对象
exit_flag = []
def zlg_task(signal, chn_handle):
    global exit_flag
    while threading.current_thread().name not in exit_flag:  # 检查事件是否被设置
        for key, value in signal.items():
            tx = can_signal[key][value][0]
            send_signal = can_signal[key][value][1]
            send_can(datalist=send_signal, canid=tx, chn_handle=chn_handle)
    logger.info(f"{threading.current_thread().name}-线程终止！")


def start_can(signal, chn_handle):
    zlg_task_name = "ZLG%s" %time.time()
    threading.Thread(target=zlg_task, args=(signal, chn_handle,), name=zlg_task_name).start()
    return zlg_task_name

def stop_can(task_name=None):
    global exit_flag
    if task_name:
        exit_flag.append(task_name)
        logger.info(f"周立功{task_name}线程发起结束标志！")



def start_receive(chn_handle, access, file_path):
    with open(file_path, "w") as f:
        f.close()
    zlg_task_name = "ZLG%s" % time.time()
    threading.Thread(target=Receive_msg, args=(chn_handle, access, file_path), name=zlg_task_name).start()
    return zlg_task_name



# devices = open_devices()
#
# if devices:
#     handle, chn_handle = devices[0]
#     Receive_msg(chn_handle=chn_handle, access=0, file_path=ZLGLOG_PATH)





# d, dd = open_devices()
# start_can(signal={"handbrake": "ON"}, chn_handle=dd)
# start_can(signal={"gear": "R"}, chn_handle=dd)

# time.sleep(10)
# stop_can()
# close_zlg(d, dd)
# d, dd = open_devices()
# start_can(signal={"power": "OFF"}, chn_handle=dd)
# time.sleep(100)


