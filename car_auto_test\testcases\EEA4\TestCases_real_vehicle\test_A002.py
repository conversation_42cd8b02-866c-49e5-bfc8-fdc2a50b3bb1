# coding: utf-8
# Project：car_auto_test
# File：test_A002.py
# Author：杨郑健
# Date ：2025/6/10 14:00
from testcases.EEA4.PageObjects.TestObject import Step_object
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from utils.LoggingSystem.Logger import logger
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.ReportGenerator.Allure import *

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("监听电源挡位以及tbox诊断状态：电源ACC挡")
@static_condition(conditions=
{
        "power": "ACC",
        "tbox_status": "READ",
        "task": "normal"
}
)
def test_A002(driver, launch_app):
    """
    测试步骤
    1、切换电源挡位至acc挡
    2、进入工程模式点击任务检测按钮
    3、任务检测结束后寻找acc挡无法拿到任务的状态（power : PWR_ACC 、tel-diagnose: TEL_DIAGNOSE_READ）

    预期结果
    3、电源acc档无法发起检测，监听电源档位及tbox诊断状态，实时日志中有电源档位状态power:acc，tbox诊断状态tel_diagnose_read状态输出
    """
    objects = Step_object(driver)
    get_task_result = objects.get_task()
    results = otaLogAnalysis.find_keywords(["power : PWR_ACC", "tel-diagnose: TEL_DIAGNOSE_READ"])
    logger.info(f"实时日志检测结果:{results}")
    assert results
    assert not get_task_result