# coding: utf-8
# Project：car_auto_test
# File：test_C023.py
# Author：杨郑健
# Date ：2025/6/11 18:31
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级主页面/小红点")
@AllureDecorators.title("埋点数据：免责页面显示")
@static_condition(conditions=
{
    "power": "ON",
    "tbox_status": "READ",
    "task": "normal"
}
)
def test_C023(driver, launch_app):
    """
    测试步骤
    1、替换发行说明检测到升级任务并下载完成
    2、检查点击预约升级后的注意事项里点击继续升级按钮，查看后台埋点

    预期结果
    2、（code20）“免责页面显示”
    """
    objects = Step_object(driver)
    objects.get_task()
    objects.matter_click()
    burying_points = Cloud("奇瑞汽车").get_burying_point()
    result = False
    for burying_point in burying_points:
        if burying_point["msg"] == '(code 20) 免责页面显示':
            result = True
    assert result