'''
工程模式密码生成
'''
import datetime
from config.api_config import APIConfig
from config.Config import SWITCH,VIN
from utils.LoggingSystem.Logger import logger

class EngineeringPassword:
    def __init__(self, now=True):
        if SWITCH:
            self.vin = APIConfig.test_data_store.get("vin","")
            logger.info(self.vin)

        else:
            self.vin = VIN
        self.wv = 123456  # 固定值
        self.now = now


    def validate_input(self, input_str):
        """验证输入，只保留数字"""
        return ''.join(filter(str.isdigit, input_str))

    def format_date(self, date):
        """格式化日期为YYMMDD格式"""
        year = date.year % 100  # 获取年份的最后两位
        month = date.month
        day = date.day
        return f"{year:02d}{month:02d}{day:02d}"

    def extract_last_sixdigits(self, vin):
        """提取VIN码的最后6位数字"""
        vin_num_str = vin[-6:]  # 获取字符串的后六位
        digits = ''.join(filter(str.isdigit, vin_num_str))
        return digits

    def generate_password(self, wv, vin, date):
        """生成工程密码"""
        vin = self.validate_input(vin)
        date = self.format_date(date)
        vin_num = int(self.extract_last_sixdigits(vin))
        date_num = int(date)
        result = (wv + (0 if vin_num == '' else int(vin_num))) * date_num
        return str(result % 1000000).zfill(6)

    def get_password(self):
        """获取当前日期的工程密码"""
        try:
            date = datetime.datetime.now()
            if self.now:
                password = self.generate_password(self.wv, self.vin, date)
            else:
                yesterday = date - datetime.timedelta(days=1)
                password = self.generate_password(self.wv, self.vin, yesterday)
            logger.info(f"生成的工程密码: {password}")
            return password
        except Exception as e:
            logger.error(f"生成工程密码失败: {str(e)}")
            return None

#
# vehile_password = EngineeringPassword().get_password()


