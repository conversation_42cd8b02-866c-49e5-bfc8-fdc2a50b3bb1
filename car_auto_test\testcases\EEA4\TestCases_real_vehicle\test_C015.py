# coding: utf-8
# Project：car_auto_test
# File：test_C015.py
# Author：杨郑健
# Date ：2025/6/11 18:12
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级主页面/小红点")
@AllureDecorators.title("注意事项：立即升级触发")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_C015(driver, launch_app):
    """
    测试步骤
    1、替换发行说明检测到升级任务并下载完成
    2、检查点击立即升级后车端显示注意事项弹框

    预期结果
    2、弹框“注意事项”
    """
    objects = Step_object(driver)
    objects.get_task()
    result = objects.nowupdate_click()
    assert result