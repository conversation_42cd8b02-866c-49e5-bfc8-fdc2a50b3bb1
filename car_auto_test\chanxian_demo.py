import json

import requests
def request_update():
    # print('即将推送产线任务ID:%s' % self.up_id)
    url = "https://api-fota-uat.mychery.com:8443/tsp/ota/tsp/v0/push/task"
    headers = {
        'Content-Type': 'application/json',
        # 'Cookie': 'cookiesession1=678B287E3B77E5F9B0870C3B64818EC6'
        'Cookie': '_csrf=-HIZagM7kqdkT236kuOiVbqK; _s=s%3AF6LzIf7kcuFDLNXff2lSy--D05VHjDUb.9YU4kdd5X6vVW63c7IlvrdtVwyAcrJv%2FJpqtjIuJKUI'
    }
    data = {
        "task_id": 58832,
        "vins": ["LVTDB24B7RG024947"],
        "batch_id": 49351
    }
    print(data)
    response = requests.post(url, data=json.dumps(data), headers=headers)
    print(response)

request_update()