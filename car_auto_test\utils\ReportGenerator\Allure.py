import allure

import logging
import os
from datetime import datetime
from selenium import webdriver
from utils.LoggingSystem.Logger import logger
from config.Config import SCREENSHOT_PATH, OTALOG_PATH,ZLGLOG_PATH
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.AdbPort.Adb_Port import AdbExec
from time import time

# --------------------------- Allure 装饰器封装 ---------------------------
class AllureDecorators:
    """   封装 Allure 核心装饰器（功能、故事、标题、严重级别）  """

    @staticmethod
    def epic(epic_name: str):
        """   标记测试用例所属的主要模块     1级标题   """
        return allure.feature(epic_name)

    @staticmethod
    def feature(feature_name: str):
        """   标记测试用例所属的功能模块     2级标题   """
        return allure.feature(feature_name)

    @staticmethod
    def story(story_name: str):
        """   标记测试用例所属的子功能      3级标题   """
        return allure.story(story_name)

    @staticmethod
    def title(test_title: str):
        """   设置测试用例的显示标题   """
        return allure.title(test_title)

    @staticmethod
    def step(step_name: str):
        """   设置测试用例的步骤显示或断言处理     """
        return allure.title(step_name)

    @staticmethod
    def severity(severity_level: str):
        """   设置测试用例的严重级别（L1/L2/L3等）   """
        return allure.severity(severity_level)

    @staticmethod
    def description(description_name: str):
        """   设置测试用例的备注说明     """
        return allure.title(description_name)

# --------------------------- Allure 步骤与日志封装 ---------------------------
class AllureSteps:
    """封装测试步骤记录（结合日志与 Allure step） """
    @staticmethod
    def log_step(step_desc: str):
        """装饰器：记录测试步骤并打印日志   step_desc: 步骤描述（显示在 Allure 报告中）  """
        def decorator(func):
            def wrapper(*args, **kwargs):
                logger.info(f"[STEP] {step_desc}")  # 记录日志
                with allure.step(step_desc):  # Allure 步骤上下文
                    return func(*args, **kwargs)
            return wrapper
        return decorator


# --------------------------- Allure 附件封装（日志/截图）---------------------------
class AllureAttachments:
    """   封装附件添加功能（日志文件、失败截图）  """
    screenshot_dir = SCREENSHOT_PATH  # 截图保存目录
    log_path = OTALOG_PATH + "/carota"

    @classmethod
    def find_otalog_path(cls):
        adb = "adb shell find %s -name carota"
        res = os.popen(adb % "/data/data").read()
        if not res:
            res = os.popen(adb % "/sdcard").read()
        return res.strip()

    @classmethod
    def setup_directories(cls):
        """   创建日志和截图目录（初始化时调用）  """
        os.makedirs(cls.screenshot_dir, exist_ok=True)

    @staticmethod
    def attach_log_file(attachment_name: str):
        """   将日志文件作为附件添加到Allure报告   attachment_name: 附件显示名称  """
        file_data = otaLogAnalysis.open_file()
        logger.info(file_data)
        if file_data:
            file_data = file_data.read()
            allure.attach(file_data, name=attachment_name, attachment_type=allure.attachment_type.TEXT)
        else:
            allure.attach("获取OTA LOG出错", name=attachment_name, attachment_type=allure.attachment_type.TEXT)
        logger.info(f"OTA LOG已保存：{OTALOG_PATH}")

    @staticmethod
    def attach_zlglog_file(attachment_name: str):
        """   将周立功日志文件作为附件添加到Allure报告   attachment_name: 附件显示名称  """
        try:
            zlg_file_path = f"{ZLGLOG_PATH}/{attachment_name}.txt"
            if os.path.exists(zlg_file_path):
                with open(zlg_file_path, "r", encoding='utf-8') as f:
                    file_data = f.read()
                allure.attach(file_data, name=attachment_name + "-ZLG_LOG", attachment_type=allure.attachment_type.TEXT)
                logger.info(f"周立功 LOG已保存：{ZLGLOG_PATH}")
            else:
                logger.warning(f"周立功日志文件不存在：{zlg_file_path}")
                allure.attach("周立功日志文件不存在", name=attachment_name + "-ZLG_LOG", attachment_type=allure.attachment_type.TEXT)
        except Exception as e:
            logger.error(f"读取周立功日志文件失败：{str(e)}")
            allure.attach(f"读取周立功日志文件失败：{str(e)}", name=attachment_name + "-ZLG_LOG", attachment_type=allure.attachment_type.TEXT)

    @staticmethod
    def attach_screenshot(screenshot_name: str):
        """   将截图作为附件添加到Allure报告实例  """
        os.makedirs(AllureAttachments.screenshot_dir, exist_ok=True)
        screenshot_path = f"{AllureAttachments.screenshot_dir}/{screenshot_name}.png"
        try:
            AdbExec().screen_capture(screenshot_path)  # 保存截图
            allure.attach.file(screenshot_path, name=screenshot_name)
            logger.info(f"截图已保存：{screenshot_path}")
        except Exception as e:
            logger.error(f"截图失败：{str(e)}")


