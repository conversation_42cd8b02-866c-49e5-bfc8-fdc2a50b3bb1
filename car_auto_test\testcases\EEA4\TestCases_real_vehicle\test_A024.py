# coding: utf-8
# Project：car_auto_test
# File：test_A024.py
# Author：杨郑健
# Date ：2025/6/10 16:43
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from time import sleep
import pytest

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("任务解析")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_A024(driver, launch_app):
    """
    测试步骤
    1、电源挡位切换为READY
    2、任务检测结束后检查拿到任务的状态
    3、云端查看活动日志中是否有 升级包版本、任务名称、包名md5，版本

    预期结果
    3、可查询到本次任务名称、升级包版本、升级包哈希值、升级包大小、升级包下载地址、前置条件信息、任务模式、任务说明、升级时间，
    目前仅拿固定几个必定存在的值，待优化
    """
    objects = Step_object(driver)
    get_task_result = objects.get_task()
    info = Cloud("奇瑞汽车").get_carinfo()
    for i in range(len(info)):
        if info[i]["message"] == "7.检测升级返回" and "data" in info[i]:
            assert info[i]["data"]["result"]["releaseNote"]      # 检测升级任务名称
            assert info[i]["data"]["result"]["displayInfoUrl"]   # 检测升级包下载地址
            assert info[i]["data"]["result"]["ecus"][0]["hv"]    # 检测版本
            assert info[i]["data"]["result"]["ecus"][0]["smd5"]  # 包名md5

    assert get_task_result