[pytest]
addopts = -s -vvv --disable-pytest-warnings
testpaths =
; testcases/EEA4/TestChanxian/
;    testcases/EEA5/TestCases/UI/test_bench_01_normal_process.py
;    testcases/EEA5/TestCases/UI/test_bench_02_first_predition.py
;    testcases/EEA5/TestCases/UI/test_03_reservation.py
;    testcases/EEA5/TestCases/UI/test_bench_04_rescue.py






     testcases/EEA4/UICases/test_01_before_update_process.py
   testcases/EEA4/UICases/test_02_preditions.py
   testcases/EEA4/UICases/test_03_reservation.py
   testcases/EEA4/UICases/test_04_update_result.py
;   testcases/EEA4/UICases/test_05_task_time_fail.py







python_files = test_*.py
python_classes  = TEST*
python_functions = test
appname = com.example.app
log_cli_format = %(asctime)s - %(levelname)s - %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S
;markers =
;	smokeð̲
;    usermanageûģ