"""
数据库初始化 writer by z<PERSON><PERSON><PERSON><PERSON>.yang
"""
import sqlite3
from config.Config import SQLITE_PATH
from utils.LoggingSystem.Logger import logger




class DbInit:
    def __init__(self):
        self.testDb = SQLITE_PATH + r"\CookieDB.db"   # 这里定义一个DB文件，每次使用前都会先初始化Db,确保db存在
        self.verification = SQLITE_PATH + "./VerificationDB.db"

    def openDb(self, dbPath):
        conn = sqlite3.connect(database=dbPath, )
        cursor = conn.cursor()
        return conn, cursor

    def closeDb(self, conn, cursor):
        cursor.close()
        conn.close()

    def createTable(self, cursor, table_name, columns):
        """
        :param table_name:  要创建的表名
        :param columns:  要创建的字段名 要符合SQL语法
        :return:
        """
        try:
            cursor.execute(
                f"CREATE TABLE IF NOT EXISTS {table_name} ({columns})"
            )
            return True
        except Exception as e:
            logger.info(f'创建数据表出现错误, 错误信息: {e}')
            return False

    def initDb(self, ):
        # Test
        userConn, userCursor = self.openDb(self.testDb)
        self.createTable(userCursor, 'Cookies', 'cookie varchar(255), time varchar(255)')  # 创建一个表
        self.closeDb(userConn, userCursor)

        userConn, userCursor = self.openDb(self.verification)
        self.createTable(userCursor, 'verification', 'key varchar(255), value varchar(255)')  # 创建一个表
        self.closeDb(userConn, userCursor)
        logger.info('数据库初始化成功！！！')


# DbInit().initDb()


