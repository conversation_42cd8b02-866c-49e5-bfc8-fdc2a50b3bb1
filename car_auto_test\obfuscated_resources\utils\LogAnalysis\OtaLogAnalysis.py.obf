# coding: utf-8
# Project：car_auto_test
# File：OtaLogAnalysis.py
# Author：杨郑健
# Date ：2025/4/11 14:38

import re
import os
from datetime import datetime
from utils.AdbPort.Adb_Port import AdbExec
from config.Config import OTALOG_PATH
import shutil
from time import sleep

class OtaLogAnalysis(AdbExec):
    def __init__(self):
        self.root()
        carota_path = OTALOG_PATH + r"\carota"
        if os.path.exists(carota_path):
            shutil.rmtree(carota_path)
            os.mkdir(carota_path)
        self.otalogpath = self.find_otalog_path()

    def parse_custom_date(self, date_str):
        # 拆分字符串：'2025-0410-1759-45' → ['2025', '0410', '1759', '45']
        parts = date_str.split('-')
        year = int(parts[0])
        month = int(parts[1][:2])  # '0410' → '04' → 4
        day = int(parts[1][2:])  # '0410' → '10' → 10
        hour = int(parts[2][:2])  # '1759' → '17' → 17
        minute = int(parts[2][2:])  # '1759' → '59' → 59
        second = int(parts[3])  # '45' → 45

        return datetime(year, month, day, hour, minute, second)

    def open_file(self):
        # 首先寻找最新的日志文件
        self.root()
        self.pull_file(self.otalogpath, OTALOG_PATH)
        for root, dirs, files in os.walk(OTALOG_PATH + r"\carota"):
            filter_list = []
            for file in files:
                filter_list.append(file.split("]")[0].split("[")[1][3:])
            # 转换所有日期为 datetime 对象
            valid_dates_dt = [self.parse_custom_date(date_str) for date_str in filter_list]
            # 计算最近的日期
            now = datetime.now()
            closest_date = min(valid_dates_dt, key=lambda x: abs(x - now))
            curr_file = os.path.join(root, files[valid_dates_dt.index(closest_date)]) # 索引最近日期的日志文件，避免拿错
            file_opject = open(curr_file, "r", encoding="utf-8")
            return file_opject

    def find_keywords(self, keywords: list):
        re_filter = f".*%s.*"
        file_data = self.open_file().read()
        result_list = []
        for i in keywords:
            keyword = re_filter % i
            try:
                result_list.append(re.findall(keyword, file_data)[-1])  #寻找最新结果
            except:
                return False
        return result_list

    def find_keywords_with_timeout(self, keywords: list, timeout):
        re_filter = f".*%s.*"
        result_list = []
        num = 0
        while True:
            if num == timeout:
                return False
            file_data = self.open_file().read()
            for i in keywords:
                keyword = re_filter % i
                try:
                    result_list.append(re.findall(keyword, file_data)[-1])  #寻找最新结果
                except:
                    pass
            sleep(1)
            num += 1
            # print(len(result_list), len(keywords))
            if len(result_list) == len(keywords):
                return result_list

    def find_otalog_path(self):
        adb = "adb shell find %s -name carota"
        res = os.popen(adb % "/data/data").read()
        if not res:
            res = os.popen(adb % "/sdcard").read()
        return res.strip()

otaLogAnalysis = OtaLogAnalysis()

if __name__ == "__main__":
    print(otaLogAnalysis.find_keywords(["power"]))
    # print(otaLogAnalysis.find_keywords(["power : PWR_"]))
    # print(otaLogAnalysis.open_file())
    # print(otaLogAnalysis.find_keywords_with_timeout(["TEL_DIAGNOSE_NOT_READ"], 10))