'''
预约升级类
'''

import time
import pytest
import functools
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.ImageRecognition.Image_comparison import *
# from utils.SerialTool.serial_tool import serial_tool
from utils.AdbPort.Adb_Port import adb
from utils.SerialTool.actuator import act


class Test_Bench_Reservation:
    mode_value: int = None
    mode_dict: dict = None
    vehicle_flag: int = None
    language: str = None

    # 使用mode fixture获取当前的模式值
    @pytest.fixture(autouse=True)
    def setup_ui_mode(self, mode, mode_dict, vehicle_flag, language):
        """设置当前的UI模式和mode_dict以及车型标志位"""
        self.mode_value = mode
        self.mode_dict = mode_dict
        self.vehicle_flag = vehicle_flag
        self.language = language
        logger.info(f"当前测试环境: 模式值={self.mode_value}, 车型标志位={self.vehicle_flag}")

    # @pytest.mark.skip
    def test_reservation_001(self, driver,ui_eea4_app):
        """
        预约设置正向流程
        """
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        # objects.get_task()
        logger.info('------------------ 开始制造:预约正常流程---------------------')
        act.power_on()
        act.set_file('speed', 0)
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        # driver.app_start(FOUR_APP_PACKAGE)
        time.sleep(3)

        # 预约时间设置
        objects.tomorrow_time_set_pass(take_screenshot=True)
        # objects.tomorrow_time_set_next_pass(take_screenshot=True)

        # 取消预约
        objects.tomorrow_time_set_cancel()
        time.sleep(3)

    @pytest.mark.skip
    def test_reservation_002(self, driver):
        """
        预约设置失败
        """
        logger.info('------------------ 开始制造:预约设置失败---------------------')
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        # objects.get_task(take_screenshot=True)
        adb.disable_network(FOUR_APP_PACKAGE)
        # 设置预约升级
        objects.tomorrow_time_set_fail()
        adb.enable_network()
        time.sleep(10)

    # @pytest.mark.skip
    def test_reservation_003(self, driver):
        '''
        预约时间到、车况不满足- 充电
        Args:
            driver:

        Returns:
        '''
        logger.info('------------------ 开始制造:预约时间到、车况不满足- 充电--------------------')
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        # driver.app_start(FOUR_APP_PACKAGE)
        time.sleep(3)
        # objects.get_task(take_screenshot=False)
        act.set_apk_file('charge_state', 2)
        act.set_file('power_gear', 0,'vehicle_lock_door_status',0)
        # 车辆上电 防止休眠
        act.power_on()
        objects.tomorrow_predition_fail_t1gc(self.language)
        objects.screen_image('预约时间到、车况不满足- 充电', self.mode_value)
        objects.click_element('schedule_predition_fail_btn')
        act.set_apk_file('tel_diagnose_state', 0)

    # @pytest.mark.skip
    def test_reservation_004(self, driver):
        '''
        预约时间到、车况不满足- 电源
        Args:
            driver:

        Returns:

        '''

        logger.info('------------------ 开始制造: 预约时间到、车况不满足- 电源非OFF挡---------------------')
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        # driver.app_start(FOUR_APP_PACKAGE)
        time.sleep(3)
        act.set_file('speed', 0,'vehicle_lock_door_status',0)
        # 车辆上电 防止休眠
        act.power_on()
        objects.tomorrow_predition_fail_t1gc(self.language)
        objects.screen_image('预约时间到、车况不满足- 电源', self.mode_value)
        objects.click_element('schedule_predition_fail_btn')

    # @pytest.mark.skip
    def test_reservation_005(self, driver):
        '''
        预约时间到、车况不满足- 设防
        Args:
            driver:

        Returns:

        '''
        logger.info('------------------ 开始制造: 预约时间到、车况不满足- 设防---------------------')
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        # driver.app_start(FOUR_APP_PACKAGE)
        time.sleep(3)
        act.set_apk_file('lock_state', -1)
        act.set_file('power_gear', 0)
        # 车辆上电 防止休眠
        act.power_on()
        objects.tomorrow_predition_fail_t1gc(self.language)
        objects.screen_image('预约时间到、车况不满足- 设防', self.mode_value)
        objects.click_element('schedule_predition_fail_btn')
        act.set_apk_file('tel_diagnose_state', 0)

    # @pytest.mark.skip
    def test_reservation_006(self, driver):
        '''
        预约时间到 、请勿在红绿灯界面
        Args:
            driver:

        Returns:
        '''

        logger.info('------------------ 开始制造:  预约时间到 、请勿在红绿灯界面---------------------')
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        # driver.app_start(FOUR_APP_PACKAGE)
        time.sleep(3)
        # act.set_apk_file('lock_state', 0)
        act.set_file('power_gear', 0,'vehicle_lock_door_status',0)
        objects.tomorrow_time_come(self.language)
        act.set_file('vehicle_speed', 0)
