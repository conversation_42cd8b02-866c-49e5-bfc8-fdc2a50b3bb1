# coding: utf-8
# Project：car_auto_test
# File：test_E066.py
# Author：杨郑健
# Date ：2025/6/20 15:32
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("埋点数据")
@AllureDecorators.title("您预约的升级时间已到:页面显示")
def test_E066(driver, launch_app):
    """
    测试步骤
    1、“预约时间设置”设置预约升级，车辆下电设防休眠
    2、待预约时间到，解防开左前门，进入“您预约的系统升级时间已到”，查看后台埋点数据

    预期结果
    2、（code70）“预约升级时间到界面显示”
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        "tbox_status": "NOTREAD",
        "power": ["READY", "OFF"],
        "task": "normal",
        "gear": "P",
        "handbrake": "ON"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'READY':
            objects.get_task()
            logger.info("READY挡验证完成")
            assert True
        elif current_state == 'OFF':
            logger.info("OFF挡预约升级，查看埋点数据")
            objects.tomorrow_time_set_next_pass()
            objects.tomorrow_time_set()
            serial_tool.set_file("vehicle_lock_door_status", 0, "anti_theft_status", 0)
            burying_points = Cloud("奇瑞汽车").get_burying_point()
            result = False
            for burying_point in burying_points:
                if burying_point["msg"] == '(code 82) 预约升级时间到，条件不满足"确认"':
                    result = True
            assert result
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()