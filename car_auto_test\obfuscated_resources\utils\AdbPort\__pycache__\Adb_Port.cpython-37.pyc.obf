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