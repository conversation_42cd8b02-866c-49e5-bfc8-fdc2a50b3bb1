# coding: utf-8
# Project：car_auto_test
# File：test_B009.py
# Author：杨郑健
# Date ：2025/6/11 14:10
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级包下载模块")
@AllureDecorators.title("新版本提示框:点击查看外区域")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_B009(driver, launch_app):
    """
    测试步骤
    1、车辆解防，切换电源至on档，待车端检测到升级任务并下载完成，切换电源至off档，触发新版本提示框
    2、点击新版本提示框除“查看”外弹框内其他区域
    3、点击新版本提示框弹框外其他区域

    预期结果
    2、弹框不会关闭
    3、弹框不会关闭
    """
    pass