'''
步骤封装文件
'''

import time
from gzip import FTEXT
from onnxruntime import package_name
from threading import Thread

from core.BasePage.Base_page import BasePage
from config.Config import *
from utils.AdbPort.Time_Object import TimeObject
from utils.SerialTool.vcc_tool import vcctool, vcc
from .Elements import elements
from utils.LoggingSystem.Logger import logger
from testcases.EEA5.PageObjects.EngineeringPassword import EngineeringPassword
from utils.ImageRecognition.Image_comparison import image_recognition
import threading
import queue
global check_status


class Step_object(BasePage):
    def __init__(self, driver, mode=0, mode_dict=None) -> None:
        super().__init__(driver)
        self.mode_value = mode
        self.width, self.height = driver.window_size()
        # 设置mode_dict
        if mode_dict:
            self.set_mode_dict(mode_dict)
        else:
            logger.warning(f"初始化Step_object时未提供mode_dict，screen_image功能可能无法正常工作")



    def no_task(self):
        self.screen_image('当前已是最新版本', self.mode_value)
        self.driver.app_start('com.android.launcher3', activity='.activity.AppListActivity')
        time.sleep(3)
        self.screen_image('无升级任务任务、Home界面小红点显示', self.mode_value)
        self.driver.app_start('com.carota.chery.v5')
        time.sleep(1)

    def enter_engineering(self):
        """进入工程模式"""
        max_attempts = 5
        attempt = 0

        while attempt < max_attempts:
            attempt += 1
            logger.info(f"尝试进入工程模式 (尝试 {attempt}/{max_attempts})")
            # 每次都重新生成密码

            vehile_password = EngineeringPassword().generate()
            max_num = 20  # 设置最大尝试次数
            num = 0

            while num < max_num:
                num += 1
                # logger.info(f"点击工程模式按钮 (第{attempt}次)")
                # 点击工程模式按钮
                self.click_element("engineering_mode")
                time.sleep(0.2)
                if self.driver(resourceId='com.carota.chery.v5:id/tv_input'):
                    logger.info('工程模式密码输入框')
                    break



            logger.info(EEA5_VIN)
            logger.info(vehile_password)

            for i in str(vehile_password):
                if i == '1':
                    self.click_element('1')
                elif i == '2':
                    self.click_element('2')
                elif i == '3':
                    self.click_element('3')
                elif i == '4':
                    self.click_element('4')
                elif i == '5':
                    self.click_element('5')
                elif i == '6':
                    self.click_element('6')
                elif i == '7':
                    self.click_element('7')
                elif i == '8':
                    self.click_element('8')
                elif i == '9':
                    self.click_element('9')
                elif i == '0':
                    self.click_element('0')

            self.click_element("password_confirm")

            if self.check_element_exists('check_update',ignore=True):
                logger.info("成功进入工程模式")
                return True
            else:
                logger.warning(f'工程模式密码输入错误 (尝试 {attempt}/{max_attempts})')
                self.click_element('engineering_iv_close')

        logger.error("达到最大尝试次数，无法进入工程模式")
        return False




    def get_task(self,take_screenshot=True):
        """
        工程模式检查更新任务获取
        """
        max_attempts =5
        exit_flag = False
        for attempt in range(1, max_attempts + 1):
            logger.info(f"开始第{attempt}次尝试检测任务 (共{max_attempts}次)")

            try:
                self.enter_engineering()
                if take_screenshot:
                    self.screen_image('工程模式', self.mode_value)

                self.click_element("check_update")
                self.click_element("back_button")
                # 通过文本比对，长度比对存在偶现相同问题
                text1= self.get_element_id('engineering_mode').get_text()
                logger.info(f'text1:{text1}')
                num = 0

                while True:
                    text2 = self.get_element_id('engineering_mode').get_text()
                    logger.info(f'text2:{text2}')
                    if text1 != text2:
                        logger.info(f"第{attempt}次尝试成功检测到任务")
                        if take_screenshot:
                            self.screen_image('检测到任务升级主页面显示', self.mode_value)
                            exit_flag = True
                        break

                    if num == EEA5_max_wait_time:
                        logger.warning(f'第{attempt}次尝试：检测任务时间到({EEA5_max_wait_time}秒)，未查到任务')
                        break  # 退出while循环，进入下一次尝试

                    logger.info(f'第{attempt}次尝试：任务检测中{num}秒 / {EEA5_max_wait_time}秒')
                    time.sleep(interval)
                    num += EEA5_interval

            except Exception as e:
                logger.error(f"第{attempt}次尝试过程中出错: {str(e)}")

            print('111',exit_flag)
            if exit_flag ==False:
                # 如果不是最后一次尝试，等待一段时间再重试
                if attempt < max_attempts:
                    logger.info(f"第{attempt}次尝试失败，等待5秒后进行第{attempt + 1}次尝试")
                    time.sleep(5)  # 等待5秒再重试
            else:
                break

        self.check_element_exists('new_version_title')
        time.sleep(1)
        if take_screenshot:
            self.screen_image('发现新版本弹框', self.mode_value)
        time.sleep(5)


    def back_home(self):
        """
        返回主页面 查看小红点
        Returns:

        # """
        self.driver.app_start('com.android.launcher3',activity='.activity.AppListActivity')
        time.sleep(0.5)
        self.screen_image('检测到任务、Home界面小红点显示', self.mode_value)
        self.driver.app_start('com.carota.chery.v5')
        time.sleep(1)

    def history_version(self):
        self.click_element('history')
        time.sleep(0.5)
        self.screen_image('历史版本',self.mode_value)
        self.click_element('history_title')

    def details_click(self):
        '''
        点击详情
        Returns:

        '''

        self.click_element('details_btn')
        logger.info('点击详情')
        time.sleep(5)
        self.screen_image('详情页面', self.mode_value)
        self.click_element('details_title')

    def privacy_click(self):
        self.click_element('privacy_Statement')
        logger.info('点击隐私申明')
        time.sleep(1)
        self.screen_image('隐私协议申明截图', self.mode_value)
        self.click_element('privacy_ok')

    def nowupdate_click(self):

        self.click_element('now_btn')
        logger.info('点击立即升级')
        time.sleep(1)

    def matter_click(self,take_screenshot=True):
        """
        1、点击立即升级弹出注意事项
        2、点击继续升级
        Returns:

        """
        self.nowupdate_click()
        if take_screenshot:
            self.screen_image('注意事项', self.mode_value)
        self.click_element('matter_argee_btn')
        logger.info('点击注意事项 继续升级按钮')
        time.sleep(1)

    def disclaimer_click(self):
        """
        1、点击立即升级弹出注意事项
        2、点击继续升级
        3、免责同意
        Returns:

        """

        self.matter_click(take_screenshot=False)
        # if take_screenshot:
        #     self.screen_image('免责声明', self.mode_value)
        time.sleep(20)
        self.click_element('disclaimer_argee_btn')
        logger.info('点击免责同意按钮')

    def precautions_click(self):
        self.matter_click()
        self.screen_image('免责声明', self.mode_value)


    def tomorrow_click(self):
        self.click_element('schedule_btn')
        logger.info('点击预约升级')
        time.sleep(1)
        self.click_element('matter_argee_btn')
        logger.info('点击注意事项 继续升级按钮')
        time.sleep(20)
        self.click_element('disclaimer_argee_btn')
        logger.info('点击免责同意按钮')

    def tomorrow_time_set(self,take_screenshot=True):
        '''
        设置预约时间、默认时间 3:00
        Returns:

        '''
        self.tomorrow_click()
        self.check_element_exists('schedule_time_set_title')
        # time.sleep(0.5)
        if take_screenshot:
            self.screen_image('预约时间设置',self.mode_value)
        self.click_element('schedule_time_set_agree_btn')

    def tomorrow_time_set_next_pass(self,take_screenshot=True):
        '''
           设置预约时间、5分钟后、最近的时间
           Returns:
        '''
        max_attempts = 5
        attempt = 0

        while attempt < max_attempts:
            attempt+=1
            logger.info(f"设置最近的预约时间 (尝试 {attempt}/{max_attempts})")
            self.tomorrow_click()
            #TODO会存在点完免责没反应的情况?
            self.check_element_exists('schedule_time_set_title')
            if self.check_element_exists('disclaimer_cancel_btn',ignore=True,max_wait_time=1):
                self.click_element('disclaimer_cancel_btn')
                time.sleep(0.5)
                self.tomorrow_click()
                self.check_element_exists('schedule_time_set_title')


            time.sleep(0.5)
            if take_screenshot:
                self.screen_image('预约时间设置',self.mode_value)
            x = int(self.height * 0.396)
            y = int(self.width * 0.409)
            self.driver.click(x, y)
            self.click_element('schedule_time_set_agree_btn')
            time.sleep(0.5)
            if self.check_element_exists('schedule_time_set_pass_title'):
                self.click_element('schedule_time_set_pass_btn')
                break
            else:
                #如果vcc 崩溃了，重启，点击取消按钮
                logger.info('预约失败了，可能vcc崩溃了')
                self.click_element('schedule_setting_cancel_btn')

                vcc.reboot_master()
        if attempt == 5:
            logger.error("达到最大尝试次数，vcc崩溃")
        return False






    def tomorrow_time_set_pass(self,take_screenshot=True):
        '''
        设置预约成功
        Args:
            take_screenshot:

        Returns:

        '''
        self.tomorrow_time_set(take_screenshot==False)
        # self.check_element_exists('schedule_time_setting')
        if take_screenshot:
            self.screen_image('预约中',self.mode_value)
        self.check_element_exists('schedule_time_set_pass_title')
        time.sleep(0.5)
        if take_screenshot:
            self.screen_image('预约成功', self.mode_value)
        self.click_element('schedule_time_set_pass_btn')
        time.sleep(0.5)
        if take_screenshot:
            self.screen_image('预约成功主页面显示', self.mode_value)

    def tomorrow_time_set_fail(self):
        max_try = 5
        num = 0
        while num <max_try:
            num+=1
            logger.info(f'开始第{num}次重试')
            vcc.vcc_execute_nonblocking('/opt/ota_pkg/oem_app/tp_client --ip ************ --sourcela 0x0F00 --targetla 0x01 --data 1101 --log /opt/ota_pkg/oem_app/tp_client.log --out version.out --level 5')
            logger.info('已重启tbox')
            time.sleep(10)
            self.tomorrow_time_set(take_screenshot=False)
            try:
                len1 = len(self.get_element_id('schedule_time_set_fail_toast').get_text())
                logger.info(f'len1:{len1}')
                for _ in range(20):
                    len2 = len(self.get_element_id('schedule_time_set_fail_toast').get_text())
                    logger.info(f'len2:{len2}')
                    if len2 != len1:
                        self.screen_image('预约失败', self.mode_value)
                        logger.info('制造预约失败')
                        break
                    time.sleep(3)
                self.click_element('schedule_cancel_btn_cancel')
                break
            except Exception as e:
                # now_time = TimeObject()
                self.screen_image(f'报错截图', self.mode_value)
                logger.error(e)
                self.click_element('schedule_setting_cancel_btn')
                # 增加点击取消预约流程，存在预约成功情况
                logger.info('点击返回预约按钮，重新再试一次')




    def tomorrow_time_set_cancel(self,take_screenshot=True):
        '''
         取消预约
        Returns:

        '''
        self.click_element('schedule_cancel_btn')
        self.click_element('schedule_cancel_ask')
        if take_screenshot:
            self.screen_image('是否取消当前预约',self.mode_value)
        self.click_element('schedule_cancel_ask_agreen_btn')
        self.check_element_exists('schedule_cancelling')
        if take_screenshot:
            self.screen_image('取消预约中...',self.mode_value)
        self.check_element_exists('schedule_cancelling_title')
        if take_screenshot:
            self.screen_image('取消预约成功',self.mode_value)
        time.sleep(1)
        logger.info('点击取消预约成功按钮')
        self.click_element('schedule_cancelling_btn')
        logger.info('点击成功')

    def tomorrow_time_set_cancel_fail(self):
        #TODO增加重试
        max_try = 5
        num = 0
        while num<max_try:
            should_break_while = False
            num+=1
            logger.info(f'开始第{num}次重试')
            self.click_element('schedule_cancel_btn')
            self.click_element('schedule_cancel_ask')
            vcc.vcc_execute_nonblocking(
                '/opt/ota_pkg/oem_app/tp_client --ip ************ --sourcela 0x0F00 --targetla 0x01 --data 1101 --log /opt/ota_pkg/oem_app/tp_client.log --out version.out --level 5')
            logger.info('已重启tbox')
            time.sleep(5)
            self.click_element('schedule_cancel_ask_agreen_btn')


            for _ in range(10):
                if self.check_element_exists('schedule_cancel_fail_please_try'):
                    self.screen_image('取消预约失败toast', self.mode_value)
                    logger.info('制造取消预约失败')
                    should_break_while = True
                    break
                if self.check_element_exists('schedule_cancel_fail_please_try',max_wait_time=3):
                    self.click_element('schedule_time_set_pass_btn')
                    logger.info('想要取消预约失败，结果制造取消预约成功了，重新设置预约')
                    self.tomorrow_time_set_pass(take_screenshot=False)
                    should_break_while = False
                    break
            if should_break_while:
                break

        time.sleep(3)
        self.screen_image('取消预约失败,是否重新尝试弹框', self.mode_value)
        self.click_element('matter_cancel_btn')


    def tomorrow_predition_fail(self):
        self.tomorrow_time_set_next_pass(take_screenshot=False)
        logger.info('等待预约时间到')
        for _ in range(60):
            if self.check_element_exists('schedule_predition_fail_title'):
                break
        time.sleep(1)


    def Upgrade_charging_conflict(self):
        '''
        先设置预约升级、预约充电冲突
        '''

        self.tomorrow_time_set_pass(take_screenshot=False)
        self.driver.app_start('com.android.launcher3', activity='.activity.AppListActivity')
        time.sleep(3)
        self.driver(resourceId='com.android.launcher3:id/app_icon_with_text')[1].click()
        time.sleep(0.5)
        self.click_element('mega_charging')
        time.sleep(0.5)
        x = int(self.height * 0.576)
        y = int(self.width * 0.409)
        z = int(self.width * 0.466)
        self.driver.swipe(x, y, x, z)
        time.sleep(3)
        self.screen_image('测试查看 预约充电设置',self.mode_value)
        self.click_element('Upgrade_charging_conflict_confim')
        # if self.check_element_exists('Upgrade_charging_conflict_toast')
        time.sleep(1)
        self.screen_image('先设置预约升级、后设置预约充电冲突',self.mode_value)
        self.click_element('Upgrade_charging_conflict_cancel')

    def Upgrade_travel_conflict(self):
        '''
        先设置预约充电、预约升级冲突
        '''

        self.driver.app_start('com.android.launcher3', activity='.activity.AppListActivity')
        time.sleep(3)
        self.driver(resourceId='com.android.launcher3:id/app_icon_with_text')[1].click()
        time.sleep(0.5)
        self.click_element('mega_charging')
        time.sleep(0.5)
        x = int(self.height * 0.576)
        y = int(self.width * 0.409)
        z = int(self.width * 0.466)
        self.driver.swipe(x, y, x, z)
        time.sleep(3)
        self.screen_image('测试查看 预约充电设置', self.mode_value)
        self.click_element('Upgrade_charging_conflict_confim')
        time.sleep(1)
        # self.check_element_exists('Upgrade_charging_conflict_toast')
        # self.screen_image('先设置预约升级、后设置预约
        # 充电冲突', self.mode_value)
        # self.click_element_id('Upgrade_charging_conflict_cancel')
        self.driver.app_start('com.carota.chery.v5')
        max_try = 5
        num = 0
        while num < max_try:
            num+=1
            logger.info(f'开始第{num}次重试')
            self.tomorrow_time_set(take_screenshot=False)
            time.sleep(0.5)
            try:
                len1 = len(self.get_element_id('Upgrade_travel_conflict_toast').get_text())
                logger.info(f'len1:{len1}')
                for _ in range(10):
                    len2 = len(self.get_element_id('Upgrade_travel_conflict_toast').get_text())
                    logger.info(f'len1:{len2}')
                    if len2!=len1:
                        self.screen_image('先设置预约充电、在设置预约升级冲突',self.mode_value)
                        self.click_element('schedule_time_set_cancel_btn')
                        break
                    time.sleep(1)
                break

            except Exception as e:
                now_time =TimeObject()
                self.screen_image(f'报错截图 {now_time}',self.mode_value)
                logger.error(e)
                self.click_element('schedule_setting_cancel_btn')
                logger.info('点击返回预约按钮，重新再试一次')

        # 结束恢复预约充电时间
        self.driver.app_start('com.android.launcher3', activity='.activity.AppListActivity')
        self.driver(resourceId='com.android.launcher3:id/app_icon_with_text')[1].click()
        time.sleep(0.5)
        self.click_element('mega_charging')
        time.sleep(0.5)
        x = int(self.height * 0.576)
        y = int(self.width * 0.526)
        z = int(self.width * 0.466)
        self.driver.swipe(x, y, x, z)
        time.sleep(3)
        self.click_element('Upgrade_charging_conflict_confim')





    def battery_fail(self):
        self.disclaimer_click()
        self.click_element('battery_power_title')

    def preconditions(self,take_screenshot=True):
        """
        前置条件界面
        """
        self.disclaimer_click()
        for _ in range(3):
            if self.check_element_exists('precondition') == False:
                logger.info('电量弹出弹出了，阻碍了前置弹框，重新点击立即升级')
                self.disclaimer_click()
            else:
                logger.info('前置条件检测界面')
                break

        if take_screenshot:
            time.sleep(0.5)
            self.screen_image('前置条件检测界面', self.mode_value)


    def preconditions_pass(self,take_screenshot=True):
        """
        前置条件全部通过
        """
        self.preconditions()
        for _ in range(3):
            if self.check_element_exists('precondition_pass') ==False:
                logger.info('电量弹出弹出了，阻碍了前置弹框，重新点击立即升级')
                self.preconditions()
            else:
                logger.info('前置条件全部通过')
                break
        if take_screenshot:
            time.sleep(0.5)
            self.screen_image('前置条件检测通过界面', self.mode_value)



    def preconditions_fail(self):
        """
          前置条件不通过
      """
        self.preconditions(take_screenshot=False)
        for _ in range(3):

            if self.check_element_exists('precondition_fail') ==False:
                logger.info('电量弹出弹出了，阻碍了前置不满足弹框，重新点击立即升级')
                self.preconditions(take_screenshot=False)
            else:
                logger.info('前置条件检测不满足界面')
                self.check_element_exists('precondition_fail_icon')
                break


    def odb_fail(self):
        self.matter_click()
        time.sleep(18)
        vcc.vcc_execute_nonblocking(
            '/opt/ota_pkg/oem_app/up_parser --source_path /opt/ota_pkg/ftp/CWC_update_package.tar.gz  --outputcheckab /opt/ota_pkg/out/checkab.out '
            ' --target_path /opt/ota_pkg/test_dir --log  /opt/ota_pkg/logs/up_parser.log --flash_info_path /opt/ota_pkg/output/flash.info '
            '--level 4')
        time.sleep(10)
        vcc.vcc_execute_nonblocking(
            '/opt/ota_pkg/oem_app/uds_shell --flash_data_config /opt/ota_pkg/output/flash.info  --log '
            ' /opt/ota_pkg/logs/uds_shell.log --mask 0x0000 --channel 123 --level 5 --log_size 5*1024*1024')
        self.click_element('disclaimer_argee_btn')
        logger.info('点击免责同意按钮')
        while True:
            if self.check_element_exists('precondition_fail_icon'):
                break




    # def odb_fail(self):
    #     self.disclaimer_click()
    #
    #     # 创建一个事件标志，用于通知命令执行线程停止
    #     stop_event = threading.Event()
    #
    #     # 创建一个队列用于存储命令执行结果
    #     result_queue = queue.Queue()
    #
    #     # 定义命令执行线程
    #     def execute_command():
    #         count = 0
    #         while not stop_event.is_set() and count < 1000:  # 设置最大执行次数防止无限循环
    #             count += 1
    #             logger.info(f'上位机发送OBD占用第{count}次')
    #             try:
    #                 result = vcc.vcc_execute(
    #                     ''
    #                     ''
    #                     '')
    #                 result_queue.put((count, result))
    #             except Exception as e:
    #                 logger.error(f"执行命令时出错: {str(e)}")
    #
    #             # 短暂休眠，避免CPU占用过高
    #             time.sleep(0.1)
    #
    #     # 启动命令执行线程
    #     command_thread = threading.Thread(target=execute_command)
    #     command_thread.daemon = True  # 设置为守护线程，主线程结束时自动结束
    #     command_thread.start()
    #
    #     # 主线程监控图标
    #     start_time = time.time()
    #     timeout = 60  # 设置超时时间，防止无限等待
    #
    #     while time.time() - start_time < timeout:
    #         # 检查图标是否出现
    #         if self.check_element_exists('precondition_fail_icon'):
    #             logger.info("检测到前置条件失败图标，停止发送命令")
    #             time.sleep(600)
    #             # stop_event.set()  # 通知命令线程停止
    #             # break
    #
    #         # 处理命令执行结果
    #         while not result_queue.empty():
    #             count, result = result_queue.get()
    #             logger.info(f"第{count}次命令执行结果: {result}")
    #
    #         # 短暂休眠，避免UI检查过于频繁
    #         time.sleep(0.5)
    #
    #     # 等待命令线程结束
    #     stop_event.set()
    #     command_thread.join(timeout=5)
    #
    #     # 检查最终状态
    #     if self.check_element_exists('precondition_fail_icon'):
    #         logger.info("成功触发前置条件失败")
    #         return True
    #     else:
    #         logger.warning("未能触发前置条件失败")
    #         return False


    def power_on(self,take_screenshot=True):
        """
        车辆即将远程上电
        Returns:

        """
        self.preconditions_pass()
        self.check_element_exists('power_on')
        logger.info('车辆即将远程上电')
        time.sleep(0.5)
        if take_screenshot:
            self.screen_image('车辆即将远程上电', self.mode_value)

    def power_on_predition(self,take_screenshot=True):
        """
        车辆即将远程上电
        Returns:

        """
        self.preconditions_pass()
        self.check_element_exists('power_on')
        logger.info('车辆即将远程上电')
        time.sleep(0.5)
        if take_screenshot:
            self.screen_image('车辆即将远程上电', self.mode_value)

    def poweron_otamode_fail(self):
        '''
        远程上电失败
        Returns:

        '''
        self.power_on()
        logger.info('等待远程上电失败弹框弹出')
        for _ in  range(40):
            if self.check_element_exists('remote_power_on_fail_title',ignore=True):
                break
        time.sleep(1)


    def second_predition_check_pass(self,take_screenshot=True):
        '''
        第二次条件检查中
        Returns:

        '''
        self.power_on(take_screenshot=False)
        self.check_element_exists('vehicle_upgrade_checking')
        if take_screenshot:
            self.screen_image('第二次前置条件检测中',self.mode_value)

    def obd_check(self):
        for i in range(120):
            vcc.vcc_execute_nonblocking('/opt/ota_pkg/run.sh')
            time.sleep(0.5)

    def second_predition_check_fail(self,take_screenshot=True):
        '''
        第二次条件检查 不过
        Returns:

        '''
        self.disclaimer_click()
        self.check_element_exists('vehicle_upgrade_checking')
        self.screen_image('第二次前置条件检测中', self.mode_value)
        for i in range(100):
            logger.info("等待 二次安装条件不足、车辆下电")
            if self.driver(resourceId='com.carota.chery.v5:id/iv_error_roll'):
                self.screen_image('二次安装条件不足、车辆下电中',self.mode_value)
                break
            time.sleep(1)
        for i in range(10):
            logger.info("开始等待二次安装条件不足结果页面")
            if self.check_element_exists('Remote_rescue_confim_btn'):
                self.screen_image('二次安装条件不足页面', self.mode_value)
                break
            time.sleep(1)
        self.click_element('Remote_rescue_confim_btn')

    def second_predition_check_fail_powerofffail(self,take_screenshot=True):
        '''
        第二次条件检查 不过,下电失败
        Returns:

        '''
        self.disclaimer_click()
        self.check_element_exists('vehicle_upgrade_checking')
        self.screen_image('第二次前置条件检测中', self.mode_value)
        for i in range(100):
            logger.info("等待 二次安装条件不足、车辆下电")
            if self.driver(resourceId='com.carota.chery.v5:id/iv_error_roll'):
                self.screen_image('二次安装条件不足、车辆下电中', self.mode_value)
                break
            time.sleep(1)
        for i in range(10):
            logger.info("开始等待下电失败")
            if self.check_element_exists('task_inivail_btn'):
                self.screen_image('下电失败', self.mode_value)
                self.click_element('task_inivail_btn')
                break
            time.sleep(1)

    def second_predition_check_fail_otaoutfail(self,take_screenshot=True):
        '''
        第二次条件检查 不过,下电失败
        Returns:

        '''
        self.disclaimer_click()
        self.check_element_exists('vehicle_upgrade_checking')
        self.screen_image('第二次前置条件检测中', self.mode_value)
        for i in range(10):
            logger.info("开始等待下电失败")
            if self.check_element_exists('task_inivail_btn'):
                self.screen_image('退ota模式失败', self.mode_value)
                self.click_element('task_inivail_btn')
                break
            time.sleep(1)

    def poweroff_otamodeout_fail(self):
        '''
       第二次条件检查 不过 下电失败
       Returns:

       '''
        self.power_on_predition(take_screenshot=False)
        for _ in range(100):
            if self.driver(resourceId='com.carota.chery.v5:id/toast_rv_pc'):
                logger.info('二次安装条件不足、车辆下电中')
                break
        self.check_element_exists('remote_power_on_fail_title')
     



    def vehicle_upgrading(self,take_screenshot=True):
        '''
        系统更新中
        Returns:

        '''
        self.second_predition_check_pass()
        self.check_element_exists('update_title')
        if take_screenshot:
            time.sleep(15)
            self.screen_image('系统更新中',self.mode_value)

    def upgrade_success_poweroff(self):
        '''升级成功车辆即将下电'''
        self.vehicle_upgrading(take_screenshot=True)
        num = 0
        while True:
            if self.driver(resourceId='com.carota.chery.v5:id/tv_fail_service_roll'):
                self.screen_image('升级成功，车辆即将下电',self.mode_value)
                break
            logger.info(f"任务更新中,已更新{num}秒")
            time.sleep(5)
            num+=5

    def upgrade_success_page(self,):
        '''升级成功，结果弹框'''
        self.upgrade_success_poweroff()
        self.check_element_exists('upgrade_success_page_btn')
        self.screen_image('升级成功，结果弹框',self.mode_value)
        self.click_element('upgrade_success_page_btn')


    def upgrade_success_poweroff_fail(self):
        '''升级成功、下电失败、退ota失败'''
        self.upgrade_success_poweroff()
        self.check_element_exists('remote_power_on_fail_title')

    def upgrade_fail_poweroff(self):
        '''升级失败车辆即将下电'''
        self.vehicle_upgrading()
        num = 0
        while True:
            if self.check_element_exists("upgrade_fail_poweroff"):
                self.screen_image('升级失败，车辆即将下电', self.mode_value)
                break
            logger.info(f"任务更新中,已更新{num}秒")
            num += 5
            # time.sleep(5)

    def upgrade_fail_page(self):
        '''升级失败 结果弹框'''
        self.upgrade_fail_poweroff()
        self.check_element_exists('upgrade_fail_page_btn')
        self.screen_image('升级失败，结果弹框',self.mode_value)
        self.click_element('upgrade_fail_page_btn')

    def upgrade_fail_poweroff_fail(self):
        '''升级失败、下电失败、退ota失败'''
        self.upgrade_fail_poweroff()
        self.check_element_exists('remote_power_on_fail_title')






    def task_inivail(self):
        '''
        任务失效
        Returns:

        '''
        self.second_predition_check_pass()
        self.check_element_exists('task_inivail_poweroff')
        time.sleep(3)
        self.screen_image('升级任务无效，车辆正在下电',self.mode_value)
        time.sleep(7)
        self.check_element_exists('task_inivail_btn')
        self.screen_image('升级任务无效结果弹框',self.mode_value)
        self.click_element('task_inivail_btn')


    def get_remote_rescue_check(self,take_screenshot=True):
        '''
        远程救援任务说明
        Returns:

        '''
        # self.enter_engineering()
        # self.click_element_id("Remote_rescue")
        for _ in range(30):
            logger.info('正在檢測救援任務')
            if self.check_element_exists('Remote_rescue_title'):
                break

        time.sleep(1)
        if take_screenshot:
            self.screen_image('远程救援任务说明-倒计时',self.mode_value)
        time.sleep(18)
        if take_screenshot:
            self.screen_image('远程救援任务说明-确定按钮',self.mode_value)
        self.click_element('Remote_rescue_btn')

    def remote_resuce_upgrade_success(self):
        '''
        远程救援升级成功
        Returns:

        '''
        self.get_remote_rescue_check(take_screenshot=True)
        logger.info('远程救援任务升级中')

        # 校验ecu
        # 使用标志变量记录已截图的元素
        step1_captured = False
        step2_captured = False
        step3_captured = False
        step4_captured = False
        
        while True:
            try:
                # 正确的icon
                tt = self.driver(resourceId='com.carota.chery.v5:id/iv_item_suc')
                
                # 只有当元素存在且尚未截图时才截图
                if tt[0] and not step1_captured:
                    self.screen_image('救援校验ECU成功', self.mode_value)
                    step1_captured = True  # 标记已截图
                    
                if tt[1] and not step2_captured:
                    self.screen_image('救援前置条件校验通过', self.mode_value)
                    step2_captured = True  # 标记已截图
                    
                if tt[2] and not step3_captured:
                    self.screen_image('救援下载完成', self.mode_value)
                    step3_captured = True  # 标记已截图
                    
                if tt[3] and not step4_captured:
                    self.screen_image('救援任务升级成功', self.mode_value)
                    step4_captured = True  # 标记已截图
                    break  # 全部完成后退出循环
                
                # 如果没有全部完成，继续等待
                if not (step1_captured and step2_captured and step3_captured and step4_captured):
                    time.sleep(2)  # 等待一段时间再检查
                else:
                    break  # 全部截图完成后退出
                    
            except Exception as e:
                time.sleep(5)
                logger.debug(e)

        self.click_element('Remote_rescue_confim_btn')

    def remote_resuce_predition_fail(self):
        '''
        救援前置条件失败
        Args:
            self:

        Returns:

        '''
        self.get_remote_rescue_check(take_screenshot=False)
        logger.info('远程救援任务升级中')
        while True:
            try:
                # 错误的icon
                tt = self.driver(resourceId='com.carota.chery.v5:id/iv_item_fail')
                if tt[0]:
                    self.screen_image('救援前置验证失败', self.mode_value)
                    break

            except Exception as e:
                time.sleep(2)
        self.click_element('Remote_rescue_confim_btn')

    def remote_resuce_download_fail(self):
        """救援下载失败"""
        # self.get_remote_rescue_check(take_screenshot=False)
        for _ in range(30):
            logger.info('正在檢測救援任務')
            if self.check_element_exists('Remote_rescue_title'):
                break

        time.sleep(18)
        vcc.vcc_execute_nonblocking(
            '/opt/ota_pkg/oem_app/tp_client --ip ************ --sourcela 0x0F00 --targetla 0x01 --data 1101 --log /opt/ota_pkg/oem_app/tp_client.log --out version.out --level 5')
        logger.info('已重启tbox')
        time.sleep(10)
        self.click_element('Remote_rescue_btn')

        # vcc.vcc_execute_nonblocking(
        #     '/opt/ota_pkg/oem_app/tp_client --ip ************ --sourcela 0x0F00 --targetla 0x01 --data 1101 --log /opt/ota_pkg/oem_app/tp_client.log --out version.out --level 5')
        # logger.info('已重启tbox')
        # time.sleep(10)
        # vcc.vcc_execute_nonblocking(
        #     '/opt/ota_pkg/oem_app/tp_client --ip ************ --sourcela 0x0F00 --targetla 0x01 --data 1101 --log /opt/ota_pkg/oem_app/tp_client.log --out version.out --level 5')
        # logger.info('已重启tbox')
        # while True:
        for _ in range(30):
            try:

                self.check_element_exists('Remote_rescue_icon_fail')
                tt = self.driver(resourceId='com.carota.chery.v5:id/iv_item_fail')
                if tt[0]:
                    logger.info('救援下载失败')
                    self.screen_image('救援下载失败', self.mode_value)
                    break
                vcc.vcc_execute_nonblocking(
                    '/opt/ota_pkg/oem_app/tp_client --ip ************ --sourcela 0x0F00 --targetla 0x01 --data 1101 --log /opt/ota_pkg/oem_app/tp_client.log --out version.out --level 5')
                logger.info('已重启tbox')
                time.sleep(10)

            except Exception as e:
                time.sleep(2)
        self.click_element('Remote_rescue_confim_btn')



    def remote_resuce_check_ecu_fail(self):
        '''
        救援校验ECU失败
        Args:
            self:

        Returns:

        '''
        self.get_remote_rescue_check(take_screenshot=False)
        while True:
            try:
                # 错误的icon
                tt = self.driver(resourceId='com.carota.chery.v5:id/iv_item_fail')
                if tt[0]:
                    self.screen_image('救援校验ECU失败', self.mode_value)
                    break

            except Exception as e:
                time.sleep(2)
        self.click_element('Remote_rescue_confim_btn')

    def remote_resuce_upgrade_fail(self):
        '''
        救援升级失败
        Args:
            self:

        Returns:

        '''
        self.get_remote_rescue_check(take_screenshot=False)
        while True:
            try:
                # 错误的icon
                tt = self.driver(resourceId='com.carota.chery.v5:id/iv_item_fail')
                if tt[0]:
                    self.screen_image('救援升级失败', self.mode_value)
                    break

            except Exception as e:
                time.sleep(2)
        self.click_element('Remote_rescue_confim_btn')





        # def update_success(self):
    #     """
    #     更新进行中 > 升级成功
    #     Returns:
    #
    #     """
    #     self.preconditions_pass()
    #     self.check_element_exists('update_title')
    #     logger.info('更新进行中')
    #     self.screen_image('更新进行中')
    #     while True:
    #         time.sleep(5)
    #         if self.driver(resourceId='com.carota.chery:id/por_tv_title'):
    #             break
    #     self.screen_image('升级成功弹窗', self.mode_value)
    #     logger.info('升级成功')
    #     time.sleep(1)
    #     self.click_element_id_text('success_btn')
    #     logger.info('点击升级成功按钮，显示升级主页面')
    #     time.sleep(1)
    #     self.screen_image('升级成功主页面', self.mode_value)
    #
    #





        '''
        上面是 升级流程 封装
---------------------------------------------分割符---------------------------------------------------------------------
        下面是 事件方法 封装
        '''



    def click_element(self, element_name: str) -> bool:
        """
        点击元素对象
        Args:
            element_name: 需要获取的对象名称

        Returns:
            bool:
        """
        element_info = elements.get(element_name)
        try:
            element_id = self.driver(resourceId=element_info['resourceid'])
            if self.wait_for_element(element_id):
                # logger.info(f"通过ID找到元素: {element_info['resourceid']}")
                element_id.click()
                return True
            logger.error(
                f'未找到元素: {element_name}, resourceid: {element_info["resourceid"]}')
            return False
        except Exception as e:
            logger.error(f'检查元素 {element_name} 出错: {str(e)}')
            return False

    def get_element_id(self, element_name: str,ignore=False) -> bool:
        """
        点击元素对象
        Args:
            element_name: 需要获取的对象名称

        Returns:
            bool:
        """
        element_info = elements.get(element_name)
        try:
            element_id = self.driver(resourceId=element_info['resourceid'])
            if self.wait_for_element(element_id):
                # logger.info(f"通过ID找到元素: {element_info['resourceid']}")
                return element_id
            if ignore == False:
                logger.error(
                    f'未找到元素、无法点击: {element_name}, resourceid: {element_info["resourceid"]}, text: {element_info["text"]}')
            return False
        except Exception as e:
            logger.error(f'检查元素 {element_name} 出错: {str(e)}')
            return False

    def click_element_by_classname(self, element_name: str) -> object:
        """
           点击元素对象
           Args:
               element_name: 需要获取的对象名称

           Returns:
               str: 将classname对象返回， 在外层需要的地方 [0].click()
               """
        element_info = elements.get(element_name)
        try:
            element_classname = self.driver(className=element_info['classname'])
            if self.wait_for_element(element_classname):
                logger.info(f"通过classname找到元素: {element_info['classname']}")
                return element_classname

            logger.info(
                f'未找到元素: {element_name}, classname: {element_info["classname"]}')
            return False
        except Exception as e:
            logger.error(f'检查元素 {element_name} 出错: {str(e)}')
            return False

    def check_element_text_exists(self, element_name: str) -> bool:
        """
        检查元素文本是否存在 针对只有文本的元素
        Args:
            element_name: 需要检查的元素名称

        Returns:
            bool: 返回元素的名称
        """
        element_info = elements.get(element_name)
        try:
            self.driver(text=element_info['text'])
            return True
        except Exception as e:
            logger.error('检查元素出错，未找到这个元素:', {e})
            return False

    def check_element_exists(self, element_name: str,ignore=False,max_wait_time=15) -> bool:
        """
        检查元素是否存在
        Args:
            element_name: 需要检查的元素名称

        Returns:
            bool: 元素是否存在，如果通过resourceId和text都找不到，则返回False
        """
        element_info = elements.get(element_name)
        try:
            element_id = self.driver(resourceId=element_info['resourceid'])
            if self.wait_for_element(element_id,max_wait_time=max_wait_time):
                return self.wait_for_element(element_id)
            if ignore==False:
                logger.error(
                    f'未找到元素: {element_name}, resourceid: {element_info["resourceid"]}')
            return False

        except Exception as e:
            logger.error(f'检查元素 {element_name} 出错: {str(e)}')
            return False

    def check_element_clickable(self, element_name: str) -> bool:
        """
        检查元素是否可点击
        Args:
            element_name: 需要检查的元素名称

        Returns:
            bool: 元素是否可点击，如果元素不存在或不可点击则返回False
        """
        element_info = elements.get(element_name)
        try:
            element_id = self.driver(resourceId=element_info['resourceid'])

            if self.wait_for_element(element_id):
                enable = element_id.info.get('enabled')
                logger.info(f'找到元素的可点击属性:{enable}')
                return enable


            logger.info(
                f'未找到元素: {element_name}, resourceid: {element_info["resourceid"]}')
            return False
        except Exception as e:
            logger.error(f'检查元素 {element_name} 出错: {str(e)}')
            return False

    def check_mark(self):
        """
        获取前置条件icon图标
        Returns:

        """
        # 重试机制，直到获取到非加载状态的图片
        max_retries = 20  # 最大重试次数
        retry_interval = 1.5  # 重试间隔（秒）
        for attempt in range(max_retries):
            # 获取图片元素
            mark_element = self.driver(resourceId='com.carota.chery:id/item_iv_pc')[0]
            mark_image = mark_element.screenshot()

            # 保存图片到本地
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            save_path = os.path.join(save_dir, f'screenshot_{timestamp}.png')
            mark_image.save(save_path)

            # 识别图片内容
            is_x_mark = image_recognition.recognize_mark(mark_image)
            # logger.info(f"当前挡位下图像识别结果: {is_x_mark}")
            if is_x_mark is not None:  # 假设recognize_mark返回None表示加载状态
                break
            time.sleep(retry_interval)
        # 如果达到最大重试次数仍然在加载状态，记录错误
        if is_x_mark is None:
            logger.error("达到最大重试次数，图片仍处于加载状态")
            assert False, "图片一直处于加载状态，无法进行识别"

        return is_x_mark

    def swipe_down(self, start_percent=0.01, end_percent=0.8, duration=0.5, steps=10):
        """
        从窗口上方滑动到下方

        Args:
            start_percent: 起始位置占屏幕高度的百分比，默认为0.01（屏幕上方1%处）
            end_percent: 结束位置占屏幕高度的百分比，默认为0.8（屏幕下方80%处）
            duration: 滑动持续时间（秒），默认为0.5秒
            steps: 滑动过程中的步数，默认为10步

        Returns:
            bool: 滑动是否成功
        """
        try:
            # 获取屏幕尺寸
            window_size = self.driver.window_size()
            width = window_size[0]
            height = window_size[1]

            # 计算滑动的起始和结束坐标
            start_x = width * 0.5  # 水平中心
            start_y = height * start_percent
            end_x = width * 0.5  # 水平中心
            end_y = height * end_percent

            # 执行滑动
            logger.info(f"从屏幕上方({start_percent * 100}%)滑动到下方({end_percent * 100}%)")
            self.driver.swipe(start_x, start_y, end_x, end_y, duration=duration * 1000, steps=steps)
            logger.info("滑动成功")
            return True
        except Exception as e:
            logger.error(f"滑动操作失败: {str(e)}")
            return False

    def swipe_up(self, start_percent=0.8, end_percent=0.2, duration=0.5, steps=10):
        """
        从窗口下方滑动到上方

        Args:
            start_percent: 起始位置占屏幕高度的百分比，默认为0.8（屏幕下方80%处）
            end_percent: 结束位置占屏幕高度的百分比，默认为0.2（屏幕上方20%处）
            duration: 滑动持续时间（秒），默认为0.5秒
            steps: 滑动过程中的步数，默认为10步

        Returns:
            bool: 滑动是否成功
        """
        # 调用swipe_down方法，但交换起始和结束百分比
        return self.swipe_down(start_percent=start_percent, end_percent=end_percent, duration=duration, steps=steps)
