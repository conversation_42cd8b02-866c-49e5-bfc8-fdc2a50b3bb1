# coding: utf-8
# Project：car_auto_test
# File：test_E051.py
# Author：杨郑健
# Date ：2025/6/27 9:57
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("埋点数据")
@AllureDecorators.title("升级失败，退出升级模式失败")
def test_E051(driver, launch_app):
    """
    测试步骤
    1、破外ecu升级包、回滚包，变更源版本
    2、“升级主页面”点击“立即升级”，“检测升级前置条件”检测通过，进入“更新进行中”
    3、制造退ota失败，进入“升级失败，车辆存在问题，退出升级模式失败”，查看后台埋点数据

    预期结果
    3、“升级失败，退ota模式失败结果页面显示”
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        "tbox_status": "NOTREAD",
        "power": ["READY", "OFF"],
        "task": "normal",
        "gear": "P",
        "handbrake": "OFF"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'READY':
            objects.get_task()
            logger.info("READY挡验证完成")
            assert True
        elif current_state == 'OFF':
            logger.info("OFF挡升级失败显示，查看埋点数据")
            serial_tool.ecu_uds_shell()
            objects.rollback_success_otamodeout_fail()
            burying_points = Cloud("奇瑞汽车").get_burying_point()
            result = False
            for burying_point in burying_points:
                if burying_point["msg"] == '升级失败，退ota模式失败结果页面显示':
                    result = True
            assert result
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()