import atexit
import webbrowser

from simple_deobfuscator import _deobfuscator

"""
程序运行入口
UI组合测试运行脚本
"""
import uiautomator2 as u2
import time
import pytest
import tempfile
import json
import os
import traceback
from utils.LoggingSystem.Logger import logger
from testcases.EEA4.PageObjects.EngineeringPassword import EngineeringPassword
from config.Config import *
from config.api_config import APIConfig
from app import create_app
from utils.AdbPort.Adb_Port import adb

atexit.register(_deobfuscator._cleanup)  # 程序退出时清理临时文件
RESOURCE_MODE = "OBFUSCATED"


# def run_eea5_ui_all_combinations(vehicle_flag):
#     # 只在最外层连接一次设备
#     driver = u2.connect()
#     width, height = driver.window_size()
#     x = int(height * 0.536)
#     start_y = int(width * 0.473)
#     end_y = int(width * 0.402)
#
#     """运行所有语言和模式组合的测试"""
#     # day_night_list = ['day']
#     day_night_list = ['day', 'night']
#     # 语言列表
#     language_list = ['中文', 'English', 'Norsk', 'Deutsh', 'Français', 'Italiano',
#                      'Español', 'Nederlands', 'Svenska', 'Português', 'Polski', 'Dansk']
#     # language_list = ['Svenska']
#
#     # 创建image目录（如果不存在）
#     base_image_dir = BASE_DIR + r"\utils\ImageRecognition\Image_ui\EEA5"
#
#     mode = 0
#     mode_dict = {}
#
#     # 遍历所有组合
#     for day_mode in day_night_list:
#         # 如果是night模式，先将语言重置为中文
#         if day_mode == 'night':
#             logger.info("切换到night模式前，先将语言重置为中文")
#             # 不需要重新连接设备
#             driver.app_start('com.mega.carsettings')
#             driver(resourceId='com.mega.carsettings:id/menu_system').click()
#             time.sleep(3)
#
#             vehicle_language = driver(resourceId='android:id/summary')[1].get_text()
#             logger.info(f'当前车端语言:{vehicle_language}')
#
#             if vehicle_language != '中文':
#                 logger.info('将语言重置为中文')
#                 driver(resourceId='android:id/summary')[1].click()
#                 time.sleep(1)
#
#                 # 可能需要多次向下滑动才能找到中文
#                 max_attempts = 20
#                 for attempt in range(max_attempts):
#                     # 向下滑动（从上往下）
#                     driver.swipe(x, end_y, x, start_y)
#                     time.sleep(1)
#
#                 driver(resourceId='com.mega.carsettings:id/tv_sure').click()
#                 logger.info('已将语言重置为中文，等待5s后语言切换完毕')
#
#             # 现在切换到夜间模式
#             driver(resourceId='com.mega.carsettings:id/menu_display').click()
#             driver(className='android.widget.RadioButton')[2].click()
#             time.sleep(3)
#
#         for language in language_list:
#             mode += 1
#             # 为每个组合创建对应的文件夹
#             folder_name = f"{day_mode}_{language}"
#             folder_path = os.path.join(base_image_dir, folder_name)
#
#             # 创建文件夹（如果不存在）
#             if not os.path.exists(folder_path):
#                 os.makedirs(folder_path)
#
#             logger.info(f"当前测试组合: 模式={day_mode}, 语言={language}")
#
#             mode_dict[str(mode)] = folder_path
#             # 不需要重新连接设备，只需要确保应用已启动
#             driver.app_start('com.mega.carsettings')
#
#             if day_mode == 'day':
#                 driver(resourceId='com.mega.carsettings:id/menu_system').click()
#                 time.sleep(3)
#                 vehicle_language = driver(resourceId='android:id/summary')[1].get_text()
#                 logger.info(f'下一个需要截图的UI语言:{language}')
#                 logger.info(f'当前车端语言:{vehicle_language}')
#
#                 if vehicle_language == language:
#                     pass
#                 else:
#                     driver(resourceId='android:id/summary')[1].click()
#                     time.sleep(1)
#                     driver.swipe(x, start_y, x, end_y)
#                     time.sleep(1)
#                     driver(resourceId='com.mega.carsettings:id/tv_sure').click()
#                     time.sleep(5)
#
#             if day_mode == 'night':
#                 driver(resourceId='com.mega.carsettings:id/menu_system').click()
#                 vehicle_language = driver(resourceId='android:id/summary')[1].get_text()
#
#                 logger.info(f'下一个要截图的语言:{language}')
#                 logger.info(f'当前车端语言:{vehicle_language}')
#                 if vehicle_language == language:
#                     pass
#                 else:
#                     logger.info('主机设置内滑动下一个语言')
#                     driver(resourceId='android:id/summary')[1].click()
#                     time.sleep(0.5)
#                     driver.swipe(x, start_y, x, end_y)
#                     time.sleep(0.5)
#                     driver(resourceId='com.mega.carsettings:id/tv_sure').click()
#                     logger.info('等待5s后语言切换完毕')
#                     time.sleep(5)
#
#             # 将mode和目录路径写入临时 JSON 文件
#             with tempfile.NamedTemporaryFile(delete=False, suffix='.json', mode='w', encoding='utf-8') as f:
#                 json.dump(mode_dict, f)
#                 mode_dict_path = f.name
#
#             pytest.main([
#                 f'--mode={mode}',
#                 f'--mode_dict_path={mode_dict_path}',
#                 f'--vehicle_flag={vehicle_flag}'
#             ])
#             logger.info(f"完成测试组合: 语言={language}, 模式={day_mode}")
#             time.sleep(3)  # 等待一段时间再运行下一个组合
#
#
#
#
# def run_eea4_ui_all_combinations_m1efl1(vehicle_flag):
#     driver = u2.connect()
#
#     """运行所有语言和模式组合的测试"""
#     # day_night_list = ['day']
#     day_night_list = ['day', 'night']
#     # 语言列表
#     language_list = ['English']
#
#     # 创建image目录（如果不存在）
#     base_image_dir = BASE_DIR + r"\utils\ImageRecognition\Image_ui\M1EFL沙特"
#
#     mode = 0
#     mode_dict = {}
#
#     # 遍历所有组合
#     for day_mode in day_night_list:
#         # 如果是night模式，先将语言重置为中文
#         # if day_mode == 'night':
#         #     logger.info("切换到night模式前，先将语言重置为English")
#         #     # 不需要重新连接设备
#         #     driver.app_start(package_name='com.desaysv.setting/', activity='/com.desaysv.setting.MainActivity')
#         #     driver(classname='android.view.View')[7].click()
#         #     time.sleep(3)
#         #     vehicle_language = driver(resourceId='com.desaysv.setting:id/tv_language_muti').get_text()
#         #     logger.info(f'当前车端语言:{vehicle_language}')
#         #
#         #     if vehicle_language != 'English':
#         #         logger.info('将语言重置为English')
#         #         driver(resourceId='com.desaysv.setting:id/arrow_language').click()
#         #         time.sleep(1)
#         #
#         #         # 可能需要多次向下滑动才能找到中文
#         #         max_attempts = 20
#         #         for attempt in range(max_attempts):
#         #             # 向下滑动（从上往下）
#         #             driver.swipe(x, end_y, x, start_y)
#         #             time.sleep(1)
#         #
#         #         driver(className='android.widget.Button')[0].click()
#         #         time.sleep(5)
#         #         logger.info('已将语言重置为English，等待5s后语言切换完毕')
#         #
#         #     driver(resourceId='com.desaysv.setting:id/rl_top_menu')[2].click()
#         #     driver(className='android.widget.RadioButton')[2].click()
#         #     time.sleep(3)
#
#         for language in language_list:
#             mode += 1
#             # 为每个组合创建对应的文件夹
#             folder_name = f"{day_mode}_{language}"
#             folder_path = os.path.join(base_image_dir, folder_name)
#
#             # 创建文件夹（如果不存在）
#             if not os.path.exists(folder_path):
#                 os.makedirs(folder_path)
#
#             logger.info(f"当前测试组合: 模式={day_mode}, 语言={language}")
#
#             mode_dict[str(mode)] = folder_path
#             # 不需要重新连接设备，只需要确保应用已启动com.desaysv.setting/com.desaysv.setting.view.activity.SettingActivity
#             driver.app_start(package_name='com.desaysv.setting', activity='.MainActivity')
#             driver(className='android.view.View')[9].click()
#
#             driver.xpath(
#                 '//*[@resource-id="android:id/content"]/androidx.compose.ui.platform.ComposeView[1]/android.view.View[1]/android.view.View[1]/android.widget.ScrollView[2]/android.view.View[8]').click()
#
#             # if day_mode == 'day':
#             #     driver(className='android.view.View')[9].click()
#             #     time.sleep(3)
#             #     vehicle_language = driver(resourceId='com.desaysv.setting:id/tv_language_muti').get_text()
#             #     logger.info(f'下一个需要截图的UI语言:{language}')
#             #     logger.info(f'当前车端语言:{vehicle_language}')
#             #
#             #     if vehicle_language == language:
#             #         pass
#             #     else:
#             #         driver(resourceId='com.desaysv.setting:id/arrow_language').click()
#             #         time.sleep(1)
#             #         driver.swipe(x, start_y, x, end_y)
#             #         time.sleep(1)
#             #         # 点击切换同意按钮
#             #         # driver.click(agree_x,agree_y)
#             #         driver(className='android.widget.Button')[0].click()
#             #
#             #         time.sleep(5)
#
#             # language='English'
#             # mode='day'
#             # 将mode和目录路径写入临时 JSON 文件
#             with tempfile.NamedTemporaryFile(delete=False, suffix='.json', mode='w', encoding='utf-8') as f:
#                 json.dump(mode_dict, f)
#                 mode_dict_path = f.name
#
#             pytest.main([
#                 f'--mode={mode}',
#                 f'--mode_dict_path={mode_dict_path}',
#                 f'--vehicle_flag={vehicle_flag}'
#             ])
#             logger.info(f"完成测试组合: 语言={language}, 模式={day_mode}")
#             time.sleep(3)  # 等待一段时间再运行下一个组合
#
#
# def run_eea4_ui_all_combinations_t22(vehicle_flag):
#     """T22车型的测试函数"""
#     # 只在最外层连接一次设备
#     driver = u2.connect()
#     width, height = driver.window_size()
#     x = int(height * 0.536)
#     start_y = int(width * 0.577)
#     end_y = int(width * 0.422)
#     agree_x = int(height * 0.438)
#     agree_y = int(height * 0.759)
#
#     """运行所有语言和模式组合的测试"""
#     # day_night_list = ['day']
#     day_night_list = ['day', 'night']
#     # 语言列表
#     language_list = ['English', 'Português', 'Español', 'Italiano', 'Deutsch',
#                      'Polski', 'Français', 'Nederlands', 'Norge', 'Svenska', 'Magyar', 'Ελληνικά', 'Dansk', 'Română']
#     # language_list = ['Svenska']
#
#     # 创建image目录（如果不存在）
#     base_image_dir = BASE_DIR + r"\utils\ImageRecognition\Image_ui\EEA4"
#
#     mode = 0
#     mode_dict = {}
#
#     # 遍历所有组合
#     for day_mode in day_night_list:
#         # 如果是night模式，先将语言重置为中文
#         if day_mode == 'night':
#             logger.info("切换到night模式前，先将语言重置为English")
#             # 不需要重新连接设备
#             driver.app_start(package_name='com.desaysv.setting', activity='.view.activity.SettingActivity')
#             driver(resourceId='com.desaysv.setting:id/rl_top_menu')[3].click()
#             time.sleep(3)
#             vehicle_language = driver(resourceId='com.desaysv.setting:id/tv_language_muti').get_text()
#             logger.info(f'当前车端语言:{vehicle_language}')
#
#             if vehicle_language != 'English':
#                 logger.info('将语言重置为English')
#                 driver(resourceId='com.desaysv.setting:id/arrow_language').click()
#                 time.sleep(1)
#
#                 # 可能需要多次向下滑动才能找到中文
#                 max_attempts = 20
#                 for attempt in range(max_attempts):
#                     # 向下滑动（从上往下）
#                     driver.swipe(x, end_y, x, start_y)
#                     time.sleep(1)
#
#                 driver(className='android.widget.Button')[0].click()
#                 time.sleep(5)
#                 logger.info('已将语言重置为English，等待5s后语言切换完毕')
#
#             driver(resourceId='com.desaysv.setting:id/rl_top_menu')[2].click()
#             driver(className='android.widget.RadioButton')[2].click()
#             time.sleep(3)
#
#         for language in language_list:
#             mode += 1
#             # 为每个组合创建对应的文件夹
#             folder_name = f"{day_mode}_{language}"
#             folder_path = os.path.join(base_image_dir, folder_name)
#
#             # 创建文件夹（如果不存在）
#             if not os.path.exists(folder_path):
#                 os.makedirs(folder_path)
#
#             logger.info(f"当前测试组合: 模式={day_mode}, 语言={language}")
#
#             mode_dict[str(mode)] = folder_path
#             # 不需要重新连接设备，只需要确保应用已启动com.desaysv.setting/com.desaysv.setting.view.activity.SettingActivity
#             driver.app_start(package_name='com.desaysv.setting', activity='.view.activity.SettingActivity')
#
#             if day_mode == 'day':
#                 driver(resourceId='com.desaysv.setting:id/rl_top_menu')[3].click()
#                 time.sleep(3)
#                 vehicle_language = driver(resourceId='com.desaysv.setting:id/tv_language_muti').get_text()
#                 logger.info(f'下一个需要截图的UI语言:{language}')
#                 logger.info(f'当前车端语言:{vehicle_language}')
#
#                 if vehicle_language == language:
#                     pass
#                 else:
#                     driver(resourceId='com.desaysv.setting:id/arrow_language').click()
#                     time.sleep(1)
#                     driver.swipe(x, start_y, x, end_y)
#                     time.sleep(1)
#                     # 点击切换同意按钮
#                     # driver.click(agree_x,agree_y)
#                     driver(className='android.widget.Button')[0].click()
#
#                     time.sleep(5)
#
#             if day_mode == 'night':
#                 driver(resourceId='com.desaysv.setting:id/rl_top_menu')[3].click()
#                 time.sleep(3)
#                 vehicle_language = driver(resourceId='com.desaysv.setting:id/tv_language_muti').get_text()
#
#                 logger.info(f'下一个要截图的语言:{language}')
#                 logger.info(f'当前车端语言:{vehicle_language}')
#                 if vehicle_language == language:
#                     pass
#                 else:
#
#                     driver(resourceId='com.desaysv.setting:id/arrow_language').click()
#                     time.sleep(1)
#                     driver.swipe(x, start_y, x, end_y)
#                     time.sleep(1)
#                     # 点击切换同意按钮
#                     # driver.click(agree_x,agree_y)
#                     driver(className='android.widget.Button')[0].click()
#                     time.sleep(5)
#
#             # 将mode和目录路径写入临时 JSON 文件
#             with tempfile.NamedTemporaryFile(delete=False, suffix='.json', mode='w', encoding='utf-8') as f:
#                 json.dump(mode_dict, f)
#                 mode_dict_path = f.name
#
#             pytest.main([
#                 f'--mode={mode}',
#                 f'--mode_dict_path={mode_dict_path}',
#                 f'--vehicle_flag={vehicle_flag}'
#             ])
#             logger.info(f"完成测试组合: 语言={language}, 模式={day_mode}")
#             time.sleep(3)  # 等待一段时间再运行下一个组合
#
#
# def run_eea4_ui_all_combinations_t1gc(vehicle_flag):
#     """T1GC欧盟截图"""
#     # 只在最外层连接一次设备
#     driver = u2.connect()
#     width, height = driver.window_size()
#     x = int(height * 0.626)
#     start_y = int(width * 0.854)
#     end_y = int(width * 0.100)
#     change_x = int(height * 0.501)
#     change_y = int(width * 0.501)
#     change_end_y = int(width * 0.420)
#
#     logger.info("%s, %s, %s, %s", width, height, change_x, change_y)
#
#     """运行所有语言和模式组合的测试"""
#     day_night_list = ['day']
#     # day_night_list = ['day', 'night']
#     # 语言列表
#     language_list = ['English', 'Deutsch', 'Francais']
#     # language_list = ['Svenska']
#
#     # 创建image目录（如果不存在）
#     base_image_dir = BASE_DIR + r"\utils\ImageRecognition\Image_ui\T1GC_PHEV"
#
#     mode = 0
#     mode_dict = {}
#     # 遍历所有组合
#     for day_mode in day_night_list:
#         for language in language_list:
#             mode += 1
#             # 为每个组合创建对应的文件夹
#             folder_name = f"{day_mode}_{language}"
#             folder_path = os.path.join(base_image_dir, folder_name)
#
#             # 创建文件夹（如果不存在）
#             if not os.path.exists(folder_path):
#                 os.makedirs(folder_path)
#
#             logger.info(f"当前测试组合: 模式={day_mode}, 语言={language}")
#
#             mode_dict[str(mode)] = folder_path
#             # # 不需要重新连接设备，只需要确保应用已启动com.desaysv.setting/com.desaysv.setting.view.activity.SettingActivity
#             # driver.app_start(package_name='com.mega.carsettings', activity='.CarSettingActivity')
#
#             # if day_mode == 'day':
#             #     # 获取当前系统语言
#             #     current_locale = os.popen('adb shell settings get system system_locales').read().strip()
#             #     # 语言码和UI显示文本的映射
#             #     locale_to_text = {
#             #         'en-GB': 'English',
#             #         'de': 'Deutsch',
#             #         'fr':'Francais'
#             #     }
#             #     vehicle_language = locale_to_text.get(current_locale, current_locale)
#             #     logger.info(f'下一个需要截图的UI语言:{language}')
#             #     logger.info(f'当前车端语言:{vehicle_language}')
#             #     if vehicle_language == language:
#             #         pass
#             #
#             #     else:
#             #         driver(description='system').click()
#             #         driver.swipe(x, start_y, x, end_y)
#             #
#             #         time.sleep(2)
#             #         driver.xpath(
#             #             '//*[@resource-id="com.mega.carsettings:id/recycler_view"]/android.view.ViewGroup[4]').click()
#             #
#             #         time.sleep(1)
#             #         driver.swipe(change_x,change_y,change_x,change_end_y)
#             #
#             #         time.sleep(1)
#             #
#             #         driver(resourceId='com.mega.carsettings:id/tv_sure').click()
#             #
#             #         time.sleep(5)
#
#             # 将mode和目录路径写入临时 JSON 文件
#             with tempfile.NamedTemporaryFile(delete=False, suffix='.json', mode='w', encoding='utf-8') as f:
#                 json.dump(mode_dict, f)
#                 mode_dict_path = f.name
#
#             pytest.main([
#                 f'--mode={mode}',
#                 f'--mode_dict_path={mode_dict_path}',
#                 f'--vehicle_flag={vehicle_flag}'
#             ])
#             logger.info(f"完成测试组合: 语言={language}, 模式={day_mode}")
#             time.sleep(3)  # 等待一段时间再运行下一个组合
#
#             # 新增：切换语言
#             from testcases.EEA4.PageObjects.TestObject import Step_object
#             step_obj = Step_object(driver, mode)
#             # 这里假设language_list中的字符串和switch_languages支持的key一致
#             # 如有不一致可自定义映射
#             lang_key = language.lower()
#             lang_map = {
#                 'english': 'en',
#                 'deutsch': 'de',
#                 'francais': 'fr',
#             }
#             step_obj.switch_languages(lang_map.get(lang_key, lang_key))
#
#             # 将mode和目录路径写入临时 JSON 文件
#
#
# def run_eea4_ui_all_combinations_t1ej(vehicle_flag):
#     """T1GC欧盟截图"""
#     # 只在最外层连接一次设备
#     driver = u2.connect()
#     width, height = driver.window_size()
#     x = int(height * 0.536)
#     start_y = int(width * 0.577)
#     end_y = int(width * 0.422)
#     agree_x = int(height * 0.438)
#     agree_y = int(height * 0.759)
#
#     """运行所有语言和模式组合的测试"""
#     # day_night_list = ['day']
#     day_night_list = ['day', 'night']
#     # 语言列表
#     language_list = ['中文', "Norge", 'Català', 'Suomi', "Lietuviy", "Eesti", "БьлгарсKи", "latviesu"]
#     # language_list = ['Svenska']
#
#     # 创建image目录（如果不存在）
#     base_image_dir = BASE_DIR + r"\utils\ImageRecognition\Image_ui\EEA4"
#
#     mode = 0
#     mode_dict = {}
#
#     # 遍历所有组合
#     for day_mode in day_night_list:
#         # 如果是night模式，先将语言重置为中文
#         if day_mode == 'night':
#             logger.info("切换到night模式前，先将语言重置为English")
#             # 不需要重新连接设备
#             driver.app_start(package_name='com.desaysv.setting', activity='.view.activity.SettingActivity')
#             driver(resourceId='com.desaysv.setting:id/rl_top_menu')[3].click()
#             time.sleep(3)
#             vehicle_language = driver(resourceId='com.desaysv.setting:id/tv_language_muti').get_text()
#             logger.info(f'当前车端语言:{vehicle_language}')
#
#             if vehicle_language != 'English':
#                 logger.info('将语言重置为English')
#                 driver(resourceId='com.desaysv.setting:id/arrow_language').click()
#                 time.sleep(1)
#
#                 # 可能需要多次向下滑动才能找到中文
#                 max_attempts = 20
#                 for attempt in range(max_attempts):
#                     # 向下滑动（从上往下）
#                     driver.swipe(x, end_y, x, start_y)
#                     time.sleep(1)
#
#                 driver(className='android.widget.Button')[0].click()
#                 time.sleep(5)
#                 logger.info('已将语言重置为English，等待5s后语言切换完毕')
#
#             driver(resourceId='com.desaysv.setting:id/rl_top_menu')[2].click()
#             driver(className='android.widget.RadioButton')[2].click()
#             time.sleep(3)
#
#         for language in language_list:
#             mode += 1
#             # 为每个组合创建对应的文件夹
#             folder_name = f"{day_mode}_{language}"
#             folder_path = os.path.join(base_image_dir, folder_name)
#
#             # 创建文件夹（如果不存在）
#             if not os.path.exists(folder_path):
#                 os.makedirs(folder_path)
#
#             logger.info(f"当前测试组合: 模式={day_mode}, 语言={language}")
#
#             mode_dict[str(mode)] = folder_path
#             # 不需要重新连接设备，只需要确保应用已启动com.desaysv.setting/com.desaysv.setting.view.activity.SettingActivity
#             # driver.app_start(package_name='com.desaysv.setting', activity='.view.activity.SettingActivity')
#             driver.app_start(package_name='com.desaysv.setting')
#
#             if day_mode == 'day':
#                 driver(resourceId='com.desaysv.setting:id/rl_top_menu')[10].click()
#                 time.sleep(3)
#                 driver(resourceId='com.desaysv.setting:id/rl_language').click()
#                 current_locale = os.popen('adb shell settings get system system_locales').read().strip()
#                 # 语言码和UI显示文本的映射
#                 locale_to_text = {
#                     'en-GB': 'English',
#                     'zh': '中文',
#                     'ca': '加泰罗尼亚',
#                     'fi': '芬兰',
#                     "lt": "立陶宛",
#                     "et": "爱沙尼亚语",
#                     "bg": "保加利亚语",
#                     "lv": "拉脱维亚语"
#                 }
#                 vehicle_language = locale_to_text.get(current_locale, current_locale)
#
#                 # vehicle_language = driver(resourceId='com.desaysv.setting:id/tv_language_muti').get_text()
#                 logger.info(f'下一个需要截图的UI语言:{language}')
#                 logger.info(f'当前车端语言:{vehicle_language}')
#
#                 if vehicle_language == language:
#                     pass
#                 else:
#                     time.sleep(2)
#                     driver.swipe(x, start_y, x, end_y + 100)
#                     time.sleep(1)
#                     # 点击切换同意按钮
#                     # driver.click(agree_x,agree_y)
#                     driver(className='android.widget.Button')[0].click()
#                     time.sleep(5)
#
#             if day_mode == 'night':
#                 driver(resourceId='com.desaysv.setting:id/rl_top_menu')[3].click()
#                 time.sleep(3)
#                 current_locale = os.popen('adb shell settings get system system_locales').read().strip()
#                 # 语言码和UI显示文本的映射
#                 locale_to_text = {
#                     'en-GB': 'English',
#                     'zh': '中文',
#                     'ca': '加泰罗尼亚',
#                     'fi': '芬兰',
#                     "lt": "立陶宛",
#                     "et": "爱沙尼亚语",
#                     "bg": "保加利亚语",
#                     "lv": "拉脱维亚语"
#                 }
#                 vehicle_language = locale_to_text.get(current_locale, current_locale)
#                 logger.info(f'下一个要截图的语言:{language}')
#                 logger.info(f'当前车端语言:{vehicle_language}')
#                 if vehicle_language == language:
#                     pass
#                 else:
#                     # driver(resourceId='com.desaysv.setting:id/arrow_language').click()
#                     time.sleep(1)
#                     driver.swipe(x, start_y, x, end_y)
#                     time.sleep(1)
#                     # 点击切换同意按钮
#                     # driver.click(agree_x,agree_y)
#                     driver(className='android.widget.Button')[0].click()
#                     time.sleep(5)
#
#             # 将mode和目录路径写入临时 JSON 文件
#             with tempfile.NamedTemporaryFile(delete=False, suffix='.json', mode='w', encoding='utf-8') as f:
#                 json.dump(mode_dict, f)
#                 mode_dict_path = f.name
#
#             pytest.main([
#                 f'--mode={mode}',
#                 f'--mode_dict_path={mode_dict_path}',
#                 f'--vehicle_flag={vehicle_flag}'
#             ])
#             logger.info(f"完成测试组合: 语言={language}, 模式={day_mode}")
#             time.sleep(3)  # 等待一段时间再运行下一个组合
#

def ui_all_combinations(selected_vehicle='EU', vehicle_flag="PHEV", languages=None):
    """
    运行所有语言和模式组合的测试
    """
    try:
        logger.info(f"开始执行测试，选择地区: {selected_vehicle}")

        # 定义地区标志位映射
        vehicle_flag_map = {
            'EU': 1,  # 欧盟
            'SA': 2,  # 沙特
            'CA': 3,  # 中亚
            'RU': 4,  # 俄罗斯
        }
        vehicle_type_map = {
            "PHEV": 1001,
            "ICE": 1002  # 油车
        }

        # 获取地区标志位
        vehicle_area = vehicle_flag_map.get(selected_vehicle)
        # 获取车型类别
        vehicle_flag = vehicle_type_map.get(vehicle_flag)

        logger.info(f"当前地区为：{selected_vehicle}，当前车辆类型为：{vehicle_flag}")

        # 如果 languages 为空，使用默认全量语言
        if not languages:
            logger.info("未指定语言，使用默认全量语言列表")
            # 默认全量语言列表
            languages = [
                {"English (United Kingdom)": "英语（英国）"},
                {"中文（中国）": "中文（中国）"},
                {"العربية (المملكة العربية السعودية)": "阿拉伯语（沙特阿拉伯）"},
                {"español (España)": "西班牙语（西班牙）"},
                {"Deutsch (Deutschland)": "德语（德国）"},
                {"français (France)": "法语（法国）"},
                {"italiano (Italia)": "意大利语（意大利）"},
                {"Nederlands (Nederland)": "荷兰语（荷兰）"},
                {"norsk (Norge)": "挪威尼诺斯克语（挪威）"},
                {"norsk bokmål (Norge)": "挪威书面语（挪威）"},
                {"português (Portugal)": "葡萄牙语（葡萄牙）"},
                {"svenska (Sverige)": "瑞典语（瑞典）"},
                {"Türkçe (Türkiye)": "土耳其语（土耳其）"},
                {"русский (Россия)": "俄语（俄罗斯）"},
                {"polski (Polska)": "波兰语（波兰）"},
                {"فارسی (ایران)": "波斯语（伊朗）"},
                {"dansk (Danmark)": "丹麦语（丹麦）"},
                {"Ελληνικά (Ελλάδα)": "希腊语（希腊）"},
                {"magyar (Magyarország)": "匈牙利语（匈牙利）"},
                {"română (România)": "罗马尼亚语（罗马尼亚）"},
                {"slovenščina (Sierra Leone)": "斯洛文尼亚语（塞拉利昂）"},
                {"hrvatski (Hrvatska)": "克罗地亚语（克罗地亚）"},
                {"català (Espanya)": "加泰罗尼亚语（西班牙）"},
                {"čeština (Česko)": "捷克语（捷克）"},
                {"slovenčina (Slovensko)": "斯洛伐克语（斯洛伐克）"},
                {"lietuvių (Lietuva)": "立陶宛语（立陶宛）"},
                {"latviešu (Latvija)": "拉脱维亚语（拉脱维亚）"},
                {"suomi (Suomi)": "芬兰语（芬兰）"},
                {"eesti (Eesti)": "爱沙尼亚语（爱沙尼亚）"},
                {"български (България)": "保加利亚语（保加利亚）"},
            ]

        logger.info(f"开始遍历语言列表，共 {len(languages)} 个语言")
        # 遍历 languages
        for i, lang_dict in enumerate(languages):
            try:
                logger.info(f"处理第 {i + 1}/{len(languages)} 个语言")
                if isinstance(lang_dict, dict):
                    for language, folder_label in lang_dict.items():
                        logger.info(f"执行语言: {language} -> {folder_label}")
                        run_eea4_ui_all_combinations_apk_change_languaget1gc(vehicle_flag, language)
                elif isinstance(lang_dict, str):
                    # 前端传递的直接是字符串
                    logger.info(f"执行语言: {lang_dict}")
                    run_eea4_ui_all_combinations_apk_change_languaget1gc(vehicle_flag, lang_dict)
            except Exception as e:
                logger.error(f"处理第 {i + 1} 个语言时出错: {str(e)}")
                logger.error(f"异常详情: {traceback.format_exc()}")
                # 继续处理下一个语言，不中断整个测试
                continue

        logger.info("所有语言测试完成")

    except Exception as e:
        logger.error(f"ui_all_combinations 执行失败: {str(e)}")
        logger.error(f"异常类型: {type(e).__name__}")
        logger.error(f"异常详情: {traceback.format_exc()}")
        # 重新抛出异常，让上层处理
        raise


def run_eea4_ui_all_combinations_apk_change_languaget1gc(vehicle_flag, language_value):
    try:
        logger.info(f"开始执行语言测试: {language_value}")
        EngineeringPassword().get_password()
        """T1GC欧盟截图"""
        # 只在最外层连接一次设备
        driver = u2.connect()
        width, height = driver.window_size()
        x = int(height * 0.626)
        start_y = int(width * 0.854)
        end_y = int(width * 0.100)

        day_night_list = ['day']
        # 保留key-value映射
        language_list = [
            {"English (United Kingdom)": "英语（英国）"},
            {"中文 (中国)": "中文（中国）"},
            {"العربية (المملكة العربية السعودية)": "阿拉伯语（沙特阿拉伯）"},
            {"español (España)": "西班牙语（西班牙）"},
            {"Deutsch (Deutschland)": "德语（德国）"},
            {"français (France)": "法语（法国）"},
            {"italiano (Italia)": "意大利语（意大利）"},
            {"Nederlands (Nederland)": "荷兰语（荷兰）"},
            {"norsk (Norge)": "挪威尼诺斯克语（挪威）"},
            {"norsk bokmål (Norge)": "挪威书面语（挪威）"},
            {"português (Portugal)": "葡萄牙语（葡萄牙）"},
            {"svenska (Sverige)": "瑞典语（瑞典）"},
            {"Türkçe (Türkiye)": "土耳其语（土耳其）"},
            {"русский (Россия)": "俄语（俄罗斯）"},
            {"polski (Polska)": "波兰语（波兰）"},
            {"فارسی (ایران)": "波斯语（伊朗）"},
            {"dansk (Danmark)": "丹麦语（丹麦）"},
            {"Ελληνικά (Ελλάδα)": "希腊语（希腊）"},
            {"magyar (Magyarország)": "匈牙利语（匈牙利）"},
            {"română (România)": "罗马尼亚语（罗马尼亚）"},
            {"slovenščina (Sierra Leone)": "斯洛文尼亚语（塞拉利昂）"},
            {"hrvatski (Hrvatska)": "克罗地亚语（克罗地亚）"},
            {"català (Espanya)": "加泰罗尼亚语（西班牙）"},
            {"čeština (Česko)": "捷克语（捷克）"},
            {"slovenčina (Slovensko)": "斯洛伐克语（斯洛伐克）"},
            {"lietuvių (Lietuva)": "立陶宛语（立陶宛）"},
            {"latviešu (Latvija)": "拉脱维亚语（拉脱维亚）"},
            {"suomi (Suomi)": "芬兰语（芬兰）"},
            {"eesti (Eesti)": "爱沙尼亚语（爱沙尼亚）"},
            {"български (България)": "保加利亚语（保加利亚）"},

        ]
        # 反查key
        language_key = None
        for lang_dict in language_list:
            for k, v in lang_dict.items():
                if v == language_value:
                    language_key = k
                    break
            if language_key:
                break
        if not language_key:
            logger.warning(f"未找到语言映射: {language_value}，将直接用value作为key")
            language_key = language_value

        base_image_dir = BASE_DIR + r"\static\images\T22PHEV"
        mode = 0
        mode_dict = {}
        for day_mode in day_night_list:
            try:
                mode += 1
                folder_name = f"{day_mode}_{language_value}"
                folder_path = os.path.join(base_image_dir, folder_name)
                if not os.path.exists(folder_path):
                    os.makedirs(folder_path)
                logger.info(f"当前测试组合: 模式={day_mode}, 语言={language_key}")
                mode_dict[str(mode)] = folder_path

                logger.info('删除app的数据库文件')
                adb.delete_app_databases()
                time.sleep(3)
                driver.app_stop(FOUR_APP_PACKAGE)
                time.sleep(3)
                driver.app_start(FOUR_APP_PACKAGE)
                if language_key == 'English (United Kingdom)':
                    pass
                else:
                    time.sleep(3)
                    from testcases.EEA4.PageObjects.TestObject import Step_object
                    step_obj = Step_object(driver, mode, mode_dict, language=language_key)
                    time.sleep(3)
                    logger.info(f"切换语言: {language_key}")
                    step_obj.switch_languages(language_key)

                with tempfile.NamedTemporaryFile(delete=False, suffix='.json', mode='w', encoding='utf-8') as f:
                    json.dump(mode_dict, f)
                    mode_dict_path = f.name

                logger.info(f"开始执行pytest测试")
                pytest.main([
                    f'--mode={mode}',
                    f'--mode_dict_path={mode_dict_path}',
                    f'--vehicle_flag={vehicle_flag}',
                    f'--language={language_key}',
                ])
                logger.info(f"完成测试组合: 语言={language_key}, 模式={day_mode}")
                time.sleep(3)  # 等待一段时间再运行下一个组合

            except Exception as e:
                logger.error(f"执行模式 {day_mode} 时出错: {str(e)}")
                logger.error(f"异常详情: {traceback.format_exc()}")
                # 继续执行下一个模式
                continue

    except Exception as e:
        logger.error(f"run_eea4_ui_all_combinations_apk_change_languaget1gc 执行失败: {str(e)}")
        logger.error(f"异常类型: {type(e).__name__}")
        logger.error(f"异常详情: {traceback.format_exc()}")
        # 重新抛出异常，让上层处理
        raise


if __name__ == "__main__":
    if SWITCH:
        # ui截图逻辑
        app = create_app()
        # 配置日志
        import logging

        log = logging.getLogger('werkzeug')
        log.setLevel(logging.DEBUG)
        time.sleep(5)
        # 启动应用
        os.system("start http://127.0.0.1:5000/")
        app.run(
            host=APIConfig.HOST,  # 关闭调试模式，防止自动重启
            port=APIConfig.PORT,
            debug=APIConfig.DEBUG,
            threaded=APIConfig.THREADED,
            use_reloader=False  # 禁用自动重载
        )

    else:
        # # 自动化用例执行
        # allure_path = f"./testreport/Report/{int(time.time())}"
        # os.makedirs(allure_path, exist_ok=True)
        # # 运行测试
        # pytest.main(['-vs', "-k", "test_A", '--alluredir', allure_path, "--clean-alluredir"])
        # # 直接启动 allure-http.exe
        import subprocess
        # import webbrowser
        # 自动化用例执行
        allure_path = f"./testreport/Report/{int(time.time())}"
        os.makedirs(allure_path, exist_ok=True)
        pytest.main(['-vs', "-k", "test_A", '--alluredir', "./testreport/Report/tmp", "--clean-alluredir"])
        subprocess.Popen(f"allure generate ./testreport/Report/tmp -o {allure_path} --clean")
        subprocess.Popen(r"copy .\testreport\Report\allure-http.exe %s" % (allure_path.replace("/", "\\")))
        subprocess.Popen(r"cd %s&&allure-http.exe" % (allure_path.replace("/", "\\")))
        subprocess.Popen("start http://127.0.0.1:8080/")

        # allure_http_exe = r".\testreport\Report\allure-http.exe"
        # subprocess.Popen([allure_http_exe], cwd=allure_path)
        # webbrowser.open("http://127.0.0.1:8080/")
