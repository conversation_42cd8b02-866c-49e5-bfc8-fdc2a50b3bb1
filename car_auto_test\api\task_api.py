'''云端任务下发API'''
from flask import Blueprint, jsonify, request
from datetime import datetime
from config.api_config import APIConfig
from utils.LoggingSystem.Logger import logger


# 创建蓝图
task_bp = Blueprint('task', __name__)

@task_bp.route('/api/dispatch_task', methods=['POST'])
def dispatch_task():
    """云端下发任务API"""
    try:
        data = request.json
        # logger.info(data)
        #{'deviceId': '123456', 'carModel': 'EEA5',
        # 'testCases': [{'id': 'test_A001', 'description': '预期结果我会制造失败'}, {'id': 'test_A002', 'description': '预期结果我会制造成功'}]}

        device_id = data.get('deviceId')
        car_model = data.get('carModel')

        test_cases = data.get('testCases', [])

        if not device_id or not car_model or not test_cases:
            return jsonify({
                'status': 'error',
                'message': '缺少必要参数'
            }), 400

        APIConfig.cloud_tasks[device_id] = {
            'carModel': car_model,
            'testCases': test_cases,
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        return jsonify({
            'status': 'success',
            'message': '任务下发成功'
        }), 200

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'任务下发失败: {str(e)}'
        }), 500


@task_bp.route('/api/check_task', methods=['GET'])
def check_task():
    """Qauto检查任务API"""
    try:
        device_id = request.args.get('deviceId')
        car_model = request.args.get('carModel')

        if not device_id or not car_model:
            return jsonify({
                'status': 'error',
                'message': '缺少设备ID或车型信息'
            }), 400

        # 检查是否存在对应的任务
        if device_id in APIConfig.cloud_tasks:
            task = APIConfig.cloud_tasks[device_id]
            # {'carModel': 'EEA5', 'testCases': ['test_A001', 'test_A002'], 'timestamp': '2025-06-05 14:42:18'}

            logger.info(task)

            # 验证车型是否匹配
            if task['carModel'] == car_model:
                # logger.info(task['testCases'])
                logger.info(task)


                return jsonify({
                    'status': 'success',
                    'data': {
                        'testCases': task['testCases'],
                        'timestamp': task['timestamp']
                    }
                }), 200

            else:
                return jsonify({
                    'status': 'error',
                    'message': '车型不匹配'
                }), 400
        else:
            return jsonify({
                'status': 'error',
                'message': '未找到对应的任务'
            }), 404

    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'检查任务失败: {str(e)}'
        }), 500

