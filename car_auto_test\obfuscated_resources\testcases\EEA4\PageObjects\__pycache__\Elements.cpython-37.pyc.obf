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