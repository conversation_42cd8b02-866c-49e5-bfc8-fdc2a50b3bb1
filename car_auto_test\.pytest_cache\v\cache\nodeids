["testcases/EEA4/TestChanxian/test_A001.py::test_A001", "testcases/EEA4/UICases/test_01_before_update_process.py::Test_Bench_Before_Update_Process::test_before_update_process_001", "testcases/EEA4/UICases/test_01_before_update_process.py::Test_Bench_Before_Update_Process::test_before_update_process_002", "testcases/EEA4/UICases/test_01_before_update_process.py::Test_Bench_Before_Update_Process::test_bench_before_update_process_001", "testcases/EEA4/UICases/test_02_preditions.py::Test_Bench_Preditions::test_11", "testcases/EEA4/UICases/test_02_preditions.py::Test_Bench_Preditions::test_bench_preditions_001", "testcases/EEA4/UICases/test_02_preditions.py::Test_Bench_Preditions::test_bench_preditions_002", "testcases/EEA4/UICases/test_02_preditions.py::Test_Bench_Preditions::test_preditions_001", "testcases/EEA4/UICases/test_02_preditions.py::Test_Bench_Preditions::test_preditions_002", "testcases/EEA4/UICases/test_03_reservation.py::Test_Bench_Reservation::test11", "testcases/EEA4/UICases/test_03_reservation.py::Test_Bench_Reservation::test_reservation_001", "testcases/EEA4/UICases/test_03_reservation.py::Test_Bench_Reservation::test_reservation_002", "testcases/EEA4/UICases/test_03_reservation.py::Test_Bench_Reservation::test_reservation_003", "testcases/EEA4/UICases/test_03_reservation.py::Test_Bench_Reservation::test_reservation_004", "testcases/EEA4/UICases/test_03_reservation.py::Test_Bench_Reservation::test_reservation_005", "testcases/EEA4/UICases/test_03_reservation.py::Test_Bench_Reservation::test_reservation_006", "testcases/EEA4/UICases/test_04_update_result.py::Test_Bench_Update_Result::test_bench_update_result_003", "testcases/EEA4/UICases/test_04_update_result.py::Test_Bench_Update_Result::test_bench_update_result_006", "testcases/EEA4/UICases/test_04_update_result.py::Test_Bench_Update_Result::test_update_result_001", "testcases/EEA4/UICases/test_04_update_result.py::Test_Bench_Update_Result::test_update_result_002", "testcases/EEA4/UICases/test_04_update_result.py::Test_Bench_Update_Result::test_update_result_004", "testcases/EEA4/UICases/test_04_update_result.py::Test_Bench_Update_Result::test_update_result_005", "testcases/EEA4/UICases/test_05_task_time_fail.py::Test_Task_TIME_FAIL::test_not_tomorrow_task", "testcases/EEA4/UICases/test_05_task_time_fail.py::Test_Task_TIME_FAIL::test_task_time_fail"]