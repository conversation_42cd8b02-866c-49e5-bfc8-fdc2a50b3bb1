/* =============== 全局配置对象 =============== */
const CONFIG = {
    itemsPerPage: 10,     // 每页显示条目数
    refreshInterval: 3000 // 自动刷新间隔(ms)
};

/* =============== 状态管理系统 =============== */
const StateManager = {
    // 存储测试数据
    testData: [],          // 原始测试数据
    filteredData: [],      // 筛选后的数据
    currentPage: 1,        // 当前页码
    currentSort: {         // 当前排序状态
        column: 'id',
        direction: 'asc'
    },
    filters: {             // 当前筛选条件
        language: '',
        mode: ''
    },
    currentImageIndex: -1, // 当前选中图片索引
    refreshTimer: null     // 自动刷新定时器
};

/* =============== DOM元素缓存 =============== */
const DOM = {
    // 控制按钮
    startTestBtn: document.getElementById('startTestBtn'),
    stopTestBtn: document.getElementById('stopTestBtn'),
    statusDot: document.getElementById('statusDot'),
    statusText: document.getElementById('statusText'),
    
    // 表格相关
    testTableBody: document.getElementById('testTableBody'),
    pagination: document.getElementById('pagination'),
    pageInfo: document.getElementById('pageInfo'),
    totalInfo: document.getElementById('totalInfo'),
    prevPageBtn: document.getElementById('prevPage'),
    nextPageBtn: document.getElementById('nextPage'),
    
    // 图片预览
    imagePreview: document.getElementById('imagePreview'),
    lastUpdate: document.getElementById('lastUpdate')
};

/* =============== 表格管理模块 =============== */
const TableManager = {
    /**
     * 初始化表格事件监听器
     */
    initEventListeners() {
        // 绑定排序点击事件
        document.querySelectorAll('.sortable').forEach(th => {
            th.addEventListener('click', () => this.handleSort(th));
        });

        // 绑定筛选点击事件
        document.querySelectorAll('.filterable').forEach(th => {
            th.addEventListener('click', (e) => this.handleFilter(th, e));
        });

        // 绑定分页按钮
        DOM.prevPageBtn.addEventListener('click', () => this.changePage(-1));
        DOM.nextPageBtn.addEventListener('click', () => this.changePage(1));
    },

    /**
     * 处理表格排序
     * @param {HTMLElement} header - 被点击的表头元素
     */
    handleSort(header) {
        // 更新排序状态
        StateManager.currentSort = {
            column: header.dataset.sort,
            direction: 'asc' // 当前仅支持升序
        };

        // 更新排序指示器
        document.querySelectorAll('.test-table th').forEach(th => {
            th.classList.remove('sort-asc');
        });
        header.classList.add('sort-asc');

        // 重置到第一页并更新表格
        StateManager.currentPage = 1;
        this.updateTable();
    },

    /**
     * 处理表格筛选
     * @param {HTMLElement} header - 被点击的表头元素
     * @param {Event} event - 点击事件对象
     */
    handleFilter(header, event) {
        const filterType = header.dataset.filter;
        const rect = header.getBoundingClientRect();
        
        // 移除其他已显示的筛选下拉框
        document.querySelectorAll('.filter-dropdown').forEach(dropdown => {
            if (dropdown.dataset.filter !== filterType) {
                dropdown.classList.remove('show');
            }
        });

        // 创建或获取筛选下拉框
        let dropdown = document.querySelector(`.filter-dropdown[data-filter="${filterType}"]`);
        if (!dropdown) {
            dropdown = this.createFilterDropdown(filterType);
        } else {
            this.updateFilterDropdown(dropdown, filterType);
        }

        // 定位下拉框
        dropdown.style.top = `${rect.bottom + window.scrollY}px`;
        dropdown.style.left = `${rect.left + window.scrollX}px`;
        dropdown.classList.toggle('show');

        // 点击其他地方关闭下拉框
        const closeDropdown = (e) => {
            if (!dropdown.contains(e.target) && !header.contains(e.target)) {
                dropdown.classList.remove('show');
                document.removeEventListener('click', closeDropdown);
            }
        };
        document.addEventListener('click', closeDropdown);
    },

    /**
     * 创建筛选下拉框
     * @param {string} filterType - 筛选类型
     * @returns {HTMLElement} 创建的下拉框元素
     */
    createFilterDropdown(filterType) {
        const dropdown = document.createElement('div');
        dropdown.className = 'filter-dropdown';
        dropdown.dataset.filter = filterType;
        document.body.appendChild(dropdown);

        // 获取唯一值
        const uniqueValues = [...new Set(StateManager.testData.map(item => item[filterType]))];
        
        // 添加"全部"选项
        this.addFilterItem(dropdown, '全部', '', filterType);

        // 添加其他选项
        uniqueValues.forEach(value => {
            this.addFilterItem(dropdown, value, value, filterType);
        });

        return dropdown;
    },

    /**
     * 更新筛选下拉框内容
     * @param {HTMLElement} dropdown - 下拉框元素
     * @param {string} filterType - 筛选类型
     */
    updateFilterDropdown(dropdown, filterType) {
        dropdown.innerHTML = '';
        const uniqueValues = [...new Set(StateManager.testData.map(item => item[filterType]))];
        
        this.addFilterItem(dropdown, '全部', '', filterType);
        uniqueValues.forEach(value => {
            this.addFilterItem(dropdown, value, value, filterType);
        });
    },

    /**
     * 添加筛选选项
     * @param {HTMLElement} dropdown - 下拉框元素
     * @param {string} text - 显示文本
     * @param {string} value - 选项值
     * @param {string} filterType - 筛选类型
     */
    addFilterItem(dropdown, text, value, filterType) {
        const item = document.createElement('div');
        item.className = 'filter-item' + (StateManager.filters[filterType] === value ? ' active' : '');
        item.textContent = text;
        item.onclick = (e) => {
            e.stopPropagation();
            StateManager.filters[filterType] = value;
            this.updateFilters(filterType);
            dropdown.classList.remove('show');
        };
        dropdown.appendChild(item);
    },

    /**
     * 更新筛选条件
     * @param {string} filterType - 筛选类型
     */
    updateFilters(filterType) {
        // 更新选中状态
        document.querySelectorAll(`.filter-dropdown[data-filter="${filterType}"] .filter-item`).forEach(item => {
            item.classList.toggle('active', item.textContent === (StateManager.filters[filterType] || '全部'));
        });

        // 重置到第一页并更新表格
        StateManager.currentPage = 1;
        this.updateTable();
    },

    /**
     * 更新表格显示
     */
    updateTable() {
        if (StateManager.testData.length === 0) {
            DOM.testTableBody.innerHTML = '<tr><td colspan="7" class="no-image">暂无测试数据</td></tr>';
            DOM.pagination.style.display = 'none';
            return;
        }

        // 应用筛选
        StateManager.filteredData = StateManager.testData.filter(item => {
            return (!StateManager.filters.language || item.language === StateManager.filters.language) &&
                   (!StateManager.filters.mode || item.mode === StateManager.filters.mode);
        });

        // 应用排序
        if (StateManager.currentSort.column === 'id') {
            StateManager.filteredData.sort((a, b) => {
                return parseInt(a.id) - parseInt(b.id);
            });
        }
        
        // 计算当前页的数据
        const start = (StateManager.currentPage - 1) * CONFIG.itemsPerPage;
        const end = start + CONFIG.itemsPerPage;
        const pageData = StateManager.filteredData.slice(start, end);

        // 更新表格内容
        DOM.testTableBody.innerHTML = pageData.map((item, index) => {
            const timestamp = new Date(item.timestamp);
            const simplifiedTime = `${timestamp.getHours().toString().padStart(2, '0')}:${timestamp.getMinutes().toString().padStart(2, '0')}:${timestamp.getSeconds().toString().padStart(2, '0')}`;
            const scenario = item.scenario.length > 50 ? 
                item.scenario.substring(0, 50) + '...' : 
                item.scenario;

            return `
                <tr class="${start + index === StateManager.currentImageIndex ? 'active' : ''}" 
                    onclick="ImageManager.selectRow(${start + index})"
                    title="${item.scenario}">
                    <td>${item.id}</td>
                    <td><span class="badge badge-primary">EEA5.0</span></td>
                    <td>${item.language}</td>
                    <td>${item.mode}</td>
                    <td>${scenario}</td>
                    <td>${simplifiedTime}</td>
                    <td><span class="badge badge-success">成功</span></td>
                </tr>
            `;
        }).join('');

        // 更新分页信息
        this.updatePagination();
    },

    /**
     * 更新分页信息
     */
    updatePagination() {
        if (StateManager.filteredData.length <= CONFIG.itemsPerPage) {
            DOM.pagination.style.display = 'none';
            return;
        }

        DOM.pagination.style.display = 'flex';
        const totalPages = Math.ceil(StateManager.filteredData.length / CONFIG.itemsPerPage);
        StateManager.currentPage = Math.min(Math.max(StateManager.currentPage, 1), totalPages);
        
        DOM.pageInfo.textContent = `第 ${StateManager.currentPage} 页，共 ${totalPages} 页`;
        DOM.totalInfo.textContent = `共 ${StateManager.filteredData.length} 条记录`;

        DOM.prevPageBtn.disabled = StateManager.currentPage <= 1;
        DOM.nextPageBtn.disabled = StateManager.currentPage >= totalPages;
    },

    /**
     * 切换页面
     * @param {number} direction - 页面切换方向（-1: 上一页, 1: 下一页）
     */
    changePage(direction) {
        const totalPages = Math.ceil(StateManager.filteredData.length / CONFIG.itemsPerPage);
        const newPage = StateManager.currentPage + direction;

        if (newPage >= 1 && newPage <= totalPages) {
            StateManager.currentPage = newPage;
            this.updateTable();
        }
    }
};

/* =============== 图片管理模块 =============== */
const ImageManager = {
    /**
     * 选择表格行并更新图片预览
     * @param {number} index - 选中的行索引
     */
    selectRow(index) {
        StateManager.currentImageIndex = index;
        TableManager.updateTable();
        this.updateImagePreview(StateManager.testData[index]);
    },

    /**
     * 更新图片预览
     * @param {Object} data - 图片数据对象
     */
    updateImagePreview(data) {
        if (!data) return;

        StateManager.currentImageIndex = StateManager.testData.indexOf(data);

        // 构建图片URL
        let imagePath = data.image_path;
        
        // 处理图片路径
        if (imagePath.includes('Image_ui')) {
            const parts = imagePath.split('Image_ui');
            if (parts.length > 1) {
                imagePath = '/static/images/' + parts[1].replace(/\\/g, '/').replace(/^\//, '');
            }
        } else if (!imagePath.startsWith('/static/')) {
            imagePath = `/static/images/${data.mode}_${data.language}/${data.image_name}`;
        }

        console.log('Image path:', imagePath);

        // 更新预览区域HTML
        DOM.imagePreview.innerHTML = `
            <div class="image-info">
                <h4>${data.scenario}</h4>
                <p><strong>车型:</strong> EEA5.0</p>
                <p><strong>语言:</strong> ${data.language}</p>
                <p><strong>模式:</strong> ${data.mode}</p>
                <p><strong>时间:</strong> ${data.timestamp}</p>
                <p><strong>图片路径:</strong> ${imagePath}</p>
            </div>
            <div class="image-container">
                <img src="${imagePath}" alt="${data.image_name}" class="preview-image"
                     onload="this.style.display='block'; this.nextElementSibling.style.display='none';"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block'; console.log('Image load error:', this.src);">
                <div class="image-error" style="display: none;">
                    <p><strong>图片加载失败</strong></p>
                    <p>路径: ${imagePath}</p>
                    <p>请检查图片文件是否存在</p>
                </div>
            </div>
        `;
    }
};

/* =============== 测试控制模块 =============== */
const TestController = {
    /**
     * 初始化测试控制
     */
    initControls() {
        DOM.startTestBtn.addEventListener('click', () => this.startTest());
        DOM.stopTestBtn.addEventListener('click', () => this.stopTest());
    },

    /**
     * 开始测试
     */
    async startTest() {
        try {
            console.log('开始发送启动测试请求...');
            const response = await fetch('/api/start_test', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            console.log('收到服务器响应:', response.status, response.statusText);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            console.log('解析响应数据:', data);
            
            if (data.status === 'success') {
                console.log('测试启动成功，开始自动刷新');
                TestController.startAutoRefresh();
                // 更新UI状态
                TestController.updateTestStatus('running', '测试进行中...');
            } else {
                console.warn('服务器返回非成功状态:', data.message);
                alert(data.message || '启动测试失败');
            }
        } catch (error) {
            console.error('启动测试过程中发生错误:', error);
            if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
                alert('网络连接失败，请检查网络连接');
            } else {
                alert(`启动测试失败: ${error.message}`);
            }
        }
    },

    /**
     * 停止测试
     */
    async stopTest() {
        try {
            const response = await fetch('/api/stop_test', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            const data = await response.json();
            console.log('测试已停止');
            TestController.stopAutoRefresh();
        } catch (error) {
            console.error('停止测试失败:', error);
        }
    },

    /**
     * 开始自动刷新
     */
    startAutoRefresh() {
        console.log('开始自动刷新...');
        if (StateManager.refreshTimer) {
            clearInterval(StateManager.refreshTimer);
        }
        // 使用箭头函数保持this指向
        StateManager.refreshTimer = setInterval(() => {
            console.log('执行自动刷新...');
            this.fetchTestStatus();
        }, CONFIG.refreshInterval);
    },

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        console.log('停止自动刷新...');
        if (StateManager.refreshTimer) {
            clearInterval(StateManager.refreshTimer);
            StateManager.refreshTimer = null;
        }
    },

    /**
     * 获取测试状态
     */
    async fetchTestStatus() {
        try {
            console.log('获取测试状态...');
            const response = await fetch('/api/test_status');
            const data = await response.json();
            console.log('测试状态数据:', data);

            // 更新状态
            let statusText = '系统就绪';
            if (data.test_status === 'running') {
                statusText = '测试进行中...';
            } else if (data.test_status === 'completed') {
                statusText = '测试已完成';
                // 不要停止自动刷新，继续显示数据
                // this.stopAutoRefresh();
            } else if (data.test_status === 'failed') {
                statusText = '测试失败';
                // 不要停止自动刷新，继续显示数据
                // this.stopAutoRefresh();
            }

            this.updateTestStatus(data.test_status, statusText);

            // 更新测试数据
            if (data.screenshots) {
                console.log('当前截图数量:', data.screenshots.length, '已存储数量:', StateManager.testData.length);
                if (data.screenshots.length !== StateManager.testData.length) {
                    console.log('更新测试数据...');
                    StateManager.testData = data.screenshots;
                    StateManager.filteredData = [...data.screenshots];
                    TableManager.updateTable();

                    // 自动显示最新截图
                    if (StateManager.testData.length > 0) {
                        const latestScreenshot = StateManager.testData[StateManager.testData.length - 1];
                        console.log('显示最新截图:', latestScreenshot);
                        ImageManager.updateImagePreview(latestScreenshot);
                    }
                }
            }

            // 更新最后更新时间
            DOM.lastUpdate.textContent = `最后更新: ${new Date().toLocaleTimeString()}`;

            // 如果测试已完成或失败，但数据还在，继续刷新
            if ((data.test_status === 'completed' || data.test_status === 'failed') && 
                data.screenshots && data.screenshots.length > 0) {
                // 保持自动刷新，但降低频率
                if (!StateManager.refreshTimer) {
                    StateManager.refreshTimer = setInterval(() => {
                        this.fetchTestStatus();
                    }, CONFIG.refreshInterval * 2); // 降低刷新频率
                }
            }
        } catch (error) {
            console.error('获取测试状态失败:', error);
        }
    },

    /**
     * 更新测试状态显示
     */
    updateTestStatus(status, text) {
        console.log('更新测试状态:', status, text);
        DOM.statusDot.className = `status-dot status-${status}`;
        DOM.statusText.textContent = text;

        DOM.startTestBtn.disabled = status === 'running';
        DOM.stopTestBtn.disabled = status !== 'running';
    }
};

/* =============== 初始化应用 =============== */
document.addEventListener('DOMContentLoaded', () => {
    console.log('初始化应用...');
    
    // 初始化表格事件监听
    TableManager.initEventListeners();
    
    // 初始化测试控制
    TestController.initControls();
    
    // 获取初始状态
    TestController.fetchTestStatus();
    
    // 如果测试正在运行，开始自动刷新
    setTimeout(() => {
        console.log('检查是否需要启动自动刷新...');
        if (DOM.statusDot.classList.contains('status-running')) {
            console.log('测试正在运行，启动自动刷新');
            TestController.startAutoRefresh();
        }
    }, 1000);
}); 