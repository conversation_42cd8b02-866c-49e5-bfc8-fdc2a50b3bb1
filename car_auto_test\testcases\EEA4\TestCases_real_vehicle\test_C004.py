# coding: utf-8
# Project：car_auto_test
# File：test_C004.py
# Author：杨郑健
# Date ：2025/6/11 15:44
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级主页面/小红点")
@AllureDecorators.title("发行说明：点击详情")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_C004(driver, launch_app):
    """
    测试步骤
    1、检测到升级任务并下载完成
    2、点击发行说明详情后，查看主页面是否有字符并返回

    预期结果
    1、显示的发行说明内容与后台配置的一致，字数过多时显示“详情”,点击返回按键可退出“版本详情”
    """
    objects = Step_object(driver)
    objects.get_task()
    result = objects.check_details_home()
    assert result