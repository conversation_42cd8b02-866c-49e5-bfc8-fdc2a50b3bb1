# coding: utf-8
# Project：car_auto_test
# File：test_A006.py
# Author：杨郑健
# Date ：2025/6/10 14:46
from testcases.EEA4.PageObjects.TestObject import Step_object
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from utils.LoggingSystem.Logger import logger
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.SerialTool.serial_tool import serial_tool
from utils.CloudOperation.chery_operation import Cloud
from utils.ReportGenerator.Allure import *

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("ota诊断的前置条件")
@static_condition(conditions=
{  # obd口占用信号目前有问题未加，后续优化后加上
    "tbox_status": "NOTREAD",
    "power": "READY",
    "task": "normal"
}
)
def test_A006(driver, launch_app):
    """
    测试步骤
    1.切换车辆为用户模式，tbox诊断完成，obd未被占用
    2.进入工程模式点击任务检测按钮
    3.任务检测结束查找车辆用户模式、tbox诊断状态、obd占用状态

    预期结果
    3、发起检测，实时日志中有车辆模式vehicle mode: norml，tbox诊断tel-diagnose: tel_diagnose_not_read，obd未被占用diagnose: diagnose_off状态输出
    """
    objects = Step_object(driver)
    serial_tool.set_file("obd_status", 0)
    get_task_result = objects.get_task()
    results = otaLogAnalysis.find_keywords(["DIAGNOSE_OFF", "TEL_DIAGNOSE_NOT_READ", "vehicle mode: NORMAL"], )
    logger.info(f"实时日志检测结果:{results}")
    assert results
    assert get_task_result