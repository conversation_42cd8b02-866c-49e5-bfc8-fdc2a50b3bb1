# coding: utf-8
# Project：car_auto_test
# File：test_C025.py
# Author：杨郑健
# Date ：2025/6/11 18:34
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级主页面/小红点")
@AllureDecorators.title("免责声明:待“同意”倒计时结束")
@static_condition(conditions=
{
    "power": "ON",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_C025(driver, launch_app):
    """
    测试步骤
    1、进入“升级主页面”，点击“立即升级”
    2、“注意事项”点击“继续升级”，触发“免责声明”
    3、查看“免责声明”内容区域是否可上下滑动查看

    预期结果
    3、可上下滑动查看
    """
    objects = Step_object(driver)
    objects.get_task()
    objects.matter_click()
    objects.swipe_up()
    result = objects.find_textContains_by_text("尊敬的车主")
    assert result == False

