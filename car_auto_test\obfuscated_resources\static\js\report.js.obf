/* =============== 导出报告初始化 =============== */
document.addEventListener('DOMContentLoaded', function() {
    // 初始化状态管理器
    if (window.initialTestData) {
        StateManager.testData = window.initialTestData.screenshots || [];
        StateManager.filteredData = [...StateManager.testData];
        
        // 更新表格
        if (typeof TableManager !== 'undefined') {
            TableManager.updateTable();
            
            // 绑定表格行点击事件
            const tableBody = document.getElementById('testTableBody');
            if (tableBody) {
                tableBody.addEventListener('click', function(e) {
                    const row = e.target.closest('tr');
                    if (row) {
                        const scenario = row.querySelector('td:nth-child(5)').textContent;
                        if (typeof ImageManager !== 'undefined') {
                            ImageManager.filterByScenario(scenario);
                        }
                        
                        // 更新选中状态
                        document.querySelectorAll('#testTableBody tr').forEach(tr => {
                            tr.classList.remove('selected');
                        });
                        row.classList.add('selected');
                    }
                });
            }
        }
        
        // 更新测试状态
        if (typeof TestController !== 'undefined' && window.initialTestData.test_status) {
            const status = window.initialTestData.test_status;
            const statusText = {
                'running': '测试进行中...',
                'completed': '测试已完成',
                'failed': '测试失败',
                'idle': '系统就绪'
            }[status] || '系统就绪';
            
            TestController.updateTestStatus(status, statusText);
        }
        
        // 显示最新截图
        if (typeof ImageManager !== 'undefined' && StateManager.testData.length > 0) {
            const latestScreenshot = StateManager.testData[StateManager.testData.length - 1];
            ImageManager.updateImagePreview(latestScreenshot);
        }
    }
    
    // 禁用开始测试按钮（因为是导出的报告）
    const startTestBtn = document.getElementById('startTestBtn');
    if (startTestBtn) {
        startTestBtn.disabled = true;
        startTestBtn.title = '导出的报告中无法开始新测试';
    }
    
    // 禁用停止测试按钮
    const stopTestBtn = document.getElementById('stopTestBtn');
    if (stopTestBtn) {
        stopTestBtn.disabled = true;
        stopTestBtn.title = '导出的报告中无法停止测试';
    }
});

// 图片网格管理
class ImageGridManager {
    constructor() {
        this.imageGrid = document.getElementById('imageGrid');
        this.imageModal = document.getElementById('imageModal');
        this.modalImage = document.getElementById('modalImage');
        this.modalTitle = document.getElementById('modalTitle');
        this.modalMeta = document.getElementById('modalMeta');
        this.modalClose = document.querySelector('.modal-close');
        
        this.initEventListeners();
    }

    initEventListeners() {
        // 关闭模态框
        this.modalClose.addEventListener('click', () => {
            this.imageModal.classList.remove('show');
        });

        // 点击模态框背景关闭
        this.imageModal.addEventListener('click', (e) => {
            if (e.target === this.imageModal) {
                this.imageModal.classList.remove('show');
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.imageModal.classList.contains('show')) {
                this.imageModal.classList.remove('show');
            }
        });
    }

    // 创建图片卡片
    createImageCard(screenshot) {
        const card = document.createElement('div');
        card.className = 'image-card';
        
        const timestamp = new Date(screenshot.timestamp).toLocaleString();
        const status = this.getStatusClass(screenshot.status);
        
        card.innerHTML = `
            <div class="image-wrapper">
                <img src="${screenshot.image_path}" alt="${screenshot.scene}" 
                     loading="lazy" onerror="this.onerror=null; this.src='/static/images/error.png';">
                ${screenshot.status === 'pending' ? `
                    <div class="image-loading">
                        <div class="loading-spinner"></div>
                    </div>
                ` : ''}
            </div>
            <div class="image-info">
                <div class="image-title" title="${screenshot.scene}">${screenshot.scene}</div>
                <div class="image-meta">
                    <div class="image-status">
                        <span class="status-dot ${status}"></span>
                        <span>${this.getStatusText(screenshot.status)}</span>
                    </div>
                    <div class="image-time">${timestamp}</div>
                </div>
            </div>
        `;

        // 添加点击事件
        card.addEventListener('click', () => this.showImageModal(screenshot));
        
        return card;
    }

    // 更新图片网格
    updateImageGrid(screenshots) {
        if (!screenshots || screenshots.length === 0) {
            this.imageGrid.innerHTML = `
                <div class="no-image">
                    <div style="font-size: 3em; margin-bottom: 15px;">📷</div>
                    <p>暂无截图预览</p>
                    <p>测试开始后，最新的截图将在此处显示</p>
                </div>
            `;
            return;
        }

        // 清空现有内容
        this.imageGrid.innerHTML = '';
        
        // 添加新的图片卡片
        screenshots.forEach(screenshot => {
            const card = this.createImageCard(screenshot);
            this.imageGrid.appendChild(card);
        });
    }

    // 显示图片模态框
    showImageModal(screenshot) {
        this.modalImage.src = screenshot.image_path;
        this.modalTitle.textContent = screenshot.scene;
        
        const timestamp = new Date(screenshot.timestamp).toLocaleString();
        const status = this.getStatusText(screenshot.status);
        
        this.modalMeta.innerHTML = `
            <div class="image-meta">
                <div class="image-status">
                    <span class="status-dot ${this.getStatusClass(screenshot.status)}"></span>
                    <span>${status}</span>
                </div>
                <div class="image-time">${timestamp}</div>
            </div>
            <div class="image-details">
                <p><strong>车型：</strong>${screenshot.car_model || '未知'}</p>
                <p><strong>语言：</strong>${screenshot.language || '未知'}</p>
                <p><strong>模式：</strong>${screenshot.mode || '未知'}</p>
            </div>
        `;
        
        this.imageModal.classList.add('show');
    }

    // 获取状态样式类
    getStatusClass(status) {
        switch (status) {
            case 'success': return 'status-success';
            case 'failed': return 'status-failed';
            case 'pending': return 'status-pending';
            default: return 'status-pending';
        }
    }

    // 获取状态文本
    getStatusText(status) {
        switch (status) {
            case 'success': return '成功';
            case 'failed': return '失败';
            case 'pending': return '进行中';
            default: return '未知';
        }
    }
}

// 初始化图片网格管理器
const imageGridManager = new ImageGridManager();

// 更新图片预览
function updateImagePreview(screenshots) {
    imageGridManager.updateImageGrid(screenshots);
}

// 导出函数供其他模块使用
window.updateImagePreview = updateImagePreview;
