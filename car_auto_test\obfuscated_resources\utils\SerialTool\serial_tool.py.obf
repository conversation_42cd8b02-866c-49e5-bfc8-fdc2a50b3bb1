import json
import time
import serial
import serial.tools.list_ports
from utils.LoggingSystem.Logger import logger
from config.Config import *



def find_usable_port(baudrate=9600, timeout=3.0):
    """
    自动检测可用串口并返回第一个可用的连接对象
    """
    ports = serial.tools.list_ports.comports()
    logger.info(f"ports: {ports}")

    for port_info in ports:
        logger.info(f"port: {port_info}")
        port = None
        try:
            # 尝试建立连接
            port = serial.Serial(
                port=port_info.device,
                baudrate=baudrate,
                timeout=timeout
            )

            # 发送测试命令
            test_command = 'ls\r\n'.encode('utf-8')  # 使用utf-8格式
            port.write(test_command)
            time.sleep(1)

            # 检查响应（至少需要等待数据到达）
            response = port.read(500)
            logger.info(f"response: {response}")
            resp_info = str(response).lower()

            # 如果有响应则认为可用
            if 'ls' in resp_info or 'cannot execute' in resp_info:
                logger.info(f"发现可用串口: {port_info.device}")
                return port
            else:
                port.close()
                return None
        except (serial.SerialException, OSError) as e:
            logger.warning(f"测试 {port_info.device} 失败: {str(e)}")
        finally:
            # 关闭不可用端口的连接
            if port and not port.is_open:
                port.close()

    return None


def get_serial_conn():
    for rate in define_rate:
        # 自动检测可用波特率端口
        valid_serial = find_usable_port(baudrate=rate, timeout=0.5)
        if valid_serial:
            logger.info(f"成功连接可用串口: {valid_serial.port}")
            return valid_serial
        time.sleep(1)
    return None


class SerialTool:
    def __init__(self):
        self.serial_tool = None
        self.port = get_serial_conn()
        if self.port is None:
            logger.error('未获取到串口连接，请检查串口是否正确连接或者重新拔插一下！')
            # raise Exception("未获取到串口连接，请检查串口是否正确连接或者重新拔插一下！")

    def base_action(self):
        into_path = ' '.join(['cd', vehicle_path, '\r\n'])
        self.port.write(into_path.encode('utf-8'))
        time.sleep(1)
        self.port.write((setup_cmd+'\r\n').encode('utf-8'))

    def power_on(self):
        self.base_action()
        cmd = './vehicle_info --remote-power-on\n'
        logger.info('远程上电')
        self.port.write(cmd.encode('utf-8'))
        time.sleep(3)

    def power_off(self):
        self.base_action()
        logger.info('远程下电')
        cmd = './vehicle_info --remote-power-off\n'
        self.port.write(cmd.encode('utf-8'))

    def ota_in(self):
        self.base_action()
        logger.info('进入ota模式')
        cmd = './vehicle_info --ota-mode-in\n'
        self.port.write(cmd.encode('utf-8'))

    def ota_out(self):
        self.base_action()
        logger.info('退出ota模式')
        cmd = './vehicle_info --ota-mode-out\n'
        self.port.write(cmd.encode('utf-8'))

    def rm_package(self):
        logger.info('删除升级包')
        cmd = 'rm /var/update/ftp/*\n'
        self.port.write(cmd.encode('utf-8'))

    def set_file(self, *args):
        """
        设置配置文件，支持传递多个键值对
        优化点：
        1. 直接在最终位置写入文件
        2. 改进验证逻辑，处理响应中的额外字符
        3. 保留三重中断保证和重试机制
        """
        from config.Config import eea4_qnx_config_file
        c_json = eea4_qnx_config_file.copy()

        # 处理参数
        if len(args) == 2:
            key, value = args
            logger.info(f"设置单个键值对: {key}={value}")
            c_json[key] = value
        elif len(args) > 2 and len(args) % 2 == 0:
            for i in range(0, len(args), 2):
                key = args[i]
                value = args[i + 1]
                logger.info(f"设置键值对: {key}={value}")
                c_json[key] = value
        else:
            logger.error("参数格式错误，应为(key, value)或(key1, value1, key2, value2, ...)")
            return False

        logger.info(f"更新后的配置: {c_json}")
        config_file_json = json.dumps(c_json)
        final_file = "/emmc/svp/tpa/ota_carota/common4_de/bin/vehicle_info_config.json"

        max_retries = 3
        for attempt in range(1, max_retries + 1):
            try:
                logger.info(f"尝试第 {attempt}/{max_retries} 次配置写入...")
                if not self.port or not self.port.isOpen():
                    logger.error('串口未打开或未初始化')
                    return False

                # 1. 发送三重中断保证
                self.port.write(b'\x03\x03\x03\n')
                time.sleep(0.3)

                # 2. 清空串口缓冲区
                self.port.reset_input_buffer()
                self.port.reset_output_buffer()

                # 3. 发送同步命令确认系统状态
                self.port.write(b'echo "SYNC_READY"\n')
                time.sleep(0.2)

                # 4. 检查系统是否准备好
                ready_count = 0
                for _ in range(3):
                    self.port.write(b'echo "SYNC_CHECK"\n')
                    time.sleep(0.2)
                    if self.port.in_waiting > 0:
                        response = self.port.read(self.port.in_waiting).decode(errors='ignore')
                        if "SYNC_CHECK" in response:
                            ready_count += 1
                            if ready_count >= 2:
                                break
                    time.sleep(0.1)

                if ready_count < 2:
                    logger.error(f"系统同步检查失败 (尝试 {attempt}/{max_retries})")
                    continue

                # 5. 执行mount命令
                self.port.write(b'mount -uw /\n')
                time.sleep(0.5)

                # 6. 直接写入最终文件位置
                write_cmd = f'cat <<\'EOF\' > {final_file}\n{config_file_json}\nEOF\n'
                self.port.write(write_cmd.encode('utf-8'))
                time.sleep(0.5)

                # 7. 改进的验证逻辑
                self.port.reset_input_buffer()
                self.port.write(f'cat {final_file}\n'.encode('utf-8'))
                time.sleep(0.8)

                if self.port.in_waiting > 0:
                    response = self.port.read(self.port.in_waiting).decode(errors='ignore')
                    logger.debug(f"验证响应: {response}")  # 添加详细日志

                    # 关键改进：从响应中提取JSON内容
                    json_start = response.find('{')
                    json_end = response.rfind('}')

                    if json_start != -1 and json_end != -1 and json_end > json_start:
                        json_content = response[json_start:json_end + 1]
                        try:
                            actual_json = json.loads(json_content)
                            if actual_json == c_json:
                                logger.info("配置文件写入验证成功")
                                return True
                            else:
                                # 详细记录差异
                                logger.error(f"配置内容不匹配! 期望: {c_json}，实际: {actual_json}")
                        except json.JSONDecodeError:
                            logger.error(f"JSON解析失败! 提取内容: {json_content}")
                    else:
                        logger.error(f"在响应中未找到JSON内容! 响应: {response[:200]}")
                else:
                    logger.error("未收到文件内容响应，写入可能失败")

                # 验证失败时进行下一次重试
                if attempt < max_retries:
                    logger.info(f"验证失败，将在 {0.5 * attempt} 秒后重试...")
                    time.sleep(0.5 * attempt)

            except serial.SerialException as se:
                logger.error(f"串口通信错误 (尝试 {attempt}/{max_retries}): {str(se)}")
                if attempt < max_retries:
                    time.sleep(0.5 * attempt)
            except Exception as e:
                logger.exception(f"未预期错误 (尝试 {attempt}/{max_retries}): {str(e)}")
                if attempt < max_retries:
                    time.sleep(0.5 * attempt)

        logger.error(f"所有 {max_retries} 次尝试均失败，操作终止")
        return False


    def reboot(self):
        cmd = "/emmc/svp/etc/reset-via-mcu.sh 30\n"
        logger.info('重启车机')
        self.port.write(cmd.encode('utf-8'))
        time.sleep(30)

    def ecu_uds_shell(self):
        pass

# serial_tool = SerialTool()

# serial_tool.reboot()
# serial_tool.set_file("vehicle_lock_door_status", 0)
# serial_tool.set_file("anti_theft_status", 0)
# serial_tool.set_file("power_gear_on", 0)
# serial_tool.set_file("gearbox_gear", 2)
# serial_tool.set_file("obd_status", 0)
# serial_tool.set_file("tbox_diag_status", 3)
# serial_tool.set_file("vehicle_model", 2)
# serial_tool.set_file("remote_poweroff", 1)
# serial_tool.set_file("remote_poweron", 1)
# serial_tool.set_file("otamodein", 0)
# serial_tool.set_file("ibs_soc", 1)
# serial_tool.set_file("otamodeout", 0)
# serial_tool.rm_package()

# serial_tool.set_file("otamodein",0,"otamodeout",0,"remote_poweroff",0,"remote_poweron",0)