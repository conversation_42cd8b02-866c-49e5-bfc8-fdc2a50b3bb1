# coding: utf-8
# Project：car_auto_test
# File：test_D008.py
# Author：杨郑健
# Date ：2025/6/12 14:52
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("前置条件检测")
@AllureDecorators.title("引擎停止")
@static_condition(conditions=
{
    "power": "ON",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_D008(driver, launch_app):
    """
    测试步骤
    1、引擎启动状态，查看“检测升级前置条件”
    2、引擎停止状态，查看“检测升级前置条件”"

    预期结果
    1、“引擎停止”检测不通过
    2、“引擎停止”检测通过"
    """
    objects = Step_object(driver)
    objects.get_task()
    serial_tool.set_file("engine_status", 1)
    objects.disclaimer_click()
    sleep(5)
    result = objects.check_element_exists("precondition")
    serial_tool.set_file("engine_status", 0)
    sleep(5)
    result1 = objects.check_element_exists("precondition")
    assert result and result1