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