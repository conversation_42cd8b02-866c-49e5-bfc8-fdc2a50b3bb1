# coding: utf-8
# Project：car_auto_test
# File：test_F015.py
# Author：杨郑健
# Date ：2025/6/23 16:38
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.OCR.Check_red_dot import check_red_dot
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest


@AllureDecorators.epic("升级结果")
@AllureDecorators.title("升级完成，退出升级模式失败”：小红点")
def test_F014(driver, launch_app):
    """
    测试步骤
    1、“更新进行中”待升级完成，升级成功，执行退ota失败
    2、“升级完成，退出升级模式失败”点击“确定”
    3、查看ota应用图标

    预期结果
    3、无小红点显示
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        "tbox_status": "NOTREAD",
        "power": ["READY", "OFF"],
        "task": "normal",
        "gear": "P",
        "handbrake": "OFF"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'READY':
            objects.get_task()
            logger.info("READY挡验证完成")
            assert True
        elif current_state == 'OFF':
            logger.info("OFF挡升级失败")
            objects.update_success_otamodeout_fail()
            objects.click_element("success_otamode_fail")
            objects.back_home()
            result = check_red_dot()
            assert result == False
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()