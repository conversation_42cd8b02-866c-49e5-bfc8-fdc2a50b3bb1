# coding: utf-8
# Project：car_auto_test
# File：test_A009.py
# Author：杨郑健
# Date ：2025/6/10 14:58
from testcases.EEA4.PageObjects.TestObject import Step_object
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from utils.LoggingSystem.Logger import logger
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.SerialTool.serial_tool import serial_tool
from utils.CloudOperation.chery_operation import Cloud
from utils.ReportGenerator.Allure import *

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("启动车辆：根据后台车型bom读取ecu信息")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_A009(driver, launch_app):
    """     根据后台BOM读取ecu信息，此步骤没有设置检查，待优化
    测试步骤
    1、电源挡位切换为READY
    2、待TBOX自诊断结束后进行任务检查（tel-diagnose: TEL_DIAGNOSE_NOT_READ）
    3、任务检测结束后检查拿到任务的状态

    预期结果
    3、发起检测，根据后台车型bom读取ecu信息
    """
    objects = Step_object(driver)
    get_task_result = objects.get_task()
    results = otaLogAnalysis.find_keywords(["TEL_DIAGNOSE_NOT_READ"])
    assert get_task_result and results