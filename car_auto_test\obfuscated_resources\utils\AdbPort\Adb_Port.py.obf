"""
ADB封装类
"""

import os
import re
import time
from typing import Optional, List
from utils.LoggingSystem.Logger import logger


class AdbExec:
    def execute(self, cmd: str) -> str:
        """执行adb命令
        Args:
            cmd: adb命令
        Returns:
            命令执行结果
        """
        resp = os.popen(cmd)
        result = resp.read()
        return result

    @property
    def devices(self) -> Optional[str]:
        """获取已连接的设备列表"""
        cmd = 'adb devices'
        device_list = re.findall(r'\n(.*?)\s+device', self.execute(cmd))
        if not device_list:
            logger.debug('未检测到ADB设备连接，请检查设备连接状态')
            # raise Exception('未检测到ADB设备连接，请检查设备连接状态')
            return None
        return device_list[0].strip()

    def delete_app_databases(self):
        '''
        删除apk数据库
        '''
        self.root()
        cmd = f"adb shell rm /data/data/com.carota.chery/databases/*"
        result = self.execute(cmd)
        return "Success" in result


    def install_app(self, apk_path: str) -> bool:
        """安装应用
        Args:
            apk_path: apk文件路径
        Returns:
            安装是否成功
        """
        cmd = f"adb install -r {apk_path}"
        result = self.execute(cmd)
        return "Success" in result

    def uninstall_app(self, package_name: str) -> bool:
        """卸载应用
        Args:
            package_name: 应用包名
        """
        cmd = f"adb uninstall {package_name}"
        result = self.execute(cmd)
        return "Success" in result

    def screen_capture(self, save_path: str) -> bool:
        """截屏
        Args:
            save_path: 保存路径
        """
        cmd = f"adb shell screencap -p /sdcard/screenshot.png"
        self.execute(cmd)
        pull_cmd = f"adb pull /sdcard/screenshot.png {save_path}"
        self.execute(pull_cmd)
        return os.path.exists(save_path)

    def pull_file(self, pull_path, save_path):
        """拉取文件
        Args:
            pull_path: pull路径
            save_path: 保存路径
        """
        cmd = f"adb pull {pull_path} {save_path}"
        result = self.execute(cmd)
        return "Success" in result

    def push_file(self, push_path, local_path):
        """推文件
        Args:
            push_path:  推文件路径
            local_path: 本地文件路径
        """
        cmd = f"adb pull {local_path} {push_path}"
        result = self.execute(cmd)
        return "Success" in result

    def root(self):
        cmd = f"adb root"
        result = self.execute(cmd)
        return "Success" in result

    def home(self):
        cmd = "adb shell input keyevent KEYCODE_HOME"
        self.execute(cmd)

    def open_wifi(self):
        cmd = "adb shell cmd wifi set-wifi-enabled enabled"
        self.execute(cmd)

    def close_wifi(self):
        cmd = "adb shell cmd wifi set-wifi-enabled disabled"
        self.execute(cmd)

    def wifi_status(self):
        """查看当前车机的wifi状态
        Returns: True 已开启wifi
                 False 未开启wifi
        """
        cmd = "adb shell cmd wifi status | findstr abled"
        ststus_data = self.execute(cmd)
        if ststus_data.rfind("disabled") != -1:
            return False
        else:
            return True

    def disable_network(self, package):
        """关闭tbox网络通用方案，使用iptables来实现,此方法如果在2分钟内不恢复有些主机会自动重启恢复
        package: 包名
        """
        logger.info('断开整车网络')
        self.root()
        cmd = f"adb shell dumpsys package {package} | findstr userId"
        res = self.execute(cmd)
        enable = f"adb shell iptables -w -A OUTPUT -m owner --uid-owner {res[11:15]} -j DROP"
        self.execute(enable)

    def enable_network(self):
        """恢复tbox网络"""
        self.root()
        cmd = "adb shell iptables -w -L OUTPUT --line-numbers | findstr DROP"
        res = self.execute(cmd)
        # 可能有多条DROP规则，逐条删除
        for line in res.splitlines():
            line_num = line.strip().split()[0]
            dis = f"adb shell iptables -w -D OUTPUT {line_num}"
            self.execute(dis)
        logger.info('等待恢复整车网络')

    def get_manufacture(self):
        """
        查询主机厂商
        """
        facture = {
            "DesaySV": 1,  # 德赛
            "Mega": 2,  # 伯泰克镁佳
            "Neusoft": 3,  # 东软
            "iFLYTEK": 4,  # 讯飞
            "tansuo_06": 4  # 讯飞
        }
        cmd = "adb shell getprop ro.product.manufacturer"
        resp = self.execute(cmd).strip()
        if resp == 'QTI' or resp == "9GF":
            cmd = "adb shell getprop ro.product.brand"
            resp = self.execute(cmd).strip()
        return facture[resp]

    def set_file(self, *args):
        """
        设置配置文件，支持传递多个键值对
        直接基于eea4_config_file修改后推送到设备
        """
        import json
        import tempfile
        from config.Config import eea4_apk_config_file

        # 配置文件路径
        config_path = "/sdcard/Download/mda_vehicle_info.json"
        
        try:
            # 使用默认配置作为基础
            c_json = eea4_apk_config_file.copy()
            
            # 处理参数
            if len(args) == 2:
                # 单个键值对
                key, value = args
                logger.info(f"设置单个键值对: {key}={value}")
                c_json[key] = value
            elif len(args) > 2 and len(args) % 2 == 0:
                # 多个键值对
                for i in range(0, len(args), 2):
                    key = args[i]
                    value = args[i+1]
                    logger.info(f"设置键值对: {key}={value}")
                    c_json[key] = value
            else:
                logger.error("参数格式错误，应为(key, value)或(key1, value1, key2, value2, ...)")
                return False

            logger.info(f"更新后的配置: {c_json}")

            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w+', delete=False) as temp_file:
                temp_path = temp_file.name
                
                # 写入临时文件
                with open(temp_path, 'w', encoding='utf-8') as f:
                    json.dump(c_json, f, indent=4, ensure_ascii=False)

                # 推送文件到设备
                push_cmd = f"adb push {temp_path} {config_path}"
                result = self.execute(push_cmd)
                
                if "error" in result.lower():
                    logger.error(f"推送配置文件失败: {result}")
                    return False

                logger.info("配置文件已成功更新")
                return True

        except Exception as e:
            logger.error(f"设置配置文件时发生错误: {str(e)}")
            return False
        finally:
            # 等待apk配置文件生效
            # logger.info('等待10秒、apk配置文件生效')
            # time.sleep(10)
            # 清理临时文件
            try:
                os.unlink(temp_path)
            except:
                pass


adb = AdbExec()
# adb.delete_app_databases()
# adb.get_manufacture()

# adb.enable_network()
# adb.disable_network("com.carota.chery")
# adb.enable_network()

# adb.set_file('charge_state',2)