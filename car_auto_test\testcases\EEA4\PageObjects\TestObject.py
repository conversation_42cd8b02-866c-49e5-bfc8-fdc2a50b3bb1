'''
步骤封装文件
'''

import time

from Demos.RegCreateKeyTransacted import classname

from core.BasePage.Base_page import BasePage
from config.Config import *
from utils.SerialTool.actuator import act
# from utils.SerialTool.serial_tool import serial_tool
from .Elements import elements
from threading import Thread
from utils.LoggingSystem.Logger import logger

from testcases.EEA4.PageObjects.EngineeringPassword import EngineeringPassword
from utils.ImageRecognition.Image_comparison import image_recognition
from utils.ReportGenerator.Allure import AllureSteps
from utils.AdbPort.Adb_Port import AdbExec
from utils.SerialTool import qnx_execute
from utils.SerialTool.actuator import act


class Step_object(BasePage):
    def __init__(self, driver, mode=0, mode_dict=None, vehicle_flag=None, language=None) -> None:
        super().__init__(driver)
        self.mode_value = mode
        self.vehicle_flag = vehicle_flag
        self.language = language
        self.width, self.height = driver.window_size()

        # 设置mode_dict
        if mode_dict:
            self.set_mode_dict(mode_dict)
        else:
            logger.warning(f"初始化Step_object时未提供mode_dict，screen_image功能可能无法正常工作")

    # 没有任务时ui
    def no_task(self):
        time.sleep(1)
        self.screen_image('初始化', self.mode_value)
        # self.driver.app_start('com.android.launcher3', activity='.activity.AppListActivity')
        # AdbExec().execute("adb shell input keyevent 3")
        if act.manufacture == 1:  # 德赛主机处理操作
            # AdbExec().execute("adb shell input keyevent 3")
            self.driver.app_start('com.desaysv.launcher', activity='com.desaysv.appmenu.activity.AppMenuActivity')
        if act.manufacture == 2:  # 镁佳/伯泰克主机处理操作
            self.driver.app_start('com.android.launcher3', activity='com.android.launcher3.activity.AppListActivity')
        if act.manufacture == 3:  # 东软主机处理操作
            AdbExec().execute("adb shell input keyevent 3")
        if act.manufacture == 4:  # 讯飞主机处理操作
            AdbExec().execute("adb shell input keyevent 3")

        time.sleep(3)
        self.screen_image('无升级任务任务、Home界面小红点显示', self.mode_value)
        self.driver.app_start('com.carota.chery')
        time.sleep(1)

    def project_password(self):
        for _ in range(12):
            self.click_element('engineering_mode')
            time.sleep(0.1)
            if self.check_element_exists('engineering_password',max_wait_time=0.1):
                logger.info('工程模式密码输入框')
                time.sleep(0.5)
                self.screen_image('工程模式密码输入框', self.mode_value)
                self.click_element('password_confirm')
                time.sleep(0.5)
                self.screen_image('工程模式密码输入错误', self.mode_value)
                self.click_element('password_confirm')
                break




    # #@AllureSteps.log_step("工程模式清除数据")
    # def clear_data(self):
    #     """
    #     工程模式清除数据
    #     Returns:
    #
    #     """
    #     for _ in range(9):
    #         self.click_element("engineering_mode")
    #     self.send_element('engineering_password',vehile_password)
    #     self.click_element("password_confirm")
    #     self.click_element("clear_data")

    #@AllureSteps.log_step("进入工程模式")
    def enter_engineering(self):
        """进入工程模式"""
        max_attempts = 5
        attempt = 0

        while attempt < max_attempts:
            attempt += 1
            logger.info(f"尝试进入工程模式 (尝试 {attempt}/{max_attempts})")
            # 每次都重新生成密码

            # vehicle_password = EngineeringPassword().get_password()
            # cmd = f'adb shell am start -n com.carota.chery/.ui.engine.EngineModeActivity -e password {vehicle_password}'
            # AdbExec().execute(cmd)
            #
            #
            #
            # if self.check_element_exists('check_update'):
            #
            #     logger.info("成功进入工程模式")
            #     return True
            # else:
            #     logger.warning(f'工程模式密码输入错误 (尝试 {attempt}/{max_attempts})')
            #     # self.click_element('password_confirm')

            now_password = EngineeringPassword().get_password()
            yesterday_password = EngineeringPassword(now=False).get_password()
            for vehicle_password in [now_password, yesterday_password]:
                cmd = f'adb shell am start -n com.carota.chery/.ui.engine.EngineModeActivity -e password {vehicle_password}'
                AdbExec().execute(cmd)
                if self.check_element_exists('check_update'):
                    logger.info("成功进入工程模式")
                    return True
                else:
                    logger.warning(f'工程模式密码输入错误 (尝试 {attempt}/{max_attempts})')

    # #@AllureSteps.log_step("工程模式检查更新任务获取检测结果")
    def get_task(self, take_screenshot=False):
        """
        工程模式检查更新任务获取
        """
        # for _ in range(9):
        #     self.click_element("engineering_mode")
        #     logger.info(_)
        #
        # self.check_element_exists('engineering_password')
        # self.send_element('engineering_password', vehile_password)
        # self.click_element("password_confirm")
        # time.sleep(1)
        # self.screen_image('工程模式', self.mode_value)
        # self.click_element("check_update")
        # self.click_element("back_button")
        # num =0
        # while True:
        #     if self.driver(text='发现新版本') or self.driver(text='New version is available'):
        #         break
        #         # return True
        #     if num == max_wait_time:
        #         return False
        #     logger.info(f'任务检测中{num}秒 / {max_wait_time}秒')
        #     time.sleep(interval)
        #     num += interval
        # self.screen_image('发现新版本', self.mode_value)
        """
              工程模式检查更新任务获取
        """
        act.power_on()
        max_attempts = 5
        exit_flag = False
        for attempt in range(1, max_attempts + 1):
            logger.info(f"开始第{attempt}次尝试检测任务 (共{max_attempts}次)")

            try:
                self.enter_engineering()
                if take_screenshot:
                    self.screen_image('工程模式', self.mode_value)
                logger.info('点击检查更新按钮')
                self.click_element("check_update")
                logger.info('点击返回按钮')
                if self.check_element_exists('back_button',max_wait_time=2):
                    self.click_element("back_button")
                else:
                    self.driver(className='android.widget.ImageButton')[0].click()

                # 通过文本比对，长度比对存在偶现相同问题
                text1 = self.find_element('engineering_mode').get_text()
                logger.info(f'text1:{text1}')
                num = 0

                while True:
                    text2 = self.find_element('engineering_mode').get_text()
                    logger.info(f'text2:{text2}')
                    if text1 != text2:
                        logger.info(f"第{attempt}次尝试成功检测到任务")
                        if take_screenshot:
                            for _ in range(20):
                                if self.check_element_exists('notify_img',max_wait_time=2) or self.check_element_exists('notify_img_update',max_wait_time=2):
                                    break
                            time.sleep(0.5)
                            self.screen_image('发现新版本弹框', self.mode_value)
                            time.sleep(5)
                            self.screen_image('检测到任务升级主页面显示', self.mode_value)
                        exit_flag = True
                        break

                    if num == EEA4_max_wait_time:
                        logger.warning(f'第{attempt}次尝试：检测任务时间到({EEA4_max_wait_time}秒)，未查到任务')
                        break  # 退出while循环，进入下一次尝试

                    # logger.info(f'第{attempt}次尝试：任务检测中{num}秒 / {EEA4_max_wait_time}秒')
                    time.sleep(interval)
                    num += EEA4_interval

            except Exception as e:
                logger.error(f"第{attempt}次尝试过程中出错: {str(e)}")

            if exit_flag == False:
                # 如果不是最后一次尝试，等待一段时间再重试
                if attempt < max_attempts:
                    logger.info(f"第{attempt}次尝试失败，等待5秒后进行第{attempt + 1}次尝试")
                    # time.sleep(5)  # 等待5秒再重试
            else:
                break


        time.sleep(5)

    # #@AllureSteps.log_step("工程模式检查更新任务不获取任务检测结果")
    def get_task_noback(self):
        """
        工程模式检查更新任务获取
        """

        for _ in range(9):
            self.click_element("engineering_mode")
            logger.info(_)
        # self.send_element('engineering_password',vehile_password)
        # time.sleep(0.5)
        self.click_element("password_confirm")
        time.sleep(2)
        self.screen_image('工程模式')
        self.click_element("check_update")
        self.click_element("back_button")

    # #@AllureSteps.log_step("查看任务检测结果")
    def check_update_status(self):
        num = 0
        while True:
            # 这里不能用下面的方法如check_element_text，元素等待会超过30s
            if self.driver(text='发现新版本'):
                return True
            if num == max_wait_time:
                return False
            logger.info(f'任务检测中{num}秒 / {max_wait_time}秒')

            time.sleep(interval)
            num += interval

    # #@AllureSteps.log_step("返回主页面，查看OTA APP是否有小红点")
    def back_home(self):
        """
        返回主页面 查看小红点
        Returns:

        """
        # self.driver.app_start('com.android.launcher3', activity='.activity.AppListActivity')

        if act.manufacture == 1:  # 德赛主机处理操作
            # AdbExec().execute("adb shell input keyevent 3")

            self.driver.app_start('com.desaysv.launcher', activity='com.desaysv.appmenu.activity.AppMenuActivity')
        if act.manufacture == 2:  # 镁佳/伯泰克主机处理操作
            self.driver.app_start('com.android.launcher3', activity='com.android.launcher3.activity.AppListActivity')
        if act.manufacture == 3:  # 东软主机处理操作
            AdbExec().execute("adb shell input keyevent 3")
        if act.manufacture == 4:  # 讯飞主机处理操作
            AdbExec().execute("adb shell input keyevent 3")
        # self.driver.app_start('com.desaysv.launcher',activity='com.desaysv.appmenu.activity.AppMenuActivity')
        # AdbExec().execute("adb shell input keyevent 3")
        time.sleep(2)
        self.screen_image('检测到任务、小红点显示', self.mode_value)
        self.driver.app_start('com.carota.chery')
        time.sleep(1)

    #@AllureSteps.log_step("点击主页面详情按钮")
    def details_click(self):
        '''
        点击详情
        Returns:

        '''

        time.sleep(1)
        # 点击详情按钮
        self.click_element('details_home')
        # self.driver.xpath(
        #     '//*[@resource-id="com.carota.chery:id/chery_coll_tv"]/android.widget.LinearLayout[1]/android.widget.LinearLayout[1]').click()
        time.sleep(1)
        # self.check_element_exists('details_title')
        self.screen_image('详情页面截图', self.mode_value)
        if act.manufacture ==1 :
            self.driver(className='android.widget.ImageView')[0].click()
            # self.driver(className='android.widget.ImageView')[1].click()
        else:
            self.click_element('back_button')
        # self.driver.xpath('//android.widget.ImageView').click()

    def privacy_click(self):
        self.click_element('engineering_privacy_btn')
        time.sleep(0.5)
        self.screen_image('隐私申明', self.mode_value)



        self.click_element('engineering_privacy_acknowledge_btn')
        # self.click_element('engineering_privacy_acknowledge_btn')
        if act.manufacture == 1:  # 德赛主机处理操作
            self.click_element('engineering_privacy_acknowledge_btn1')
        if act.manufacture == 2:  # 镁佳/伯泰克主机处理操作
            self.click_element('engineering_privacy_acknowledge_btn')
        if act.manufacture == 3:  # 东软主机处理操作
            self.click_element('engineering_privacy_acknowledge_btn1')
        if act.manufacture == 4:  # 讯飞主机处理操作
            self.click_element('engineering_privacy_acknowledge_btn1')



    #@AllureSteps.log_step("点击主页面立即升级按钮")
    def nowupdate_click(self):
        self.click_element('now_btn')
        logger.info('点击立即升级')
        time.sleep(1)

    #@AllureSteps.log_step("点击主页面立即升级按钮后再点击继续升级按钮")
    def matter_click(self, take_screenshot=True):
        """
        1、点击立即升级弹出注意事项
        2、点击继续升级
        Returns:

        """
        self.nowupdate_click()
        if take_screenshot:
            time.sleep(0.5)
            self.screen_image('注意事项', self.mode_value)
        time.sleep(1)
        self.click_element('matter_argee_btn', index=0)
        logger.info('已点击注意事项 继续升级按钮')

    #@AllureSteps.log_step("点击免责同意")
    def disclaimer_click(self, take_screenshot=True):
        """
        1、点击立即升级弹出注意事项
        2、点击继续升级
        3、免责同意
        Returns:

        """
        self.matter_click(take_screenshot)
        time.sleep(1)
        if take_screenshot:
            time.sleep(0.5)
            self.screen_image('免责声明倒计时', self.mode_value)
        time.sleep(12)
        if take_screenshot:
            self.screen_image('免责声明', self.mode_value)
        self.click_element('disclaimer_argee_btn')
        logger.info('点击免责同意按钮')

    #@AllureSteps.log_step("点击主页面立即升级后，点击继续升级，点击免责取消")
    def disclaimer_cancel_click(self):
        """
        1、点击立即升级弹出注意事项
        2、点击继续升级
        3、免责同意
        Returns:

        """
        self.matter_click()
        self.click_element('disclaimer_cancel_btn')
        logger.info('点击免责取消按钮')

    #@AllureSteps.log_step("点击主页面立即升级后，点击继续升级，点击免责取消(等待计时结束)")
    def disclaimer_cancel_click_delay5s(self):
        """
        1、点击立即升级弹出注意事项
        2、点击继续升级
        3、免责同意
        Returns:

        """
        self.matter_click()
        time.sleep(6)
        self.click_element('disclaimer_cancel_btn')
        logger.info('点击免责取消按钮')

    #@AllureSteps.log_step("点击主页面立即升级后，点击继续升级，检查免责同意按钮是否可以点击")
    def check_disclaimer_exist(self):
        """
        1、点击立即升级弹出注意事项
        2、点击继续升级
        3、查看免责页面的按钮同意否可以直接点击
        Returns:

        """
        logger.info("检查免责同意按钮是否可以点击")
        self.matter_click()
        if self.check_element_exists("disclaimer_cancel_frame"):
            res = self.get_element_enabled("disclaimer_argee_btn")
            return res
        return False

    #@AllureSteps.log_step("点击主页面立即升级后，点击继续升级，等待5秒后,检查免责同意按钮是否可以点击")
    def check_disclaimer_withtime_exist(self):
        """
        1、点击立即升级弹出注意事项
        2、点击继续升级
        3、查看免责页面的按钮同意否可以直接点击
        Returns:

        """
        logger.info("等待5s,检查免责同意按钮是否可以点击")
        self.matter_click()
        time.sleep(6)
        if self.check_element_exists("disclaimer_cancel_frame"):
            res = self.find_element_text_enable("disclaimer_argee_btn")
            return res
        return False

    #@AllureSteps.log_step("检查有没有下拉负一屏")
    def check_negative_screen(self):
        logger.info('检查有没有下拉负一屏')

    #@AllureSteps.log_step("点击主页面预约升级按钮")
    def appointment_upgrade_click(self):
        self.click_element('schedule_btn')
        logger.info('点击预约升级')
        time.sleep(1)
        self.screen_image('注意事项', self.mode_value)
        return self.check_element_exists('matter_cancel_frame')

    #@AllureSteps.log_step("连续点击主页面预约升级按钮")
    def appointment_upgrade_fast_click(self):
        # self.click_element('schedule_btn')
        self.fast_click_element("schedule_btn", cum=5)
        logger.info('连续点击预约升级')
        time.sleep(1)
        self.screen_image('注意事项', self.mode_value)
        return not self.check_element_exists('matter_cancel_frame')

    #@AllureSteps.log_step("点击主页面预约升级按钮后注意事项页面点击取消")
    def appointment_upgrade_click(self):
        """点击主页面预约升级按钮后注意事项页面点击取消
        Returns: True/False
        """
        self.click_element('schedule_btn')
        logger.info('注意事项页面点击取消')
        time.sleep(1)
        self.screen_image('注意事项', self.mode_value)
        self.click_element('matter_cancel_btn')
        return not self.check_element_exists('matter_cancel_frame')

    #@AllureSteps.log_step("当前车辆OFF挡检测")
    def gear_off(self):
        """
        当前车辆处于off档
        Returns:

        """
        self.disclaimer_click()
        self.click_element('gear_off')
        logger.info('当前车辆处于off档')
        time.sleep(0.5)
        self.screen_image('当前车辆处于off档', self.mode_value)

    def first_prepare_update(self, take_screenshot=False):
        """第一次升级准备中"""
        self.disclaimer_click(take_screenshot=False)
        for _ in range(10):
            if self.check_element_exists('update_preparing',max_wait_time=1) or  self.check_element_exists('update_preparing_desay',max_wait_time=1):
                break
        logger.info('第一次升级准备中')

        if take_screenshot:

            self.screen_image('车机检查中，软开关下电', self.mode_value)

    def first_prepare_update_fail(self, take_screenshot=False):
        """第一次升级准备中"""
        self.disclaimer_click(take_screenshot)
        self.check_element_exists('update_conditions_not_meet')
        logger.info('第一次升级准备中')

    #@AllureSteps.log_step("车辆即将远程上电页面检测")
    def power_on(self, take_screenshot=False):
        """
        车辆即将远程上电
        Returns:

        """
        self.first_prepare_update(take_screenshot)
        self.check_element_exists('power_on')
        logger.info('车辆即将进入 on 档升级')
        if take_screenshot:
            time.sleep(0.5)
            self.screen_image('车辆即将进入 on 档升级', self.mode_value)

    def power_on_fail(self):
        """
        车辆即将远程上电失败
        Returns:

        """
        self.power_on()
        time.sleep(10)
        act.power_on()
        self.check_element_exists('remote_power_on_fail_btn')
        logger.info('上电失败')

    def enter_ota_mode_fail(self):
        '''
        进ota失败
        Returns:

        '''
        self.power_on()
        self.check_element_exists('otamodein_fail_btn')

        logger.info('进入ota模式失败')
        time.sleep(0.5)
        self.screen_image('进入ota模式失败倒计时', self.mode_value)
        time.sleep(23)

        # power_on()
        act.power_on()
        self.screen_image('进入ota模式失败确定按钮', self.mode_value)

    def enter_hvo_up_fail(self):
        '''
        上高压失败
        Returns:

        '''
        self.power_on()

        self.check_element_exists('otamodein_fail_btn')
        logger.info('上高压失败')
        time.sleep(0.5)
        self.screen_image('上高压失败倒计时', self.mode_value)
        time.sleep(22)

        AdbExec().execute("adb shell input keyevent KEYCODE_WAKEUP")
        self.screen_image('上高压失败确定按钮', self.mode_value)

    def ibs_soc_fail(self):
        '''蓄电池电量不满足'''
        self.power_on()
        self.check_element_exists('ibs_soc_fail_btn')
        logger.info('蓄电池电量不满足')
        time.sleep(0.5)
        self.screen_image('蓄电池电量不满足倒计时', self.mode_value)
        time.sleep(22)
        if act.manufacture == 1:
            act.power_on()
        self.screen_image('蓄电池电量不满足确定按钮', self.mode_value)

    def power_off_fail(self):
        """
        下电失败
        Returns:

        """

        self.check_element_exists('remote_power_off_fail_btn')
        time.sleep(0.5)
        self.screen_image('下电失败', self.mode_value)
        time.sleep(0.5)
        self.click_element('remote_power_off_fail_btn',max_wait_time=5)
        self.click_element('remote_power_off_fail_desay_btn')


    def otamode_out_fail(self):
        '''退出ota失败'''
        # if act.manufacture ==1 :
        #     self.check_element_exists('desay_otamodeout_fail_btn')
        #     time.sleep(0.5)
        #     self.screen_image('退出ota模式失败', self.mode_value)
        #     self.click_element('desay_otamodeout_fail_btn')
        # else:
        #
        #     self.check_element_exists('otamodeout_fail_btn')
        #     time.sleep(0.5)
        #     self.screen_image('退出ota模式失败', self.mode_value)
        #     self.click_element('otamodeout_fail_btn')
        if self.check_element_exists('desay_otamodeout_fail_btn',max_wait_time=5) or self.check_element_exists('otamodeout_fail_btn',max_wait_time=5):
            time.sleep(0.5)
            self.screen_image('退出ota模式失败', self.mode_value)

            self.smart_click_elements([
                'otamodeout_fail_btn',
                'desay_otamodeout_fail_btn'
            ], max_wait_time=5)
        # time.sleep(0.5)
        # self.screen_image('退出ota模式失败', self.mode_value)

    #
    #     self.smart_click_elements([
    #         'otamodeout_fail_btn',
    #         'desay_otamodeout_fail_btn'
    #     ], max_wait_time=5)
    #
    # #
    # def enter_hv_fail(self):
    #     '''
    #     上高压
    #     Returns:
    #
    #     '''
    #     self.power_on()
    #     self.check_element_exists('otamodein_fail_btn')
    #     logger.info('进入ota模式失败')
    #     self.screen_image('上高压失败、升级条件不满足倒计时', self.mode_value)
    #     time.sleep(22)
    #     self.screen_image('上高压失败、升级条件不满足确定按钮', self.mode_value)
    #

    #@AllureSteps.log_step("升级前置条件页面")
    def preconditions(self, take_screenshot=False):
        """
        前置条件界面
        Returns:

        """
        self.power_on(take_screenshot)
        self.check_element_exists('precondition')
        logger.info('前置条件检测界面')
        if take_screenshot:
            time.sleep(0.5)
            self.screen_image('前置条件检测界面', self.mode_value)

    def exce_poweron(self):
        '''处理绕过on档'''
        self.power_on()
        # act.set_file('power_gear', 2)
        self.check_element_exists('precondition')
        logger.info('前置条件检测界面')

    #@AllureSteps.log_step("前置条件全部检测通过判断")
    def preconditions_pass(self, take_screenshot=False):
        """
        前置条件全部通过
        Returns:

        """
        self.exce_poweron()
        self.check_element_exists('precondition_pass',max_wait_time=20)
        logger.info('前置条件全部通过')
        if take_screenshot:
            self.screen_image('前置条件检测通过界面', self.mode_value)

    #@AllureSteps.log_step("前置条件不通过判断")
    def preconditions_fail(self, take_screenshot=False):
        """
          前置条件不通过
          Returns:

        """
        self.preconditions(take_screenshot)
        self.check_element_exists('precondition_fail')
        logger.info('前置条件不通过')

    def cancel_preconditions(self):
        """
        前置条件不满足点击取消
        """
        self.preconditions_fail()
        self.check_element_exists("precondition_fail_cancelbtn")
        logger.info('前置条件不满足，点击取消按钮')
        self.click_element("precondition_fail_cancelbtn")

    def cancel_preconditions_delay(self):
        """
        前置条件不满足点击取消
        """
        self.preconditions_fail()
        cun = 0
        while True:
            if self.check_element_exists("precondition_fail_cancelbtn"):
                logger.info('前置条件不满足，delay%s' % cun)
                time.sleep(1)
            else:
                logger.info("等待时间前置120s 已到，跳出检测点击确认按钮")
                self.click_element("precondition_fail_cancelbtn")
                break

    #@AllureSteps.log_step("升级中到升级成功页面操作")
    def update_success(self, take_screenshot=False):
        """
        更新进行中 > 升级成功
        Returns:

        """
        self.preconditions_pass(take_screenshot)
        self.check_element_exists('update_title')
        logger.info('更新进行中')
        if take_screenshot:
            time.sleep(5)
            self.screen_image('更新进行中', self.mode_value)
        while True:
            time.sleep(5)
            if self.driver(resourceId='com.carota.chery:id/por_tv_title'):
                logger.info('升级结束')
                break
        if take_screenshot:
            time.sleep(0.5)
            self.screen_image('升级成功、确定20S倒计时', self.mode_value)

    def update_success_otamodeout_fail(self):
        '''
        升级成功 退ota失败
        Returns:

        '''
        self.update_success()
        act.set_file('otamodeout', 1)
        self.click_element('success_btn')
        self.check_element_exists('success_otamode_fail')
        time.sleep(0.5)
        self.screen_image('升级成功、退出ota升级模式失败', self.mode_value)
        # if act.manufacture ==1:
        #     self.click_element('hvo_on_fail_btn')
        # else:
        #     self.click_element('success_btn')

        self.smart_click_elements([
            'hvo_on_fail_btn',
            'success_btn'
        ], max_wait_time=5)
     
    def update_success_power_fail(self):
        '''
        升级成功 下电失败
        Returns:

        '''
        self.update_success()
        act.set_file('remote_poweroff', 1)
        act.set_apk_file('vcu_power_hook', 1)
        time.sleep(6)
        self.click_element('success_btn')
        # self.check_element_exists('success_poweroff_fail')
        time.sleep(25)
        self.screen_image('升级成功、下电失败', self.mode_value)
        self.click_element('success_btn')
        act.set_apk_file('vcu_power_hook', 0)

    def rollback_success(self, take_screenshot=False):
        """
        更新进行中 > 回滚成功
        Returns:

        """
        act.rm_package()
        self.preconditions_pass(take_screenshot=False)
        self.check_element_exists('update_title')
        logger.info('更新进行中')
        while True:
            time.sleep(5)
            if self.driver(resourceId='com.carota.chery:id/por_tv_title'):
                logger.info('升级结束')
                break
        if take_screenshot:
            time.sleep(0.5)
            self.screen_image('升级失败、回滚成功、20S倒计时', self.mode_value)

    def rollback_success_otamodeout_fail(self):
        '''
        回滚成功 退ota失败
        Returns:

        '''
        self.rollback_success()
        act.set_file('otamodeout', 1)
        self.click_element('success_btn')
        self.check_element_exists('success_otamode_fail')
        time.sleep(0.5)
        self.screen_image('升级失败、回滚成功、退出ota失败', self.mode_value)
        time.sleep(0.5)
        # if act.manufacture == 1:
        #     self.click_element('hvo_on_fail_btn')
        # else:
        #     self.click_element('success_btn')

        self.smart_click_elements([
            'hvo_on_fail_btn',
            'success_btn'
        ], max_wait_time=5)

    def rollback_success_poweroff_fail(self):
        '''
        回滚成功 下电失败
        Returns:

        '''
        self.rollback_success()
        act.set_file('remote_poweroff', 1)
        act.set_apk_file('vcu_power_hook', 1)
        time.sleep(6)
        self.click_element('success_btn')
        # self.check_element_exists('success_poweroff_fail')
        time.sleep(25)
        if act.manufacture ==1 :
            act.power_on()
            AdbExec().execute("adb shell input keyevent KEYCODE_WAKEUP")

        self.screen_image('升级失败、回滚成功、下电失败', self.mode_value)
        time.sleep(0.5)
        self.click_element('success_btn')
        act.set_apk_file('vcu_power_hook', 0)


    def tomorrow_click(self):
        self.click_element('schedule_btn')
        logger.info('点击预约升级')
        time.sleep(1)
        self.click_element('matter_argee_btn', index=0)
        logger.info('点击注意事项 继续升级按钮')
        time.sleep(12)
        self.click_element('disclaimer_argee_btn')
        logger.info('点击免责同意按钮')

    def tomorrow_time_set(self, take_screenshot=False):
        '''
        设置预约时间、默认时间 3:00
        Returns:

        '''
        self.tomorrow_click()
        self.check_element_exists('schedule_time_set_title')
        # time.sleep(0.5)
        if take_screenshot:
            time.sleep(0.5)
            self.screen_image('预约时间设置', self.mode_value)
        self.click_element('schedule_time_set_agree_btn')

    def tomorrow_time_set_pass(self, take_screenshot=False):
        '''
        设置预约时间成功
        Returns:

        '''
        self.tomorrow_time_set(take_screenshot)
        if take_screenshot:
            time.sleep(0.5)
            self.screen_image('预约中', self.mode_value)
        self.check_element_exists('schedule_time_set_success_icon')
        if take_screenshot:
            time.sleep(0.5)
            self.screen_image('预约成功弹框', self.mode_value)
        self.click_element('schedule_time_set_success_btn')
        time.sleep(0.5)
        if take_screenshot:
            time.sleep(0.5)
            self.screen_image('预约成功主页面显示', self.mode_value)
        time.sleep(0.5)

    def tomorrow_time_set_cancel(self, take_screenshot=False):
        '''
         取消预约
        Returns:

        '''
        self.click_element('schedule_cancel_btn')
        logger.info('点击取消预约按钮')

    def tomorrow_time_set_fail(self, take_screenshot=False):
        '''
          预约设置失败
        Returns:

        '''
        self.tomorrow_time_set()
        # 等待20分钟
        for _ in range(800):
            if self.check_element_exists("schedule_time_set_success_icon"):
                break
            logger.info("等待预约失败提示")
            # time.sleep(10)
        time.sleep(1)
        self.screen_image('预约设置失败', self.mode_value)
        self.click_element('matter_cancel_btn', index=1)
        logger.info('预约设置失败')

    def tomorrow_time_set_next_pass_t22(self,language):
        '''
           设置预约时间、5分钟后、最近的时间
           Returns:
        '''
        max_attempts = 5
        attempt = 0
        print(language)
        while attempt < max_attempts:
            attempt += 1
            logger.info(f"设置最近的预约时间 (尝试 {attempt}/{max_attempts})")
            self.tomorrow_click()
            self.check_element_exists('schedule_time_set_title')
            # x = int(self.height * 0.405)
            # y = int(self.width * 0.412)
            if language == "العربية (المملكة العربية السعودية)" or language =='فارسی (ایران)':
                x = int(self.height * 0.622)  # 阿拉伯坐标  # 622 358
                y = int(self.width * 0.358)
            else:
                x = int(self.height * 0.372)
                y = int(self.width * 0.352)
            self.driver.click(x, y)
            self.click_element('schedule_time_set_agree_btn')
            time.sleep(2)
            if self.check_element_exists('schedule_time_set_success_icon'):
                self.click_element('schedule_time_set_success_btn')
                break
        if attempt == 5:
            logger.error("达到最大尝试次数、设置预约失败")
        return False

    def tomorrow_time_set_next_pass_t1gc(self,language):
        '''
           设置预约时间、5分钟后、最近的时间
           Returns:
        '''
        max_attempts = 5
        attempt = 0

        while attempt < max_attempts:
            attempt += 1
            logger.info(f"设置最近的预约时间 (尝试 {attempt}/{max_attempts})")
            self.tomorrow_click()
            self.check_element_exists('schedule_time_set_title')
            # x = int(self.height * 0.405)
            # y = int(self.width * 0.412)
            if language == "العربية (المملكة العربية السعودية)" or language =='فارسی (ایران)':
                x = int(self.height * 0.600)  # 阿拉伯坐标  # 622 358
                y = int(self.width * 0.430)
            else:
                x = int(self.height * 0.405)
                y = int(self.width * 0.412)
            self.driver.click(x, y)
            self.click_element('schedule_time_set_agree_btn')
            time.sleep(2)
            if self.check_element_exists('schedule_time_set_success_icon'):
                self.click_element('schedule_time_set_success_btn')
                break
        if attempt == 5:
            logger.error("达到最大尝试次数、设置预约失败")
        return False


    def tomorrow_predition_fail_t1gc(self,language):
        if act.manufacture ==2:
            self.tomorrow_time_set_next_pass_t1gc(language)
        else:
            self.tomorrow_time_set_next_pass_t22(language)
        logger.info('等待预约时间到')
        for _ in range(30):
            if self.check_element_exists('schedule_predition_fail_title'):
                break
        time.sleep(1)

    def tomorrow_time_come(self,language):
        """预约时间到界面"""
        if act.manufacture == 2:
            self.tomorrow_time_set_next_pass_t1gc(language)
        else:
            self.tomorrow_time_set_next_pass_t22(language)
        # self.tomorrow_time_set_next_pass_t1gc(language)
        if act.manufacture ==1 :
            act.power_on()
        logger.info("等待预约时间到，红绿灯界面")
        for _ in range(60):
            if self.check_element_exists('tomorrow_time_come'):
                if act.manufacture == 1:
                    AdbExec().execute("adb shell input keyevent KEYCODE_WAKEUP")
                time.sleep(0.5)
                self.screen_image('预约时间到，等待红绿灯界面', self.mode_value)
                break

        self.click_element('tomorrow_time_come_back_btn')

    #@AllureSteps.log_step("升级中到升级成功页面,无操作")
    def update_success_no_op(self):
        """
        更新进行中 > 升级成功，无其他操作
        Returns:

        """
        self.preconditions_pass()
        self.check_element_exists('update_title')
        logger.info('更新进行中')
        time.sleep(0.5)
        self.screen_image('更新进行中')
        while True:
            time.sleep(5)
            if self.driver(resourceId='com.carota.chery:id/por_tv_title'):
                break
        time.sleep(0.5)
        self.screen_image('升级成功弹窗', self.mode_value)

    #@AllureSteps.log_step("主页面查看升级按钮可以点击")
    def check_home_button(self):
        """
          主页面查看升级按钮和发行说明可以点击
          Returns: True/False
        """
        self.click_element('now_btn')
        self.click_element('matter_cancel_btn')
        self.click_element('schedule_btn')
        back = self.click_element('matter_cancel_btn')
        return back

    #@AllureSteps.log_step("主页面查看升级按钮可以点击")
    def check_schedule_button(self):
        """
          主页面查看预约升级按钮可以点击
          Returns: True/False
        """
        self.click_element('schedule_btn')
        back = self.click_element('matter_cancel_btn')
        return back

    #@AllureSteps.log_step("主页面查看升级按钮不可点击")
    def check_home_button_nothing(self):
        """
          主页面查看升级按钮不可以点击
          Returns: True/False
        """
        self.click_element('now_btn')
        back = self.check_element_text_exists('matter_cancel_btn')

        self.click_element('schedule_btn')
        back1 = self.check_element_text_exists('matter_cancel_btn')

        if not back and not back1:
            return True
        return False

    #@AllureSteps.log_step("主页面点击详情按钮后返回,检查详情主页面是否有字符")
    def check_details_home(self):
        """
          主页面点击详情按钮后返回
          Returns: True/False
        """
        self.click_element('details_title')
        time.sleep(2)
        # result = self.check_element_exists("details_home")
        self.click_element('details_back_btn')
        # return result
        return True

    def quick_click_all(self):
        """快速多次点击所有主页面元素
        Returns:
        """
        pass

    #@AllureSteps.log_step("切换主机为白天模式")
    def switch_day(self):
        self.driver.app_start("com.desaysv.setting")
        self.click_element("display")
        self.click_element("night_day", index=1)
        self.driver.app_start(FOUR_APP_PACKAGE)

    #@AllureSteps.log_step("切换主机为黑夜模式")
    def switch_night(self):
        self.driver.app_start("com.desaysv.setting")
        self.click_element("display")
        self.click_element("night_day", index=2)
        self.driver.app_start(FOUR_APP_PACKAGE)

    def switch_languages(self, language):
        self.enter_engineering()
        time.sleep(3)
        self.click_element("Multilingual_btn")
        max_swipe = 20  # 最多滑动次数，防止死循环
        swipe_count = 0
        while True:
            # 检查当前所有可见TextView是否有目标语言
            all_texts = [tv.get_text() for tv in self.driver(className="android.widget.TextView")]
            if language in all_texts:
                logger.info(f"找到目标语言: {language}")
                self.driver(text=language).click()
                break
            else:
                swipe_count += 1
                if swipe_count > max_swipe:
                    logger.error(f"未找到目标语言: {language}，请检查列表或文本")
                    break
                # 取第1和第5个TextView的坐标滑动
                bt5 = self.driver(className="android.widget.TextView")[5]
                sbounds = bt5.info["bounds"]
                sy = sbounds["top"]
                sx = sbounds["left"]
                bt1 = self.driver(className="android.widget.TextView")[1]
                ebounds = bt1.info["bounds"]
                ey = ebounds["top"]
                ex = ebounds["left"]
                logger.info("向下滑动")
                self.driver.swipe(sx, sy, ex, ey)
                time.sleep(0.5)  # 滑动后稍等

        self.driver.app_stop('com.carota.chery')
        time.sleep(2)
        self.driver.app_start('com.carota.chery')
        time.sleep(2)
