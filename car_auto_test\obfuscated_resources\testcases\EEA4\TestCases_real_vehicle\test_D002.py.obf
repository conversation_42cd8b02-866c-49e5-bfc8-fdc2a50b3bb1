# coding: utf-8
# Project：car_auto_test
# File：test_D002.py
# Author：杨郑健
# Date ：2025/6/12 9:43
import time

from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest


@AllureDecorators.epic("前置条件检测")
@AllureDecorators.title("前置条件检测:禁止负一屏")
def test_D002(driver, launch_app):
    """
    测试步骤
    1、“升级主页面”点击“立即升级”，“免责声明”点击“同意”
    2、远程上电、进ota成功，触发“检测升级前置条件”，查看是否可下拉负一屏"

    预期结果
    2、禁止下拉负一屏
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        'power': ['ON', 'OFF'],  # 将挡位放在列表中
        "task": "normal"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'ON':
            objects.get_task()
            objects.disclaimer_click()
            assert True
        elif current_state == 'OFF':
            logger.info("OFF挡位检查在前置条件检测时能否下拉负一屏")
            sleep(5)
            objects.check_element_exists("precondition")
            objects.swipe_down()
            if objects.driver(resourceId="com.android.systemui:id/tv_qs_notification_title").exists:
                assert False
            else:
                assert True
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()


