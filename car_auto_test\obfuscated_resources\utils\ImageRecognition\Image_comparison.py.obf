'''
前置条件 X 和 √ 图像比对
'''

import cv2
import numpy as np
from PIL import Image
import io
import os
from utils.LoggingSystem.Logger import logger
from config.Config import *


class ImageRecognition:
    def __init__(self):
        # 加载模板图片
        self.x_template_path = x_image
        self.check_template_path = d_image
        self.loading_template_path = loading_image  # 加载状态的模板图片

        # 检查模板图片是否存在
        if not all(os.path.exists(path) for path in [self.x_template_path, self.check_template_path, self.loading_template_path]):
            logger.error(f"模板图片不存在: {self.x_template_path}, {self.check_template_path}, {self.loading_template_path}")
            # raise FileNotFoundError("请确保所有模板图片存在")

        self.x_template = cv2.imread(self.x_template_path, 0)  # X标记的模板图片
        self.check_template = cv2.imread(self.check_template_path, 0)  # √标记的模板图片
        self.loading_template = cv2.imread(self.loading_template_path, 0)  # 加载状态的模板图片

    def recognize_mark(self, element_image, use_color=True):
        """
        识别图片中的标记是X还是√
        Args:
            element_image: 从xpathlite获取的图片元素
            use_color: 是否使用颜色识别，默认为True
        Returns:
            bool: True表示是X标记，False表示是√标记，None表示是加载状态
        """
        try:
            if use_color:
                return self._recognize_by_color(element_image)
            else:
                pass
                # return self._recognize_by_template(element_image)
        except Exception as e:
            logger.error(f"图像识别失败: {str(e)}")
            return None

    def _recognize_by_color(self, element_image):
        """
        通过颜色识别标记
        """
        img = self._convert_to_cv2(element_image)

        # 转换为HSV颜色空间
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)

        # 定义颜色范围
        # 红色范围 (X标记)
        lower_red1 = np.array([0, 100, 100])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([160, 100, 100])
        upper_red2 = np.array([180, 255, 255])

        # 绿色范围 (√标记)
        lower_green = np.array([35, 100, 100])
        upper_green = np.array([85, 255, 255])

        # 蓝色范围 (加载状态)
        lower_blue = np.array([100, 100, 100])
        upper_blue = np.array([130, 255, 255])

        # 创建颜色掩码
        mask_red1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask_red2 = cv2.inRange(hsv, lower_red2, upper_red2)
        mask_red = cv2.bitwise_or(mask_red1, mask_red2)
        mask_green = cv2.inRange(hsv, lower_green, upper_green)
        mask_blue = cv2.inRange(hsv, lower_blue, upper_blue)

        # 计算各颜色像素数量
        red_pixels = cv2.countNonZero(mask_red)
        green_pixels = cv2.countNonZero(mask_green)
        blue_pixels = cv2.countNonZero(mask_blue)

        logger.info(f"红色像素: {red_pixels}, 绿色像素: {green_pixels}, 蓝色像素: {blue_pixels}")

        # 如果蓝色像素最多，说明是加载状态
        if blue_pixels > red_pixels and blue_pixels > green_pixels:
            logger.info('图标当前是加载状态,等待2秒后重试')
            return None
        # 存在空白截图
        elif blue_pixels == red_pixels == green_pixels:
            logger.info('图标当前是加载状态,等待2秒后重试')
            return None
        # 红色最多就是X
        if red_pixels > blue_pixels and red_pixels > green_pixels:
            logger.info('图标当前是X,前置不通过')
            return False
        # 绿色最多就是√
        if green_pixels > red_pixels and green_pixels > blue_pixels:
            logger.info('图标当前是√,前置通过')
            return True

        #
        # # 返回红色像素是否多于绿色像素
        # return red_pixels > green_pixels

    # def _recognize_by_template(self, element_image):
    #     """
    #     通过模板匹配识别标记
    #     """
    #     img = self._convert_to_cv2(element_image)
    #     gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    #
    #     template_size = (50, 50)
    #     x_template = cv2.resize(self.x_template, template_size)
    #     check_template = cv2.resize(self.check_template, template_size)
    #     loading_template = cv2.resize(self.loading_template, template_size)
    #
    #     target_size = (100, 100)
    #     gray = cv2.resize(gray, target_size)
    #
    #     x_result = cv2.matchTemplate(gray, x_template, cv2.TM_CCOEFF_NORMED)
    #     check_result = cv2.matchTemplate(gray, check_template, cv2.TM_CCOEFF_NORMED)
    #     loading_result = cv2.matchTemplate(gray, loading_template, cv2.TM_CCOEFF_NORMED)
    #
    #     x_score = cv2.minMaxLoc(x_result)[1]
    #     check_score = cv2.minMaxLoc(check_result)[1]
    #     loading_score = cv2.minMaxLoc(loading_result)[1]
    #
    #     logger.info(f"X标记匹配度: {x_score}, √标记匹配度: {check_score}, 加载状态匹配度: {loading_score}")
    #
    #     # 如果加载的颜色匹配最高就返回None进行下一次的循环
    #     if loading_score > x_score and loading_score > check_score:
    #         return None
    #     # 存在元素没加载出的情况
    #     elif loading_score == x_score and loading_score == check_score:
    #         return None
    #
    #     return x_score > check_score

    def _convert_to_cv2(self, element_image):
        """
        将元素图片转换为OpenCV格式
        """
        try:
            if isinstance(element_image, Image.Image):
                return cv2.cvtColor(np.array(element_image), cv2.COLOR_RGB2BGR)
            elif isinstance(element_image, bytes):
                return cv2.imdecode(np.frombuffer(element_image, np.uint8), cv2.IMREAD_COLOR)
            else:
                raise ValueError("不支持的图片格式")
        except Exception as e:
            logger.error(f"图片转换失败: {str(e)}")
            raise



image_recognition = ImageRecognition()




