# coding: utf-8
# Project：car_auto_test
# File：test_F075.py
# Author：杨郑健
# Date ：2025/6/25 18:35
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.OCR.Check_red_dot import check_red_dot
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from utils.AdbPort.Adb_Port import adb
from config.Config import *
from time import sleep, time
import pytest

@AllureDecorators.epic("升级结果")
@AllureDecorators.title("“升级失败”下电失败：升级主页面")
def test_F075(driver, launch_app):
    """
    测试步骤
    1、篡改ecu升级包、回滚包，变更ecu基础版本,模拟下电失败,“更新进行中”待升级完成，升级失败，回滚失败，执行下电失败
    2、“升级失败”点击“确定”
    3、查看“升级主页面”显示

    预期结果
    3、“当前系统已是最新版本”，升级时长被擦除，保留本次升级的“发行说明”，点击“详情”可查阅全文，“预约升级”、“立即升级”置灰，不可点击
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        "tbox_status": "NOTREAD",
        "power": ["READY", "OFF"],
        "task": "normal",
        "gear": "P",
        "handbrake": "OFF"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'READY':
            objects.get_task()
            logger.info("READY挡验证完成")
            assert True
        elif current_state == 'OFF':
            logger.info("OFF挡升级失败显示，查看埋点数据")
            serial_tool.ecu_uds_shell()
            objects.rollback_success_poweroff_fail()
            objects.check_home_status()
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()