# coding: utf-8
# Project：car_auto_test
# File：test_A027.py
# Author：杨郑健
# Date ：2025/6/10 16:56
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("信息合法性校验")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_A027(driver, launch_app):
    """
    测试步骤
    1、电源挡位切换为READY，任务为正常
    2、更新ecu
    3、查看更新后是否还可以收到升级任务（后台配置了升降级任务，直接对比升级前后检测到的任务名称是不是一致）

    预期结果
    3、无法检测到该ecu升级任务
    """
    global old_task,new_task
    objects = Step_object(driver)
    objects.get_task()
    info = Cloud("奇瑞汽车").get_carinfo()
    for i in range(len(info)):
        if info[i]["message"] == "7.检测升级返回" and "data" in info[i]:
            old_task = info[i]["data"]["result"]["releaseNote"]
    objects.disclaimer_click()
    objects.update_success()
    objects.get_task()
    info = Cloud("奇瑞汽车").get_carinfo()
    for i in range(len(info)):
        if info[i]["message"] == "7.检测升级返回" and "data" in info[i]:
            new_task = info[i]["data"]["result"]["releaseNote"]

    assert old_task == new_task and new_task != None