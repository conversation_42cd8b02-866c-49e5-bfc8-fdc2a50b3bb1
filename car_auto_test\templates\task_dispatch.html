<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动化任务下发</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
    <style>
        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .page-switch {
            display: flex;
            gap: 10px;
        }

        .badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .badge-warning {
            background-color: #ffc107;
            color: #000;
        }

        .badge-success {
            background-color: #28a745;
            color: #fff;
        }

        .badge-danger {
            background-color: #dc3545;
            color: #fff;
        }

        .input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
    </style>
</head>
<body>
    <!-- 页面头部区域 -->
    <div class="header">
        <div class="header-content">
            <div class="logo-section">
                <h1>自动化任务下发</h1>
                <div class="subtitle">Automation Task Dispatch</div>
            </div>
            <div class="header-right">
                <div class="page-switch">
                    <button id="switchToUI" class="btn btn-outline-primary">UI测试</button>
                    <button id="switchToTask" class="btn btn-outline-primary active">任务下发</button>
                </div>
                <div class="brand-info">
                    <div class="brand-logo">OTA</div>
                    <div>远程升级 · 品质生活</div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 控制面板区域 -->
        <div class="control-panel">
            <div class="control-buttons">
                <input id="deviceId" class="input" type="text" placeholder="请输入设备ID" style="width:180px;">
                <select id="carModel" class="input" style="width:120px;margin-left:10px;">
                    <option value="EEA5">EEA5</option>
                    <option value="EEA4">EEA4</option>
                    <option value="其他">其他</option>
                </select>
                <button id="dispatchTaskBtn" class="btn btn-success" style="margin-left:20px;">下发任务</button>
            </div>
        </div>

        <div class="main-content">
            <div class="test-table-section">
                <h2 class="section-title">📋 用例选择</h2>
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="test-table">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAll"></th>
                                    <th>用例编号</th>
                                    <th>用例名称</th>
                                    <th>描述</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                    <div class="table-body-container">
                        <table class="test-table">
                            <tbody id="testCaseTableBody">
                                <!-- 测试用例数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="pagination-container">
                    <div class="pagination" id="pagination">
                        <button id="prevPage">上一页</button>
                        <div class="pagination-info">
                            <span id="pageInfo">第 1 页，共 1 页</span>
                            <span id="totalInfo">共 0 条记录</span>
                        </div>
                        <button id="nextPage">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div>© 2024 OTA自动化平台</div>
    </footer>

    <script src="{{ url_for('static', filename='js/task_dispatch.js') }}"></script>
    <script>
        // 测试用例数据
        const testCases = [
            { id: 'test_A001', name: '没有任务得状态显示', description: '预期结果我会制造失败', status: '待执行' },
            { id: 'test_A002', name: '返回主页面', description: '预期结果我会制造成功', status: '待执行' },
            { id: 'test_D003', name: '主题切换测试', description: '验证系统主题切换功能及显示效果', status: '待执行' },
            { id: 'test_D004', name: '网络连接测试', description: '测试网络连接状态显示及切换功能', status: '待执行' },
            { id: 'test_D005', name: '蓝牙功能测试', description: '验证蓝牙设备搜索和连接功能', status: '待执行' },
            { id: 'test_D006', name: '音量控制测试', description: '测试系统音量调节功能及显示效果', status: '待执行' },
            { id: 'test_D007', name: '亮度调节测试', description: '验证屏幕亮度调节功能及显示效果', status: '待执行' },
            { id: 'test_D008', name: '时间设置测试', description: '测试系统时间设置及显示功能', status: '待执行' },
            { id: 'test_D009', name: '系统更新测试', description: '验证系统更新检查及提示功能', status: '待执行' },
            { id: 'test_D0010', name: '系统信息显示测试', description: '测试系统信息页面显示及数据准确性', status: '待执行' }
        ];

        // 页面加载完成后初始化表格
        document.addEventListener('DOMContentLoaded', function() {
            // 渲染测试用例表格
            const tbody = document.getElementById('testCaseTableBody');
            tbody.innerHTML = testCases.map(testCase => `
                <tr>
                    <td><input type="checkbox" class="case-checkbox" data-id="${testCase.id}"></td>
                    <td>${testCase.id}</td>
                    <td>${testCase.name}</td>
                    <td>${testCase.description}</td>
                    <td><span class="badge badge-warning">${testCase.status}</span></td>
                </tr>
            `).join('');

            // 全选功能
            document.getElementById('selectAll').addEventListener('change', function(e) {
                const checkboxes = document.querySelectorAll('.case-checkbox');
                checkboxes.forEach(checkbox => checkbox.checked = e.target.checked);
            });

            // 页面切换
            document.getElementById('switchToUI').onclick = function() {
                window.location.href = '/';
            };

            // 任务下发功能（关键修改部分）
            document.getElementById('dispatchTaskBtn').addEventListener('click', async function() {
                const deviceId = document.getElementById('deviceId').value.trim();
                const carModel = document.getElementById('carModel').value;

                // 获取选中的用例（包含编号和描述）
                const selectedCases = Array.from(document.querySelectorAll('.case-checkbox:checked'))
                    .map(checkbox => {
                        const caseId = checkbox.dataset.id;
                        // 从testCases中获取对应的完整用例数据
                        const fullCase = testCases.find(tc => tc.id === caseId);
                        return {
                            id: caseId,
                            description: fullCase.description  // 新增描述字段
                        };
                    });

                if (!deviceId) {
                    alert('请输入设备ID');
                    return;
                }

                if (selectedCases.length === 0) {
                    alert('请选择至少一个测试用例');
                    return;
                }

                try {
                    const response = await fetch('/api/dispatch_task', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        // 发送包含id和description的对象数组
                        body: JSON.stringify({
                            deviceId: deviceId,
                            carModel: carModel,
                            testCases: selectedCases  // 修改为对象数组
                        })
                    });

                    const result = await response.json();

                    if (result.status === 'success') {
                        alert('任务下发成功');
                        // 更新表格状态（保持原有逻辑）
                        selectedCases.forEach(caseObj => {
                            const row = document.querySelector(`.case-checkbox[data-id="${caseObj.id}"]`).closest('tr');
                            const statusCell = row.querySelector('.badge');
                            statusCell.className = 'badge badge-success';
                            statusCell.textContent = '已下发';
                        });
                    } else {
                        alert(result.message || '任务下发失败');
                    }
                } catch (error) {
                    console.error('任务下发失败:', error);
                    alert('任务下发失败，请检查网络连接');
                }
            });
        });
    </script>
</body>
</html>
