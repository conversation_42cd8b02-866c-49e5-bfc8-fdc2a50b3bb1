# coding: utf-8
# Project：car_auto_test
# File：test_C016.py
# Author：杨郑健
# Date ：2025/6/11 18:14
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级主页面/小红点")
@AllureDecorators.title("埋点数据：点击立即升级")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_C016(driver, launch_app):
    """
    测试步骤
    1、替换发行说明检测到升级任务并下载完成
    2、检查点击立即升级后云端埋点 (code 11) 点击主页面"立即升级"按钮

    预期结果
    2、（code11）“点击主页面立即升级按钮”
    """
    objects = Step_object(driver)
    objects.get_task()
    objects.appointment_upgrade_click()
    burying_points = Cloud("奇瑞汽车").get_burying_point()
    result = False
    for burying_point in burying_points:
        if burying_point["msg"] == '(code 11) 点击主页面"立即升级"按钮':
            result = True
    assert result