# coding: utf-8
# Project：car_auto_test
# File：test_B010.py
# Author：杨郑健
# Date ：2025/6/11 14:12
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级包下载模块")
@AllureDecorators.title("新版本提示框:不点击查看")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_B010(driver, launch_app):
    """
    测试步骤
    1、车辆解防，切换电源至on档，待车端检测到升级任务并下载完成，切换电源至off档，触发新版本提示框
    2、新版本提示框不点击“查看”"

    预期结果
    2、10秒内新版本提示框不会关闭，10秒后自动关闭，回到弹框下页面
    """
    pass