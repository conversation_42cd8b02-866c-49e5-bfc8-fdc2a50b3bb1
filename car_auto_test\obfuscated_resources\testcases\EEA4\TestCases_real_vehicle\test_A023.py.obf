# coding: utf-8
# Project：car_auto_test
# File：test_A023.py
# Author：杨郑健
# Date ：2025/6/10 16:39
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from time import sleep
import pytest

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("收到升级任务")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_A023(driver, launch_app):
    """
    测试步骤
    1、电源挡位切换为READY
    2、任务检测结束后检查拿到任务的状态
    3、云端查看活动日志中是否有（收到升级任务）关键字

    预期结果
    3、Clientresponseisclm:总状态[收到升级任务]状态:[收到升级任务]
    """
    objects = Step_object(driver)
    get_task_result = objects.get_task()
    result = False
    info = Cloud("奇瑞汽车").get_carinfo()
    for i in info:
        if i["massage"].rfind("收到升级任务") != -1:
            result = True
    assert get_task_result and result