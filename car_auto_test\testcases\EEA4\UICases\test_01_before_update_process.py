'''
升级前各页面UI
'''

import pytest
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.ImageRecognition.Image_comparison import *
from config.api_config import APIConfig

class Test_Bench_Before_Update_Process:
    mode_value: int = None
    mode_dict: dict = None
    vehicle_flag: int = None

    # 使用mode fixture获取当前的模式值
    @pytest.fixture(autouse=True)
    def setup_ui_mode(self, mode, mode_dict, vehicle_flag):
        """设置当前的UI模式和mode_dict以及车型类别"""
        self.mode_value = mode
        self.mode_dict = mode_dict
        self.vehicle_flag = vehicle_flag
        logger.info(f"当前测试环境: 模式值={self.mode_value}, 车型类别={self.vehicle_flag}")

    # @pytest.mark.skip
    def test_before_update_process_001(self, driver, ui_eea4_app):
        """
        正常流程
        """
        objects = Step_object(driver, self.mode_value, self.mode_dict, self.vehicle_flag)
        # # 没有任务时
        objects.no_task()
        # 工程模式密码输入框
        objects.project_password()
        # 拿到升级任务
        objects.get_task(take_screenshot=True)
        # # 返回主页面截图
        objects.back_home()
        # 点击详情
        objects.details_click()
        # # 点击隐私声明
        objects.privacy_click()
