# from utils.SerialTool.serial_tool import serial_tool
from utils.SerialTool import qnx_execute
# from utils.SerialTool.vcc_tool import vcc
from utils.LoggingSystem.Logger import logger
from utils.AdbPort.Adb_Port import adb

class VehicleControl:
    """车型控制类，统一管理车型标志位"""

    def __init__(self, vehicle_flag=None):
        self.vehicle_flag = vehicle_flag
    
    def set_vehicle_flag(self, vehicle_flag):
        self.vehicle_flag = vehicle_flag
        logger.info(f"设置车型标志位: {vehicle_flag}")
    
    def set_file(self, *args):
        """根据车型标志位设置配置文件"""
        if self.vehicle_flag == 1:  # T22
            pass
            # return serial_tool.set_file(*args)
        elif self.vehicle_flag == 2:  # M1E
            pass
            # return serial_tool.set_file(*args)
        elif self.vehicle_flag == 3:  # T1GC
            return qnx_execute.set_file(*args)
        elif self.vehicle_flag == 4:  # E0Y
            pass
            # return vcc.set_file(*args)
        elif self.vehicle_flag == 5:  # TEST
            pass
            # return serial_tool.set_file(*args)
        elif self.vehicle_flag == 6:  # T1ej
            return adb.set_file(*args)

        else:
            logger.warning(f"未知车型标志位 {self.vehicle_flag}，默认使用serial_tool.set_file")
            # return serial_tool.set_file(*args)


    def set_apk_file(self,*args):
        """设置apk打桩文件"""
        if self.vehicle_flag == 3:
            return adb.set_file(*args)

    def power_on(self):
        """根据车型标志位执行上电"""
        if self.vehicle_flag is None:
            logger.error("车型标志位未设置，请先调用set_vehicle_flag()")
            return False
            
        # logger.info(f"根据车型标志位 {self.vehicle_flag} 调用对应的power_on方法")
        
        if self.vehicle_flag == 1:  # T22
            pass
            # return serial_tool.power_on()
        elif self.vehicle_flag == 2:  # M1E
            pass
            # return serial_tool.power_on()
        elif self.vehicle_flag == 3:  # T1GC
            return qnx_execute.power_on()
        elif self.vehicle_flag == 4:  # E0Y
            return False
        elif self.vehicle_flag == 5:  # TEST
            pass
            # return serial_tool.power_on()
        else:
            logger.warning(f"未知车型标志位 {self.vehicle_flag}，默认使用serial_tool.power_on")
            # return serial_tool.power_on()

    def ota_mode_out(self):
        if self.vehicle_flag == 1:  # T22
            pass
            # return serial_tool.power_on()
        elif self.vehicle_flag == 2:  # M1E
            pass
            # return serial_tool.power_on()
        elif self.vehicle_flag == 3:  # T1GC
            qnx_execute.ota_mode_out()
        elif self.vehicle_flag == 4:  # E0Y
            return False
        elif self.vehicle_flag == 5:  # TEST
            pass
            # return serial_tool.power_on()
        else:
            pass
            # logger.warning(f"未知车型标志位 {self.vehicle_flag}，默认使用serial_tool.power_on")
            # return serial_tool.power_on()


    def rm_package(self):
        if self.vehicle_flag == 1:
            pass
            # serial_tool.rm_package()

        if self.vehicle_flag ==3:
            qnx_execute.rm_package()



# # 创建全局实例

vehicle_control = VehicleControl()
# vehicle_control.ota_mode_out()
# vehicle_control.power_on()
# vehicle_control.set_file('ibs_soc', 1)
# vehicle_control.set_file('otamodein',0)
# vehicle_control.ota_mode_out()
# qnx_execute.ota_mode_out()


#

    