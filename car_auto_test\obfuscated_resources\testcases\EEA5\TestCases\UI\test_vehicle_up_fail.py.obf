'''
升级失败
1、升级失败
2、升级失败，下电失败
3、升级失败、退ota失败
13h
'''
import time

import pytest
import functools
from testcases.EEA5.PageObjects.TestObject import Step_object
from utils.ImageRecognition.Image_comparison import *
from utils.SerialTool.vcc_tool import vcc


class Test_Vehicle_Up_Fail:
    # 使用mode fixture获取当前的模式值
    @pytest.fixture(autouse=True)
    def setup_ui_mode(self, mode, mode_dict):
        """设置当前的UI模式和mode_dict"""
        self.mode_value = mode
        self.mode_dict = mode_dict
        logger.info(f"当前测试环境: 模式值={self.mode_value}")

    # @pytest.mark.skip
    def test_vehicle_up_fail_001(self, driver, ui_app):
        """
        升级失败
        """
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.get_task()
        vcc.vcc_execute('rm  -r /opt/ota_pkg/dm/')
        objects.upgrade_fail_page()

    # @pytest.mark.skip
    def test_vehicle_up_fail_002(self, driver, ui_app):
        """
        升级失败、远程下电失败
        """
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.get_task()
        vcc.vcc_execute('rm  -r /opt/ota_pkg/dm/')
        vcc.set_file('remote_poweroff',1)
        time.sleep(10)
        objects.upgrade_fail_poweroff_fail()
        objects.check_element_exists('upgrade_success_page_btn')
        objects.screen_image('升级失败、下电失败', self.mode_value)
        objects.click_element('upgrade_success_page_btn')

    # @pytest.mark.skip
    def test_vehicle_up_fail_003(self, driver, ui_app):
        """
        升级失败、退出ota失败
        """
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.get_task()
        vcc.vcc_execute('rm  -r /opt/ota_pkg/dm/')
        vcc.set_file('otamodeout', 1)
        time.sleep(10)
        objects.upgrade_fail_poweroff_fail()
        objects.screen_image('升级失败、退出ota模式失败', self.mode_value)
        objects.click_element('upgrade_fail_page_btn')
        vcc.set_file('otamodeout', 0)
        time.sleep(10)


