# coding: utf-8
# Project：car_auto_test
# File：Check_red_dot.py
# Author：杨郑健
# Date ：2025/6/17 9:49
from utils.AdbPort.Adb_Port import AdbExec
from config.Config import BASE_DIR
import cv2
import numpy as np
import json

def check_red_dot(threshold=200):
    """ 检查主页面OTA APP有无小红点显示
    Args:
        threshold: 红色阈值，当前经过测试，如果有小红点红色的阈值会超过200

    Returns: True/False    有小红点就返回True，无则False

    """
    path = BASE_DIR + "/utils/OCR/home.png"
    adb = AdbExec()
    r_back = adb.execute("adb shell dumpsys activity top | findstr carota")
    bounds = json.loads(r_back.split("bnds=")[2][:-3].replace("][",","))

    adb.screen_capture(path)
    img = cv2.imread(path)
    x1, y1, x2, y2 = bounds
    icon_region = img[y1:y2, x1:x2]
    cv2.imwrite(path, icon_region)
    img = cv2.imread(path)
    lower_red = np.array([0, 0, 200])  # 最低红色阈值
    upper_red = np.array([80, 80, 255])  # 最高红色阈值
    mask = cv2.inRange(img, lower_red, upper_red)
    red_pixels = cv2.countNonZero(mask)
    return red_pixels > threshold
