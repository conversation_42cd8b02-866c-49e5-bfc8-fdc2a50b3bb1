"""
项目入口文件
"""
from flask import Flask, render_template, jsonify, request, send_from_directory, abort
from config.api_config import APIConfig
from api.task_api import task_bp
from api.ui_screenshot_api  import screenshot_bp
from api.start_test_api  import start_test_bp
from api.report_api import report_bp

# 优先使用简单解混淆器，如果不可用则尝试资源管理器，最后回退到原始路径
try:
    # 尝试导入解混淆器
    from simple_deobfuscator import get_resource_path
    import atexit
    from simple_deobfuscator import _deobfuscator
    atexit.register(_deobfuscator._cleanup)  # 程序退出时清理临时文件
    RESOURCE_MODE = "OBFUSCATED"
    # print("Flask应用使用混淆资源模式")
except ImportError:

    # 开发模式回退
    def get_resource_path(path):
        return path
    RESOURCE_MODE = "DEVELOPMENT"
    # print("Flask应用使用开发资源模式")

def create_app():
    # 使用资源路径管理器初始化Flask应用
    app = Flask(__name__,
                template_folder=get_resource_path('templates'),
                static_folder=get_resource_path('static'))
    # 加载配置
    app.config.from_object(APIConfig)

    # 注册蓝图
    app.register_blueprint(task_bp)
    app.register_blueprint(screenshot_bp)
    app.register_blueprint(start_test_bp)
    app.register_blueprint(report_bp)

    @app.route('/')
    def index():
        """主页面"""
        return render_template('dashboard.html')

    @app.route('/task_dispatch')
    def task_dispatch():
        """任务下发页面"""
        return render_template('task_dispatch.html')

    return app

#
# if __name__ == '__main__':
#     app = create_app()
#
#     # 配置日志
#     import logging
#     log = logging.getLogger('werkzeug')
#     log.setLevel(logging.INFO)
#     # 启动应用
#     app.run(
#         host=APIConfig.HOST,   # 关闭调试模式，防止自动重启
#         port=APIConfig.PORT,
#         debug=APIConfig.DEBUG,
#         threaded=APIConfig.THREADED,
#         use_reloader=False  # 禁用自动重载
#     )
