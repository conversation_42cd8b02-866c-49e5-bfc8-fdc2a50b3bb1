import time
import pytest
from functools import wraps
from utils.LoggingSystem.Logger import logger
from utils.ZlgCan.zlgcan import *
from config.Config import normal_task,abnormal_task,PLATFORM
from utils.CloudOperation.chery_operation import Cloud

def static_condition(conditions=None):
    """
    Args: 默认不变更信号装饰器
        conditions:
          "engine": {
            "on": "发动机启动",
            "off": "发动机关闭"
        },
        "power": {
            "on": "on挡",
            "acc": "acc挡",
            "off": "off档"
        },
        "speed": {
            0,
            50
        },
        "handbreak": {
            "on": "手刹拉起",
            "off": "手刹放下"
        },
        "voltage": {
          5，
              12.2，
              12.3，
              12.4
        },
        "gear":{
                P,
                R,
                N,
                D
        },
       "charge": {
                "on": "充电",
                "off": "未充电"
        },
      "discharge": {
            "on": "放电",
            "off": "未放电"
        },
      “vehiclemode”： {
            “Factory”:
            "Factory_pause"
      }
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if conditions:
                #  获取周立功对象
                devices = open_devices()
                if devices:
                    handle, chn_handle = devices[1]
                logger.info(f"开始执行用例{func.__name__}的前置条件")
                # 首先处理模拟信号,周立功初始化失败会跳过模拟信号
                thread_name = []
                for signal in conditions.keys():
                    if signal in can_signal.keys() and chn_handle:
                        logger.info(f"开始模拟信号,设置{signal} 为{conditions[signal]}")
                        thread_name.append(start_can({signal: conditions[signal]}, chn_handle=chn_handle))
                # 录制周立功报文
                for access in range(len(devices)):
                    logger.info(f"开始录制通道{access}的报文")
                    handle1, chn_handle1 = devices[access]
                    thread_name.append(start_receive(chn_handle=chn_handle1, access=0, file_path=ZLGLOG_PATH + f"/{func.__name__}.txt"))


                # 云端任务处理
                # 目前分正向任务和非正向任务，非正向任务需要传入任务名称
                if "task" in conditions.keys():
                    web = Cloud(PLATFORM)
                    could_task = web.get_open_task().keys()
                    # 判断云端任务是否与本地任务一致，不一致则同步任务
                    for t in normal_task:
                        if t not in could_task:
                            logger.info("同步云端任务！")
                            if conditions["task"] == "normal":
                                web.sync_task(normal_task)
                            else:
                                web.sync_task(conditions["task"])

                try:
                    fuc = func(*args, **kwargs)  # 用例主体
                except Exception as e:
                    logger.info(e)
                    # 用例结束后关闭周立功线程
                    [stop_can(name) for name in thread_name]
                    close_zlg(handle, chn_handle)
                    assert False  # 此处抛出异常证明用例fail了，往上翻找log查看，此报错为解决周立功线程不被占用，谨慎修改
                # 用例结束后关闭周立功线程
                [stop_can(name) for name in thread_name]
                close_zlg(handle, chn_handle)
                return fuc
            else:
                logger.info(f'用例{func.__name__}没有前置条件')
                return func(*args, **kwargs)  # 如果没有条件，直接执行函数
        return wrapper
    return decorator


# TODO 千万别动！！！ 动了你自己改回来
def dynamic_condition(conditions=None, delay=None):
    """
    动态条件装饰器
    Args:
        conditions:
        {
            'gear':[D,R,P,N],
            'handbrake':ON
        }
        delay: 状态变化时间，如果为None则等待断言完成
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if conditions:
                logger.info(f"开始执行用例{func.__name__}的前置条件")
                # 云端任务处理
                if "task" in conditions.keys():
                    web = Cloud("奇瑞汽车")
                    could_task = web.get_open_task().keys()
                    # 判断云端任务是否与本地任务一致，不一致则同步任务
                    for t in normal_task:
                        if t not in could_task:
                            logger.info("同步云端任务！")
                            if conditions["task"] == "normal":
                                web.sync_task(normal_task)
                            else:
                                web.sync_task(conditions["task"])
                # 周立功信号处理
                # 获取周立功对象
                devices = open_devices()
                if devices:
                    handle, chn_handle = devices[0]
                thread_name = []
                for condition, value in conditions.items():
                    if not isinstance(value, list):
                        if condition in can_signal.keys() and chn_handle:
                            logger.info(f"开始模拟信号,设置{condition} 为{conditions[condition]}")
                            thread_name.append(start_can({condition: conditions[condition]}, chn_handle=chn_handle))
                            try:
                                func(*args, **kwargs)  # 用例主体
                            except Exception as e:
                                logger.info(e)
                                # 用例结束后关闭周立功线程
                                [stop_can(name) for name in thread_name]
                                close_zlg(handle, chn_handle)
                                assert False  # 此处抛出异常证明用例fail了，往上翻找log查看，此报错为解决周立功线程不被占用，谨慎修改
                    else:
                        if condition in can_signal.keys() and chn_handle:
                            for i, state in enumerate(value):
                                logger.info(f"开始模拟信号,设置{condition} 为{state}")
                                # 发送当前挡位信号
                                task_name = start_can({condition: state}, chn_handle=chn_handle)
                                time.sleep(1)  # 等待信号发送完成
                                # 执行用例逻辑 - 传递当前挡位索引和状态
                                kwargs['current_index'] = i
                                kwargs['current_state'] = state
                                try:
                                    func(*args, **kwargs)
                                except Exception as e:
                                    logger.info(e)
                                    # 用例结束后关闭周立功线程
                                    stop_can(task_name)
                                    close_zlg(handle, chn_handle)
                                    assert False  # 此处抛出异常证明用例fail了，往上翻找log查看，此报错为解决周立功线程不被占用，谨慎修改
                                # 停止当前挡位的信号发送
                                stop_can(task_name)
                                time.sleep(1)  # 给系统一点时间处理状态变化
                # 最后关闭周立功线程
                [stop_can(name) for name in thread_name]
                close_zlg(handle, chn_handle)
                return True  # 返回成功标志
            else:
                logger.info(f'用例{func.__name__}没有前置条件')
                return func(*args, **kwargs)  # 如果没有条件，直接执行函数

        return wrapper
    return decorator





def retry_on_exception(max_retries=5, delay=0):
    """异常时重试装饰器，最终失败只记录日志不抛出异常"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            for attempt in range(1, max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    logger.error(f"{func.__name__} 第{attempt}次失败: {e}")
                    if attempt < max_retries:
                        time.sleep(delay)
            logger.error(f"{func.__name__} 重试{max_retries}次后依然失败，跳过此步骤")
        return wrapper
    return decorator