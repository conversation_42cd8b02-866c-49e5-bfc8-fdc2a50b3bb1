'''
正常流程
'''

import time
import pytest
import functools
from testcases.EEA5.PageObjects.TestObject import Step_object
from utils.ImageRecognition.Image_comparison import *
from utils.SerialTool.vcc_tool import vcc

class Test_Bench_Normal_Process:
    # 使用mode fixture获取当前的模式值
    @pytest.fixture(autouse=True)
    def setup_ui_mode(self, mode, mode_dict):
        """设置当前的UI模式和mode_dict"""
        self.mode_value = mode
        self.mode_dict = mode_dict
        logger.info(f"当前测试环境: 模式值={self.mode_value}")
    #
    # @pytest.mark.skip
    def test_bench_normal_process_001(self, driver, ui_app):
        """
        正常流程
        """
        #
        objects = Step_object(driver, self.mode_value, self.mode_dict)

        # 没有任务时
        objects.no_task()

        # 拿到升级任务
        objects.get_task()
        # 返回主页面截图
        objects.back_home()
        # 历史版本
        objects.history_version()

        # 点击详情
        objects.details_click()

        # 点击隐私声明
        objects.privacy_click()

        # 点击注意事项、免责
        objects.precautions_click()


    def test_bench_normal_process_002(self, driver, ui_app):
        """
        正常流程
        """
        #
        objects = Step_object(driver, self.mode_value, self.mode_dict)

        objects.back_home()
        assert True

