# coding: utf-8
# Project：car_auto_test
# File：test_D004.py
# Author：杨郑健
# Date ：2025/6/12 14:18
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("前置条件检测")
@AllureDecorators.title("前置条件检测:电源ON挡")
def test_D004(driver, launch_app):
    """
    测试步骤
    1、切换电源档位至off档，查看“检测升级前置条件”
    2、切换电源档位至acc档，查看“检测升级前置条件”
    3、启动车辆，查看“检测升级前置条件”
    4、切换电源至on档，查看“检测升级前置条件”

    预期结果
    1、电源off档“电源处于on”检测不通过
    2、电源acc档“电源处于on”检测不通过
    3、启动车辆“电源处于on”检测不通过
    4、“电源处于on”检测通过
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        "tbox_status": "NOTREAD",
        "power": ["OFF", "ACC", "READY", "ON"],
        "task": "normal"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")
        check_status = objects.check_element_exists("precondition")
        # 根据当前挡位进行不同的断言
        if current_state == 'OFF':
            assert check_status == True, "OFF挡时应该停留在前置条件检测页面"
            logger.info("OFF挡验证完成")
        elif current_state == 'ACC':
            assert check_status == True, "ACC挡时应该停留在前置条件检测页面"
            logger.info("ACC挡验证完成")
        elif current_state == 'READY':
            assert check_status == True, "READY挡时应该停留在前置条件检测页面"
            logger.info("READY挡验证完成")
        elif current_state == 'ON':
            assert check_status == False, "ON挡应该通过前置检测"
            logger.info("ON挡验证完成")
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()
