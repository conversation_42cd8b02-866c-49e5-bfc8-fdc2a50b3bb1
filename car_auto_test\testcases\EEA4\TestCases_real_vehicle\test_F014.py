# coding: utf-8
# Project：car_auto_test
# File：test_F014.py
# Author：杨郑健
# Date ：2025/6/23 15:50
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest


@AllureDecorators.epic("升级结果")
@AllureDecorators.title("“升级完成，退出升级模式失败”：升级主页面")
def test_F014(driver, launch_app):
    """
    测试步骤
    1、“更新进行中”待升级完成，升级成功，执行退ota失败
    2、“升级完成，退出升级模式失败”点击“确定”
    3、查看“升级主页面”显示

    预期结果
    3、“当前系统已是最新版本”，升级时长被擦除，保留本次升级的“发行说明”，点击“详情”可查阅全文，“预约升级”、“立即升级”置灰，不可点击
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        "tbox_status": "NOTREAD",
        "power": ["READY", "OFF"],
        "task": "normal",
        "gear": "P",
        "handbrake": "OFF"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'READY':
            objects.get_task()
            logger.info("READY挡验证完成")
            assert True
        elif current_state == 'OFF':
            logger.info("OFF挡升级失败")
            objects.update_success_otamodeout_fail()
            objects.click_element("success_otamode_fail")
            objects.check_home_status()
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()