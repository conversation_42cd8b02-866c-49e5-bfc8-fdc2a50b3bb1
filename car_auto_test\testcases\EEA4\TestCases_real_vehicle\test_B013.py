# coding: utf-8
# Project：car_auto_test
# File：test_B013.py
# Author：杨郑健
# Date ：2025/6/11 14:17
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级包下载模块")
@AllureDecorators.title("升级包下载完成后：小红点")
@static_condition(conditions=
{
    "power": "ACC",
    "tbox_status": "READ",
    "task": "normal"
}
)
def test_B013(driver, launch_app):
    """
    测试步骤
    1、后台配置“用户确认”升级任务
    2、车辆解防上电，车端检测到升级任务并下载完成
    3、查看ota应用图标

    预期结果
    3、右上角有小红点显示
    """
    objects = Step_object(driver)
    oldhash = objects.get_app_hash()
    objects.get_task()
    currhash = objects.get_app_hash()
    assert oldhash != currhash
    AdbExec().home()