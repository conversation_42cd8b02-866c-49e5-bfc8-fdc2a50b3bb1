# coding: utf-8
# Project：car_auto_test
# File：test_A019.py
# Author：杨郑健
# Date ：2025/6/10 16:23
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from utils.AdbPort.Adb_Port import adb
from time import sleep
import pytest

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("tbox离线状态使用wifi进行检测")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_A019(driver, launch_app):
    """
    测试步骤
    1、电源挡位切换为READY
    2、tbox 离线状态
    3、任务检测结束后检查拿到任务的状态，tsp离线状态

    预期结果
    3、正常进行检测，后台对应vin码活动日志有相关信息输出
    """
    adb.open_wifi()
    objects = Step_object(driver)
    results = otaLogAnalysis.find_keywords_with_timeout(["el-diagnose: TEL_DIAGNOSE_NOT_READ"], 20)
    get_task_result = objects.get_task()
    assert get_task_result and results