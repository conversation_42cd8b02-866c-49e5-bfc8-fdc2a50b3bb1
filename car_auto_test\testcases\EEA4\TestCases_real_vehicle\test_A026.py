# coding: utf-8
# Project：car_auto_test
# File：test_A026.py
# Author：杨郑健
# Date ：2025/6/10 16:52
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("任务过期")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_A026(driver, launch_app):
    """
    测试步骤
    1、电源挡位切换为READY，任务为正常
    2、任务检测中关闭任务
    3、查看是否能检测到任务

    预期结果
    3、无法检测到升级任务
    """
    objects = Step_object(driver)
    objects.get_task_noback()
    cloud = Cloud("奇瑞汽车")
    cloud.sync_task([])
    get_task_result = objects.check_update_status()
    cloud.sync_task(normal_task)
    assert get_task_result