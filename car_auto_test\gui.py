from datetime import datetime
from operator import index

import requests
import ttkbootstrap as ttk
# from pygments.styles.dracula import foreground
from ttkbootstrap.constants import *
import tkinter as tk
from tkinter import ttk as tk_ttk
import time
import uuid
import threading
import random
from threading import Event
import urllib.request
import urllib.parse
import json
import pytest
from utils.AdbPort.Adb_Port import AdbExec
from utils.LoggingSystem.Logger import logger
from utils.SerialTool.vcc_tool import vcctool


class Qauto:
    def __init__(self, root):
        self.root = root
        self.root.title("OTA自动化测试工具 Qauto")
        self.root.geometry("1200x1200")
        self.root.resizable(False, False)
        self.style = ttk.Style(theme='minty')
        self.style.configure('.', font=('微软雅黑', 10))
        self.style.configure('TFrame', background='#f8f9fa')
        self.style.configure('TLabe<PERSON>rame.Label',
                            font=('微软雅黑', 14, 'bold'),
                            background='white',
                            foreground='#2c3e50')

        # 生成设备ID（格式：ota-8位随机十六进制数）
        # self.device_id = f"ota-{uuid.uuid4().hex[:8]}"
        self.device_id = "123456"

        # 设备状态管理（存储设备名称、状态及对应界面控件）
        self.devices = {
            'ADB': {'status': '未检测', 'widget': None},
            'ZLG': {'status': '未检测', 'widget': None},
            '串口': {'status': '未检测', 'widget': None},
            'SSH': {'status': '未检测', 'widget': None}
        }

        # 测试流程控制变量
        self.test_running = False       # 测试运行状态标记
        self.pause_event = Event()      # 暂停控制事件（线程同步工具）
        self.pause_event.set()          # 初始为"继续"状态（事件已设置）
        self.all_cases = []             # 存储测试用例数据



        self.root.grid_rowconfigure(0, weight=0)  # 按钮区不自动扩展
        self.root.grid_rowconfigure(1, weight=0)  # 设备状态区不自动扩展
        self.root.grid_rowconfigure(2, weight=3)  # 用例列表区占主要空间（权重3）
        self.root.grid_rowconfigure(3, weight=1)  # 进度条区占次要空间（权重1）
        # 列权重设置（窗口水平扩展时占满可用空间）
        self.root.grid_columnconfigure(0, weight=1)

        self.create_widgets()

    def create_widgets(self):
        """创建所有界面组件"""
        # ------------------------ 按钮操作区 ------------------------
        self.btn_frame = ttk.Frame(self.root, padding=15)
        self.btn_frame.grid(row=0, column=0, sticky='ew', padx=20, pady=10)

        # ------------------------ 设备状态显示区 ------------------------
        self.device_status_frame = ttk.LabelFrame(self.root, text=" 设备连接状态 ", padding=20)
        self.device_status_frame.grid(row=1, column=0, sticky='ew', padx=20, pady=10)
        self.device_status_frame.grid_propagate(False)  # 禁止框架根据内容自动调整大小
        self.device_status_frame.config(width=1160)       # 固定框架宽度（避免闪烁）

        # ------------------------ 测试用例列表区 ------------------------
        self.case_frame = ttk.LabelFrame(self.root, text=" 测试用例列表 ", padding=20)
        self.case_frame.grid(row=2, column=0, sticky='nsew', padx=20, pady=10)  # 占据剩余空间

        # ------------------------ 进度条显示区 ------------------------
        self.progress_frame = ttk.Frame(self.root, padding=15)
        self.progress_frame.grid(row=3, column=0, sticky='ew', padx=20, pady=10)

        # 调用各区域组件创建方法
        self.create_button_section()
        self.create_device_status()
        self.create_case_list()
        self.create_progress_bar()

    def create_button_section(self):
        """创建按钮操作区"""
        self.first_row = ttk.Frame(self.btn_frame)
        self.first_row.pack(fill='x', pady=5)  # 水平填充，上方留白5像素

        self.detect_btn = ttk.Button(self.first_row, text="检测设备",
                                    bootstyle='primary', command=self.start_device_detection,
                                    padding=8, width=12)
        self.detect_btn.pack(side='left', padx=20)  # 左对齐，左侧间距20像素

        self.task_btn = ttk.Button(self.first_row, text="检测任务",
                                  bootstyle='info', command=self.load_test_cases,
                                  padding=8, width=12)
        self.task_btn.pack(side='left', padx=20)

        right_container = ttk.Frame(self.first_row)
        right_container.pack(side='right', anchor='e')
        self.model_combobox = ttk.Combobox(right_container,
                                          values=['EEA5', 'EEA4', '其他'],
                                          width=15, bootstyle='primary')
        self.model_combobox.current(0)
        self.model_combobox.pack(side='left', padx=5)

        self.device_id_label = ttk.Label(right_container,
                                        text=f"设备ID：{self.device_id}",
                                        font=('Consolas', 11, 'bold'),
                                        foreground='#007bff')
        self.device_id_label.pack(side='left', padx=10)

        self.second_row = ttk.Frame(self.btn_frame)
        self.second_row.pack(fill='x', pady=5)

        self.start_btn = ttk.Button(self.second_row, text="启动测试",
                                   bootstyle='success', command=self.start_test,
                                   padding=8, width=12)
        self.start_btn.pack(side='left', padx=20)

        self.pause_btn = ttk.Button(self.second_row, text="暂停测试",
                                   bootstyle='warning', command=self.toggle_pause,
                                   padding=8, width=12, state='disabled')
        self.pause_btn.pack(side='left', padx=20)

    def create_device_status(self):
        """创建设备状态显示区"""
        status_container = ttk.Frame(self.device_status_frame, padding=10)
        status_container.pack(fill='both', expand=True)

        for device_name in ['ADB', 'ZLG', '串口', 'SSH']:
            device_frame = ttk.Frame(status_container, padding=5)
            device_frame.pack(side='left', padx=20)  #
            device_frame.grid_propagate(False)

            # 设备名称标签
            ttk.Label(device_frame, text=f"{device_name}：", font=('微软雅黑', 12),
                     foreground='#2c3e50').pack(side='left')

            status_label = ttk.Label(device_frame, text="未检测",
                                    font=('微软雅黑', 12), foreground='#6c757d',
                                    width=8)
            status_label.pack(side='left', padx=5)
            self.devices[device_name]['widget'] = status_label

    def create_case_list(self):
        """创建测试用例列表"""
        self.case_tree = tk_ttk.Treeview(
            self.case_frame,
            columns=('用例ID', '用例名称', '结果'),
            show='headings',
            height=15,
            selectmode='browse'
        )

        self.case_tree.heading('用例ID', text='用例ID')
        self.case_tree.heading('用例名称', text='用例名称')
        self.case_tree.heading('结果', text='结果')

        self.case_tree.column('用例ID', width=120)
        self.case_tree.column('用例名称', width=550)
        self.case_tree.column('结果', width=150)

        self.style.configure('Treeview',
                            background='white',
                            fieldbackground='#e3f2fd',
                            rowheight=30,                #
                            font=('微软雅黑', 10))
        self.style.map('Treeview',
                      background=[('selected', '#007bff')],
                      foreground=[('selected', 'white')])

        scroll_y = ttk.Scrollbar(self.case_frame, orient=VERTICAL, command=self.case_tree.yview)
        self.case_tree.configure(yscrollcommand=scroll_y.set)
        self.case_tree.pack(side='left', fill='both', expand=True)
        scroll_y.pack(side='left', fill='y')

    def create_progress_bar(self):
        """创建自适应进度条"""
        progress_container = ttk.Frame(self.progress_frame, padding=10)
        progress_container.pack(fill='x', expand=True)

        # 进度说明标签
        ttk.Label(progress_container, text="测试进度：",
                 font=('微软雅黑', 12), foreground='#2c3e50').pack(anchor='w', pady=5)

        # 自适应进度条（mode='determinate'表示确定进度）
        self.progress_bar = ttk.Progressbar(progress_container, mode='determinate')
        self.progress_bar.pack(fill='x', pady=8)  # 水平填充，上下留白8像素

        # 进度文本标签（显示已完成/总数）
        self.progress_label = ttk.Label(progress_container, text="0/0",
                                      font=('微软雅黑', 12), foreground='#6c757d')
        self.progress_label.pack(anchor='w', pady=3)

    def start_device_detection(self):
        """启动设备检测"""
        try:
            logger.info('开始设备检测流程')
            # 禁用按钮防止重复点击
            self.detect_btn.configure(state='disabled')
            
            # 添加日志
            logger.info(f'当前设备列表: {list(self.devices.keys())}')
            
            # 为每个设备启动独立检测线程
            for device_name in self.devices.keys():
                logger.info(f'启动 {device_name} 检测线程')
                thread = threading.Thread(
                    target=self.detect_device,
                    args=(device_name,),
                    daemon=True,
                    name=f'Detect_{device_name}'
                )
                thread.start()
                
        except Exception as e:
            logger.error(f'启动设备检测时出错: {str(e)}')
            # 发生错误时恢复按钮状态
            self.detect_btn.configure(state='normal')

    def detect_device(self, device_name):
        """通用设备检测"""
        try:
            device = self.devices[device_name]
            # if not device or 'widget' not in device:
            #     logger.error(f'设备 {device_name} 配置错误: {device}')
            #     return
            
            loading_icons = ['◌', '◍', '●', '◍']
            stop_loading = threading.Event()

            def update_loading_animation():
                """加载动画"""
                frame_index = 0
                logger.info(f'{device_name} 加载动画线程启动')
                try:
                    while not stop_loading.is_set():
                        current_icon = loading_icons[frame_index % 4]
                        self.root.after(0, lambda icon=current_icon:
                            device['widget'].configure(text=f"检测中 {icon}", foreground='#6c757d'))
                        frame_index += 1
                        time.sleep(0.1)
                    logger.info(f'{device_name} 加载动画线程结束')
                except Exception as e:
                    logger.error(f'{device_name} 加载动画出错: {str(e)}')

            def check_device_status():
                """检查设备状态"""
                logger.info(f'{device_name} 开始检查设备状态')
                try:
                    # 根据设备类型调用不同的检测方法
                    is_connected = self._check_specific_device(device_name)
                    
                    status = '√ 已连接' if is_connected else '✗ 未连接'
                    color = '#28a745' if is_connected else '#dc3545'
                    
                    stop_loading.set()
                    
                    self.root.after(0, lambda: device['widget'].configure(
                        text=status, 
                        foreground=color
                    ))
                    
                    # 检查所有设备是否检测完成
                    all_done = all(dev['widget'].cget('text') in ['√ 已连接', '✗ 未连接'] 
                                  for dev in self.devices.values())
                    if all_done:
                        self.root.after(0, lambda: self.detect_btn.configure(state='normal'))
                        
                except Exception as e:
                    logger.error(f'{device_name} 设备检测出错: {str(e)}')
                    stop_loading.set()
                    self.root.after(0, lambda: device['widget'].configure(
                        text='✗ 检测失败',
                        foreground='#dc3545'
                    ))

            # 启动加载动画线程
            loading_thread = threading.Thread(
                target=update_loading_animation,
                daemon=True,
                name=f'Loading_{device_name}'
            )
            loading_thread.start()
            time.sleep(2)

            # 启动设备检测线程
            check_thread = threading.Thread(
                target=check_device_status,
                daemon=True,
                name=f'Check_{device_name}'
            )
            check_thread.start()

        except Exception as e:
            logger.error(f'设备检测过程出错: {str(e)}')
            self.root.after(0, lambda: self.detect_btn.configure(state='normal'))

    def _check_specific_device(self, device_name):
        """根据设备类型执行具体检测逻辑"""
        try:
            if device_name == 'ADB':
                devices = AdbExec().devices
                return len(devices) > 0
                
            elif device_name == 'ZLG':
                return False
                
            elif device_name == '串口':
                return False
                
            elif device_name == 'SSH':
                vcc = vcctool().connect_vcc
                if vcc:
                    return True
                
            else:
                logger.error(f'未知设备类型: {device_name}')
                return False
                
        except Exception as e:
            logger.error(f'{device_name} 具体检测过程出错: {str(e)}')
            return False

    def load_test_cases(self):
        """从云端加载测试用例数据"""

        logger.info('开始从云端获取任务数据')
        # 禁用按钮防止重复点击
        self.task_btn.configure(state='disabled')

        # 获取当前设备ID和车型
        device_id = self.device_id
        car_model = self.model_combobox.get()

        # 构建API请求URL
        # 构建查询参数
        params = {
            'deviceId': device_id,
            'carModel': car_model
        }
        # 将字典转化为url查询字符串格式
        # query_string = urllib.parse.urlencode(params)
        # url = f'http://localhost:5000/api/check_task?{query_string}'
        url = f'http://localhost:5000/api/check_task'
        logger.info(f'请求URL: {url}')

        # # 发送请求
        # with urllib.request.urlopen(url) as response:
        #     data = json.loads(response.read().decode())
        #     print(data)
        # try:
        response = requests.get(
            url=url,
            params=params,
            timeout=10
        )
        data = response.json()

        # {
        #     'status': 'success',
        #     'data': {
        #         'testCases': [
        #             {
        #                 'id': 'test_A001',
        #                 'description': '预期结果我会制造失败'
        #             },
        #             {
        #                 'id': 'test_A002',
        #                 'description': '预期结果我会制造成功'
        #             }
        #         ],
        #         'timestamp': '2025-06-05 16:12:08'}
        #
        # }

        try:
            if data['status'] == 'success':
                # 获取测试用例数据
                test_cases = data['data']['testCases']
                timestamp = data['data']['timestamp']

                self.all_cases.clear()


                # 清空旧数据
                for item in self.case_tree.get_children():
                    self.case_tree.delete(item)
                #  'testCases': [{'id': 'test_A001', 'description': '预期结果我会制造失败'}, {'id': 'test_A002', 'description': '预期结果我会制造成功'}]}
                # 插入新数据
                for case in test_cases:
                    case_id = case['id']
                    case_description = case['description']

                    # 这里可以根据实际需求格式化用例数据
                    self.case_tree.insert('', 'end', values=(
                        case_id,
                        case_description,
                        '未执行'
                    ))
                    self.all_cases.append(case_id)


                logger.info(f'成功加载 {len(test_cases)} 个测试用例')
                # 显示成功消息
                self.show_message('成功', f'已从云端获取 {len(test_cases)} 个测试用例\n下发时间: {timestamp}')
            else:
                logger.warning(f'获取任务失败: {data.get("message", "未知错误")}')
                self.show_message('警告', data.get('message', '获取任务失败'))

        except Exception as e:
            logger.error(f'加载测试用例时出错: {str(e)}')
            self.show_message('错误', f'加载测试用例失败: {str(e)}')
        finally:
            # 恢复按钮状态
            self.task_btn.configure(state='normal')



    def start_test(self):
        """启动测试流程"""
        logger.info('点击启动测试按钮')
        if not self.all_cases:  # 无数据时不启动
            logger.debug('请先检测任务')
            return

        self.test_running = True
        self.pause_event.set()  # 确保初始为"继续"状态

        # 禁用相关按钮和下拉框（防止测试中误操作）
        self.detect_btn.configure(state='disabled')
        self.task_btn.configure(state='disabled')
        self.start_btn.configure(state='disabled')
        self.pause_btn.configure(state='normal')  # 启用暂停按钮
        self.model_combobox.configure(state='disabled')  # 锁定车型选择

        # 启动测试线程
        threading.Thread(target=self.run_test, daemon=True).start()

    def run_test(self):
        """实际测试执行逻辑"""
        total = len(self.all_cases)
        # 初始化进度条
        self._update_progress(0,total)


        threading.Thread(target=self._run_test_sequence,args=(total,),daemon=True).start()



    def _run_test_sequence(self, total):
        '''Qauto里面的 用例状态更新'''
        for i in range(total):
            if not self.test_running:  # 检测到终止标记时退出
                break

            self.pause_event.wait()  # 阻塞直到事件被设置（继续测试）

            # 获取当前用例id
            case_id = self.all_cases[i]  # 直接获取ID字符串

            # 更新当前id的ui状态为执行中
            self.root.after(0, lambda idx=i: self._update_case_status(idx, '执行中...'))
            self.root.after(0, lambda: self.case_tree.see(self.case_tree.get_children()[i]))  # 滚动到当前用例

            try:
                test_case_result = self._run_test_case(case_id)

                result = '√ 通过' if test_case_result else '✗ 失败'
                self.root.after(0, lambda idx=i, res=result: self._update_case_status(idx, res))

                # # 结果传到后端api
                # self._back_test_reslut(case_id, test_case_result)

            except Exception as e:
                logger.error(f'测试用例{case_id}出错:{str(e)}')
                return False

    def _run_test_case(self, case_id):
        '''单线程按照顺序执行测试用例'''
        try:

            test_case_path = f" testcases/EEA4/TestCases/{case_id}.py"

            # 配置pytest参数
            pytest_args = [
                test_case_path,  # 指定测试文件路径
                "-vvv",  # 详细输出模式
            ]

            # 执行pytest并获取返回码
            result_code = pytest.main(pytest_args)

            # 根据pytest返回码判断测试结果
            # 0: 所有测试通过 | 1: 有测试失败 | 2: 测试被用户中断 | 3: 执行测试时发生内部错误 | 4: pytest命令行使用错误
            return result_code == 0  # 如果返回码为0，表示测试通过，返回True；否则返回False

        except Exception as e:
            logger.error(f'执行测试用例{case_id}时出错: {str(e)}')
            return False  # 发生异常时返回False


    def _update_case_status(self, index, status):
        '''更新用例状态，只更新状态列'''
        all_items = self.case_tree.get_children()
        current_item = all_items[index]
        # 获取当前行的所有值
        current_values = self.case_tree.item(current_item)['values']
        # 只更新状态列（第三列），保持ID和描述不变
        self.case_tree.item(current_item, values=(current_values[0], current_values[1], status))
        # 确保当前项可见
        self.case_tree.see(current_item)

    def _update_progress(self,current,total):
        self.root.after(0,lambda :[
            self.progress_bar.configure(value=current/total*100),
            self.progress_label.configure(text=f"{current}/{total}")

        ])

    def _back_test_reslut(self,case_id,result):
        try:

            url = f'http://localhost:5000/api/back_result'
            json = {

                'case_id':case_id,
                'status': 'pass' if result else  'fail',
                'timestamp':datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            response = requests.post(
                url=url,
                json=json

            )
            response.raise_for_status()
        except Exception as e:
            logger.error(f"将测试结果回调后端api失败:{str(e)}")


    def _reset_buttons(self):
        self.root.after(0, lambda:
            [
            self.detect_btn.configure(state='normal'),
            self.task_btn.configure(state='normal'),
            self.start_btn.configure(state='normal'),
            self.pause_btn.configure(state='disabled', text='暂停测试'),
            self.model_combobox.configure(state='normal')  # 解锁车型选择
        ])



    def toggle_pause(self):
        """切换暂停/继续状态"""
        if self.pause_event.is_set():  # 当前为继续状态
            self.pause_event.clear()   # 清除事件，进入暂停状态
            self.pause_btn.configure(text='继续测试')
        else:                        # 当前为暂停状态
            self.pause_event.set()    # 设置事件，恢复继续状态
            self.pause_btn.configure(text='暂停测试')

    def show_message(self, title, message):
        """显示消息对话框"""
        from tkinter import messagebox
        messagebox.showinfo(title, message)


if __name__ == "__main__":
    root = ttk.Window(themename='minty')
    app = Qauto(root)
    root.mainloop()