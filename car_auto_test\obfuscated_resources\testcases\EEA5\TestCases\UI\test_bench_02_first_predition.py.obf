'''
第一次前置条件相关
1、第二次前置不过
2、退ota失败
3、下电失败
'''
import time

import pytest
import functools

from testcases.EEA5.PageObjects.TestObject import Step_object
from utils.ImageRecognition.Image_comparison import *
from utils.SerialTool.vcc_tool import vcc



class Test_Bench_First_Predition:
    # 使用mode fixture获取当前的模式值
    @pytest.fixture(autouse=True)
    def setup_ui_mode(self, mode, mode_dict):
        """设置当前的UI模式和mode_dict"""
        self.mode_value = mode
        self.mode_dict = mode_dict
        logger.info(f"当前测试环境: 模式值={self.mode_value}")

    # @pytest.mark.skip
    def test_bench_first_predition_001(self, driver):
        '''
        蓄电池/动力电池不足，不用监听弹框
        Args:
            driver:
            launch_app:

        Returns:

        '''
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        # 拿到升级任务
        # objects.get_task()

        time.sleep(1)
        vcc.set_file('master_vehicle_batt', 10)
        objects.battery_fail()
        objects.screen_image('蓄电池电量不足', self.mode_value)
        objects.click_element('battery_power_btn')
        time.sleep(1)
        vcc.set_file('hv_battle_soc', 10)
        objects.battery_fail()
        objects.screen_image('动力电池电量不足', self.mode_value)
        objects.click_element('battery_power_btn')

    # @pytest.mark.skip
    def test_bench_first_predition_002(self, driver):
        """
        前置相关,需要处理mda断链 导致的apk电量不足弹框
        """

        import threading
        import time

        # 创建一个共享变量用于控制线程
        monitor_active = True
        last_click_time = 0  # 上次点击时间
        click_cooldown = 2  # 两次点击间的冷却时间(秒)

        # 定义监控函数
        def monitor_dialog():
            nonlocal last_click_time
            while monitor_active:  # 使用控制变量
                try:
                    # 检测弹框
                    if driver(resourceId="com.carota.chery.v5:id/battery_tv_title").exists:
                        current_time = time.time()
                        # 确保两次点击间有足够的冷却时间
                        if current_time - last_click_time > click_cooldown:
                            logger.info("检测到电量不足弹框，点击确认按钮")
                            driver(resourceId="com.carota.chery.v5:id/incomplete_btn_confirm").click()
                            last_click_time = current_time
                            # 点击后等待一段时间，让UI有时间响应
                            time.sleep(3)
                            vcc.reboot_master()
                except Exception as e:
                    # 只在需要时记录错误
                    if "UiObjectNotFoundException" not in str(e):
                        logger.error(f"监控线程错误: {e}")
                # 降低检查频率，减少资源占用
                time.sleep(0.5)

        # 启动监控线程
        monitor_thread = threading.Thread(target=monitor_dialog, daemon=True)
        monitor_thread.start()
        logger.info("已设置电量不足弹框的后台监听(线程)")

        try:
            objects = Step_object(driver, self.mode_value, self.mode_dict)

            # 手刹
            logger.info('-----------------------开始制造手刹不满足---------------------------------')
            vcc.set_file('epb_status', 0)
            objects.preconditions_fail()
            objects.screen_image('手刹不满足', self.mode_value)
            objects.click_element('precondition_fail_cancelbtn')

            # 挡位
            logger.info('-----------------------开始制造挡位不满足---------------------------------')
            vcc.set_file('gear', 0)
            objects.preconditions_fail()
            objects.screen_image('挡位不满足', self.mode_value)
            objects.click_element('precondition_fail_cancelbtn')

            # 车速
            logger.info('-----------------------开始制造车速不满足---------------------------------')
            vcc.set_file('vehicle_speed', 99)
            objects.preconditions_fail()
            objects.screen_image('车速不满足', self.mode_value)
            objects.click_element('precondition_fail_cancelbtn')

            # 充电
            logger.info('-----------------------开始制造充电不满足---------------------------------')
            vcc.set_file('vehicle_charge_leg_status', 6)
            objects.preconditions_fail()
            objects.screen_image('未在充电不满足', self.mode_value)
            objects.click_element('precondition_fail_cancelbtn')

            # 放电
            logger.info('-----------------------开始制造放电不满足---------------------------------')
            vcc.set_file('vehicle_charge_leg_status', 8)
            objects.preconditions_fail()
            objects.screen_image('未在放电不满足', self.mode_value)
            objects.click_element('precondition_fail_cancelbtn')

            # 童宠
            logger.info('-----------------------开始制造童宠不满足---------------------------------')
            vcc.set_file('pet_mode', 1)
            objects.preconditions_fail()
            objects.screen_image('童宠不满足', self.mode_value)
            objects.click_element('precondition_fail_cancelbtn')

            # 上电失败
            logger.info('-----------------------开始制造上电不满足---------------------------------')
            vcc.set_file('remote_poweron', 1)
            objects.poweron_otamode_fail()
            objects.screen_image('上电失败', self.mode_value)
            objects.click_element('remote_power_on_fail_btn')

            # ota失败
            logger.info('-----------------------开始制造ota不满足---------------------------------')
            vcc.set_file('otamodein', 1)
            objects.poweron_otamode_fail()
            objects.screen_image('进入ota模式失败', self.mode_value)
            objects.click_element('remote_power_on_fail_btn')
            vcc.set_file('otamodein', 0)

            # OBD失败
            logger.info('-----------------------开始制造OBD不满足---------------------------------')
            objects.odb_fail()
            objects.screen_image('OBD不满足', self.mode_value)
            objects.click_element('precondition_fail_cancelbtn')


        finally:
            # 确保测试结束时停止监控线程
            monitor_active = False
            logger.info("测试完成，停止弹框监控线程")

