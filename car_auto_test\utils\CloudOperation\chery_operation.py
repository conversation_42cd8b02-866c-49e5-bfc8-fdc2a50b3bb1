# coding: utf-8
# Project：car_auto_test
# File：chery_operation.py
# Author：杨郑健
# Date ：2025/4/15 10:08
import os
import re
import json
import base64
import time
import hashlib
import requests
from config.Config import *
from urllib.parse import urljoin
from utils.OCR.OCR import ocr_image
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import AllureSteps
from tenacity import retry, stop_after_attempt, wait_fixed
from requests_toolbelt.multipart.encoder import MultipartEncoder
from config.api_config import APIConfig
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception

class Cloud:
    def __init__(self, enterprise=PLATFORM):
        if SWITCH:
            self.enterprise_type = APIConfig.test_data_store.get("selected_vehicle", "")
            self.platfrom = {"EU": "奇瑞欧盟", "SA": "奇瑞沙特", "CA": "奇瑞中亚", "RU": "奇瑞俄罗斯"}
            self.enterprise = self.platfrom.get(self.enterprise_type)
            self.vin = self.vin = APIConfig.test_data_store.get("vin", "")
        else:
            self.enterprise = enterprise
            self.vin = VIN
        self.chery_url = CHERY_URL
        self.path = os.getcwd() + r"\utils\CloudOperation\Verification_code.svg"
        for i in range(1, 4):
            self.session = requests.Session()
            self.get_csrf()
            logger.info(f"第{i}次初始化云端")
            result = self.login_backend()
            if result:
                logger.info("云端登陆成功，切换企业成功！")
                break

        self.model, self.vmid = self.get_car_model()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is requests.exceptions.ConnectionError:
            print(f"连接异常已处理: {exc_val}")
            logger.error("连接异常")
            print(requests.get("http://www.baidu.com", timeout=3).status_code)

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_fixed(10),
        retry=retry_if_exception(Exception)  # Retries on ANY exception
    )
    def get_csrf(self):
        """
        获取当前csrf，绕过csrf验证
        Returns:

        """
        csrf = '<meta content=".* name="csrf-token">'
        login_page_url = urljoin(self.chery_url, "/fota/vehicle_models")
        response = self.session.get(login_page_url)
        self.csrf_token = re.findall(csrf, response.text)[0].split('<meta content="')[1].split('" name=')[0]
        self.session.headers["X-CSRF-Token"] = self.csrf_token

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_fixed(10),
        retry=retry_if_exception(Exception)  # Retries on ANY exception
    )
    def save_code(self,):
        """ 保存验证码
        """
        with open(self.path, "w", encoding="utf-8") as f:
            data = self.session.get(f"{self.chery_url}/home/<USER>/login/imageCode").text
            f.write(data)
            f.close()

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_fixed(10),
        retry=retry_if_exception(Exception)  # Retries on ANY exception
    )
    def xor_encrypt(self, password: str, username: str) -> str:
        """
        模拟 XOR 加密
        参数:
            password: 要加密的密码
            username: 用作加密密钥的用户名

        返回:
            加密后的字符串
        """
        encrypted_chars = []
        for i in range(len(password)):
            # 获取密码和用户名对应位置的字符的ASCII码
            p_char = ord(password[i])
            u_char = ord(username[i % len(username)])
            # 执行XOR运算并转换为字符
            encrypted_char = chr(p_char ^ u_char)
            encrypted_chars.append(encrypted_char)

        return ''.join(encrypted_chars)

    # @AllureSteps.log_step("云端登录")
    
    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def login_backend(self,):
        """ 登录后台
        Returns: 返回cookie
        {
            "forward": "/"
        }

        """
        company = f'<li class="company" _id=".*"><a>{self.enterprise}</a></li>'
        for k in range(1, 3):
            logger.info(f"第{k}次登录.")
            # 拿三次验证码，如果没拿到就返回失败，不进行其他操作
            for i in range(3):
                self.save_code()
                code = ocr_image(self.path)
                if code:
                    # code_path = 'history\%s.svg' %code
                    # os.system(f"copy {self.path} {self.path.replace('Verification_code.svg', code_path)}")
                    break
                if i == 2:
                    logger.info("验证码识别失败，不进行登录")
                    return False
            # 登陆前处理
            xor_password = self.xor_encrypt(username=CHERY_USER, password=CHERY_PASSWORD)
            curr_user_data = {"username": CHERY_USER, "password": xor_password, "verifyCode": code, "remember": False}
            payload = {"base64": base64.b64encode(json.dumps(curr_user_data, ensure_ascii=False).encode("utf-8")).decode("utf-8")}
            try:
                rback = self.session.post(f"{self.chery_url}/home/<USER>/login", json=payload).text
                if rback.rfind("forward") != -1:
                    logger.info(f"云端登录成功! 开始切换到企业{self.enterprise}")
                    # 登陆成功后开始切换企业
                    response = self.session.get(f'{self.chery_url}/fota/vehicle_models')
                    company_id = re.findall(company, response.text)[0].split('_id="')[1].split('"><a>')[0]
                    self.session.get(f"{self.chery_url}/home/<USER>/switch_company/{company_id}")
                    self.get_csrf()
                    return True
                if rback.rfind("-6") != -1:
                    logger.info("返回状态码判断：验证码错误!")
                if rback.rfind("last") != -1:
                    logger.debug("返回状态码判断：账号或密码错误!")
            except Exception as e:
                logger.info(f"切换企业失败或者登录失败：{e}")
                if k == 2:
                    return False
            time.sleep(1)
        logger.info("云端登录失败")
        return False

    @AllureSteps.log_step(f"获取当前测试车的埋点信息")
    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def get_burying_point(self,):
        """
        获取对应vim的埋点日志
        Returns: [{'_id': '6821b5ab7db88a41a5dce81e-1747039704442-1747039703044-10100', 'action': 10100, 'vin': 'LVVDD21B0PC001990', 'usid': '6821b5ab7db88a41a5dce81e', 'vmid': 2234, 'sch_id': 54969, 'cmp_id': 38966, '_at': '2025-05-12T08:48:24.352Z', 's_at': 1747039704442, 'action_at': 1747039703044, 'source': None, 'msg': '(code 10) 主页面显示', 'r_id': '6821b5dbcb83fc1962966840', '_id_at': '2025-05-12T08:48:27.000Z', 'did_at': '2025-05-12T08:47:39.000Z', 'delta_at': 48000, 'data_version': 1747039711006, 'last_update_at': '2025-05-12T08:48:31.006Z', 'sid_name': 'T1EJ-台架-ddis升级=0416', 'cmp_name': 'T1EJ-台架-ddis升级=0416', '_fid': '6821b5ab7db88a41a5dce81e-1747039704442-1747039703044-10100'}]

                默认拿最近一次升级的日志，如果没有的关键字就拿最近一百条
        """
        logger.info(f"获取车辆{self.vin}埋点")
        time.sleep(10)  # 接口请求太快导致埋点上传跟不上，手动延迟10s
        jsondata = self.session.get(
            f"{self.chery_url}/statistics/statistics_action/api/list?search%5Bvalue%5D=%7B%22vin%22%3A%22{self.vin}%22%7D&draw=3",
            ).json()
        try:
            for i in range(len(jsondata["data"])):
                if jsondata["data"][i]["msg"].rfind("主页面显示") != -1:
                    return jsondata["data"][:i+1]
        except:
            return jsondata[:100]
        return jsondata[:100]

    @AllureSteps.log_step("获取当前测试车的活动日志")
    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def get_carinfo(self,):
        """
        获取当前vin的活动日志
        Returns: [a,b,c]
                拿最近一次升级的活动日志
        """
        logger.info(f"获取车辆{self.vin}活动日志")
        jsondata = self.session.get(
            f"{self.chery_url}/fota/vehicle_logs/api/list?vin={self.vin}",
            ).json()

        for i in range(len(jsondata["data"])):
            if jsondata["data"][i]["message"] == "1.校验参数":
                return jsondata["data"][:i+1]
        else:
            logger.info("未找到检测任务标志位，返回最近30条活动日志")
            return jsondata["data"][:30]

    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def get_car_model(self):
        """
        获取当前vin的车型名称和vmid,如果此方法报错，注意下是不是企业名称错误
        Returns:
            return (奇瑞汽车,1234)
        """
        logger.info(f"获取{self.vin}的所属车型")
        jsondata = self.session.get(
            f"{self.chery_url}/fota/vehicle_logs/api/list?vin={self.vin}&length=100",
            ).json()["data"]
        for i in jsondata:
            if "vehicle_model" in i:
                jsondata = i["vehicle_model"]
        return "%s-%s" % (jsondata["brand"], jsondata["model"]), jsondata["_id"]

    # @AllureSteps.log_step("查询所有任务")
    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def get_tasks(self):
        """
        获取当前VIN的所有任务
        Returns:
                return {["任务名称":["任务id", "任务开启关闭状态"]]}
        """
        logger.info(f"获取{self.vin}的所有任务")
        url = f"{self.chery_url}/fota/schedules/api/list?start=0&length=100&search%5Bvalue%5D=%7B%22vmid%22%3A%22{self.vmid}%22%2C%22name%22%3A%22%22%7D"
        jsondata = self.session.get(url,).json()
        task_dict = {}
        for task in jsondata["data"]:
            task_dict[task["name"]] = [task["_id"], task["enabled"]]
        return task_dict

    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def get_open_task(self):
        """
        Returns: [任务名称:任务id]

        """
        logger.info(f"获取{self.vin}的已打开任务")
        open_dict = {}
        for name, value in self.get_tasks().items():
            if value[1] == True:
                open_dict[name] = value[0]
        return open_dict

    # @AllureSteps.log_step("同步当前配置任务到云端")
    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def sync_task(self, task_list):
        logger.info(f"同步{self.vin}的任务")
        # 对比打开的任务,先打开未打开的任务
        is_open = self.get_open_task()
        for name in task_list:
            if name not in is_open.values():

                self.open_task(id=name)
        # 再关闭没有关闭的任务
        for k in is_open.values():
            if k not in task_list:
                self.close_task(id=k)

        # 检测任务同步后任务开启数量是否一致，不一致说明同步失败
        if len(self.get_open_task().keys()) == len(task_list):
            return True
        return False

    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def open_task(self, name=None, id=None):
        logger.info(f"打开-{id}-任务")
        if not id:
            task_dict = self.get_tasks()
            id = int(task_dict[name][0])
        url = f"{self.chery_url}/fota/schedules/api/save"
        data = {"_id": id, "enabled": True}
        self.session.post(url, json=data).json()

    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def close_task(self, name=None, id=None):
        logger.info(f"关闭-{id}-任务")
        if not id:
            task_dict = self.get_tasks()
            id = int(task_dict[name][0])
        url = f"{self.chery_url}/fota/schedules/api/save"
        data = {"_id": id, "enabled": False}
        self.session.post(url, json=data).json()

    @AllureSteps.log_step("查询所有救援任务")
    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def get_rescue_task(self,):
        logger.info(f"查询-{self.vin}-所有救援任务")
        url = f"{self.chery_url}/fota/rescue/api/list?start=0&length=100&search%5Bvalue%5D=%7B%22vin%22%3A%22{self.vin}%22%7D"
        jsondata = self.session.get(url=url, ).json()["data"]
        task_dict = {}
        for i in jsondata:
            task_dict[i["_id"]] = i["enabled"]
        return task_dict

    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def get_rescue_open_task(self):
        """
        Returns: [已开启的救援任务id1,已开启的救援任务id2]

        """
        logger.info(f"查询-{self.vin}-已开启救援任务")
        open_task_dict = []
        for id, status in self.get_rescue_task().items():
            if status == True:
                open_task_dict.append(id)
        return open_task_dict

    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def open_rescue_task(self, id):
        logger.info(f"打开紧急救援-{id}-任务")
        url = f"{self.chery_url}/fota/schedules/api/save"
        data = {"_id": id, "enabled": True}
        self.session.post(url, json=data).json()

    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def close_rescue_task(self, id):
        logger.info(f"关闭紧急救援-{id}-任务")
        url = f"{self.chery_url}/fota/schedules/api/save"
        data = {"_id": id, "enabled": False}
        self.session.post(url, json=data).json()

    @AllureSteps.log_step("同步当前配置救援任务到云端")
    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def sync_rescue_task(self, task_list):
        logger.info(f"同步紧急救援{self.vin}的任务")
        # 对比打开的任务,先打开未打开的任务
        is_open = self.get_rescue_open_task()
        for name in task_list:
            if name not in is_open:
                self.open_rescue_task(name)
        # 再关闭没有关闭的任务
        for k in is_open:
            if k not in task_list:
                self.close_rescue_task(k)

        if len(self.get_rescue_open_task()) == len(task_list):
            return True
        return False

    @AllureSteps.log_step("获取指定任务的发行说明hash")
    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def get_release_note(self, id):
        """获取当前云端指定任务的发行说明
        Args:
            id: 任务id
        Returns:
        """
        logger.info(f"获取当前云端-{id}-任务的发行说明hash值")
        url = f"{self.chery_url}/fota/campaigns/api/get/{id}"
        return self.session.get(url,).json()["data"]["notes"][0]["release_notes"]

    @AllureSteps.log_step("获取最近一次的升级任务id,并获取发行说明")
    @retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(10),
    retry=retry_if_exception(Exception)  # Retries on ANY exception
)
    def get_curr_release_note(self):
        """获取云端当前任务的发行说明，只看最近一次已经检测到的任务的发行说明，不是看具体任务的发行说明"""
        info = self.get_carinfo()
        for i in info:
            if i["message"] == "6.查询升级任务":
                cmpid = i["data"]["result"]["cmpid"]
                return self.get_release_note(cmpid)
        logger.info("未找到最近任务的发行说明")

    def calculate_md5(self, file_path):
        md5_hash = hashlib.md5()
        with open(file_path, "rb") as f:
            # 分块读取文件，避免大文件占用过多内存
            for chunk in iter(lambda: f.read(4096), b""):
                md5_hash.update(chunk)
        return md5_hash.hexdigest()

    def get_strategy(self, strategy_id):
        url = f"{self.chery_url}/fota/campaigns/api/get/{strategy_id}"
        return self.session.get(url, ).json()

    def edit_strategy(self, strategy_id):
        url = f"{self.chery_url}/fota/campaigns/api/save"
        strategy = self.get_strategy(strategy_id)["data"]
        notes = self.upload_notes()
        strategy["isEditing"] = True
        strategy["notes"] = notes
        language = {}
        for i in notes:
            language[i["lang"]] = i["title"]
        strategy["rn"] = language
        status = self.session.post(url, json=strategy)
        if status.ok and status.text.rfind("error") == -1:
            return True
        else:
            return False

    def get_language(self):
        url = f"{self.chery_url}/fota/campaigns/api/list_language"
        language = self.session.get(url, ).json()
        strategy_lanuage = {}
        for i in language:
            strategy_lanuage[i["id"]] = "test_" + i["text"]
        return strategy_lanuage

    def upload_notes(self):
        notes = []
        upload_url = f"{self.chery_url}/files/chunks/upload"
        check_url = f"{self.chery_url}/files/check"
        check_data = {"md5": ""}
        for i in os.listdir("./notes"):
            # print(i)
            check_data["md5"] = self.calculate_md5(f"./notes/{i}")
            name = i.replace(".html", "")
            notes.append({"lang": name, "title": name, "brief": name, "file_name": i, "file_size": "0KB",
                          "release_notes": check_data["md5"]})
            check_res = self.session.post(check_url, json=check_data).json()["exist"]
            if not check_res:
                html_content = open(f"./notes/{i}", "r").read()
                size = len(html_content.encode('utf-8'))
                multipart_data = MultipartEncoder(
                    fields={
                        'file': ('blob', html_content, 'application/octet-stream'),
                        'name': i,
                        'total': '1',
                        'ext': 'html',
                        'index': '0',
                        'size': str(size),
                        'chunkMd5': check_data["md5"]
                    },
                    boundary='----WebKitFormBoundarytQRveqyCcDhiGoY0')
                self.session.headers['Content-Type'] = multipart_data.content_type
                res = self.session.post(upload_url, data=multipart_data).text
                get_data = self.session.get(
                    f"{self.chery_url}/fota/mergedChunkFiles/api/get/{check_data['md5']}").json()
                if not get_data["data"]:
                    self.session.headers['Content-Type'] = "application/x-www-form-urlencoded"
                    data = f"name={i}&ext=html&type=1&chunkCount=1&chunkSize=20971520&size={str(size)}&chunkHashes%5B%5D={check_data['md5']}&md5={check_data['md5']}"
                    self.session.post(url=f"{self.chery_url}/fota/mergedChunkFiles/api/save", data=data)
                    get_data = self.session.get(
                        f"{self.chery_url}/files/chunks/merge/{check_data['md5']}").json()
        return notes

# 
# if __name__ == "__main__":
#     # demo
#     # page = Cloud("奇瑞沙特")
# 
#     for i in range(100):
#         page = Cloud()
#         if i %2 ==0:
#             page.sync_task([41497, 41496])
#         else:
#             page.sync_task([41587, 41585])
#         time.sleep(2)
    # page.sync_task([54971,54970])
    # print(page.get_burying_point()[0]["msg"] == "注意事项页面显示")
    # page.get_curr_release_note()
    # print(page.get_release_note(38966))
    # print(page.get_carinfo())
    # info = page.get_carinfo()
    # for i in info:
    #     print(i["message"].rfind("开始下载升级包"))
    # burying_point = Cloud().get_burying_point()
    # print(burying_point)


