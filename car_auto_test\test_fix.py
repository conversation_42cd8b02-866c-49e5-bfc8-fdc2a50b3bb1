#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的代码
"""
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_allure_fix():
    """测试 Allure 文件修复"""
    try:
        from utils.ReportGenerator.Allure import AllureAttachments
        print("✓ AllureAttachments 导入成功")
        
        # 测试文件不存在的情况
        AllureAttachments.attach_zlglog_file("test_nonexistent")
        print("✓ attach_zlglog_file 处理文件不存在的情况成功")
        
    except Exception as e:
        print(f"✗ AllureAttachments 测试失败: {e}")
        return False
    
    return True

def test_condition_fix():
    """测试 Condition 文件修复"""
    try:
        from testcases.EEA4.PageObjects.Condition import condition
        print("✓ Condition 模块导入成功")
        
        # 测试装饰器
        @condition({})
        def dummy_test():
            pass
        
        print("✓ condition 装饰器创建成功")
        
    except Exception as e:
        print(f"✗ Condition 测试失败: {e}")
        return False
    
    return True

def test_main_fix():
    """测试 main 文件修复"""
    try:
        import main
        print("✓ main 模块导入成功")
        
    except Exception as e:
        print(f"✗ main 测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("开始测试修复后的代码...")
    
    tests = [
        ("Allure 修复", test_allure_fix),
        ("Condition 修复", test_condition_fix),
        ("Main 修复", test_main_fix),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- 测试 {test_name} ---")
        if test_func():
            print(f"✓ {test_name} 测试通过")
            passed += 1
        else:
            print(f"✗ {test_name} 测试失败")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("❌ 部分测试失败")
        sys.exit(1)
