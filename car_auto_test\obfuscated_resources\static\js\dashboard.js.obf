/* =============== 全局配置对象 =============== */
const CONFIG = {
    itemsPerPage: 10,     // 每页显示条目数
    refreshInterval: 3000 // 自动刷新间隔(ms)
};

/* =============== 状态管理系统 =============== */
const StateManager = {
    // 存储测试数据
    testData: [],          // 原始测试数据
    filteredData: [],      // 筛选后的数据
    currentPage: 1,        // 当前页码
    currentSort: {         // 当前排序状态
        column: 'id',
        direction: 'asc'
    },
    filters: {             // 当前筛选条件
        language: '',
        mode: ''
    },
    currentImageIndex: -1, // 当前选中图片索引
    refreshTimer: null     // 自动刷新定时器
};

/* =============== DOM元素缓存 =============== */
const DOM = {
    // 控制按钮
    startTestBtn: document.getElementById('startTestBtn'),
    stopTestBtn: document.getElementById('stopTestBtn'),
    statusDot: document.getElementById('statusDot'),
    statusText: document.getElementById('statusText'),
    
    // 表格相关
    testTableBody: document.getElementById('testTableBody'),
    pagination: document.getElementById('pagination'),
    pageInfo: document.getElementById('pageInfo'),
    totalInfo: document.getElementById('totalInfo'),
    prevPageBtn: document.getElementById('prevPage'),
    nextPageBtn: document.getElementById('nextPage'),
    
    // 图片预览
    imagePreview: document.getElementById('imagePreview'),
    lastUpdate: document.getElementById('lastUpdate')
};

/* =============== 表格管理模块 =============== */
const TableManager = {
    /**
     * 初始化表格事件监听器
     */
    initEventListeners() {
        // 绑定排序点击事件
        document.querySelectorAll('.sortable').forEach(th => {
            th.addEventListener('click', () => this.handleSort(th));
        });

        // 绑定筛选点击事件
        document.querySelectorAll('.filterable').forEach(th => {
            th.addEventListener('click', (e) => this.handleFilter(th, e));
        });

        // 绑定分页按钮
        DOM.prevPageBtn.addEventListener('click', () => this.changePage(-1));
        DOM.nextPageBtn.addEventListener('click', () => this.changePage(1));
    },

    /**
     * 处理表格排序
     * @param {HTMLElement} header - 被点击的表头元素
     */
    handleSort(header) {
        // 更新排序状态
        StateManager.currentSort = {
            column: header.dataset.sort,
            direction: 'asc' // 当前仅支持升序
        };

        // 更新排序指示器
        document.querySelectorAll('.test-table th').forEach(th => {
            th.classList.remove('sort-asc');
        });
        header.classList.add('sort-asc');

        // 重置到第一页并更新表格
        StateManager.currentPage = 1;
        this.updateTable();
    },

    /**
     * 处理表格筛选
     * @param {HTMLElement} header - 被点击的表头元素
     * @param {Event} event - 点击事件对象
     */
    handleFilter(header, event) {
        const filterType = header.dataset.filter;
        const rect = header.getBoundingClientRect();
        
        // 移除其他已显示的筛选下拉框
        document.querySelectorAll('.filter-dropdown').forEach(dropdown => {
            if (dropdown.dataset.filter !== filterType) {
                dropdown.classList.remove('show');
            }
        });

        // 创建或获取筛选下拉框
        let dropdown = document.querySelector(`.filter-dropdown[data-filter="${filterType}"]`);
        if (!dropdown) {
            dropdown = this.createFilterDropdown(filterType);
        } else {
            this.updateFilterDropdown(dropdown, filterType);
        }

        // 定位下拉框
        dropdown.style.top = `${rect.bottom + window.scrollY}px`;
        dropdown.style.left = `${rect.left + window.scrollX}px`;
        dropdown.classList.toggle('show');

        // 点击其他地方关闭下拉框
        const closeDropdown = (e) => {
            if (!dropdown.contains(e.target) && !header.contains(e.target)) {
                dropdown.classList.remove('show');
                document.removeEventListener('click', closeDropdown);
            }
        };
        document.addEventListener('click', closeDropdown);
    },

    /**
     * 创建筛选下拉框
     * @param {string} filterType - 筛选类型
     * @returns {HTMLElement} 创建的下拉框元素
     */
    createFilterDropdown(filterType) {
        const dropdown = document.createElement('div');
        dropdown.className = 'filter-dropdown';
        dropdown.dataset.filter = filterType;
        document.body.appendChild(dropdown);

        // 获取唯一值
        const uniqueValues = [...new Set(StateManager.testData.map(item => item[filterType]))];
        
        // 添加"全部"选项
        this.addFilterItem(dropdown, '全部', '', filterType);

        // this.addFilterItem(dropdown, '中文', 'zh', filterType);
        // this.addFilterItem(dropdown, '英文', 'en', filterType);
        // this.addFilterItem(dropdown, '德文', 'de', filterType);
        // this.addFilterItem(dropdown, '法文', 'fr', filterType);
        // this.addFilterItem(dropdown, '阿拉伯文', 'ar', filterType);

        // 添加其他选项
        uniqueValues.forEach(value => {
            this.addFilterItem(dropdown, value, value, filterType);
        });

        return dropdown;
    },



    /**
     * 更新筛选下拉框内容
     * @param {HTMLElement} dropdown - 下拉框元素
     * @param {string} filterType - 筛选类型
     */
    updateFilterDropdown(dropdown, filterType) {
        dropdown.innerHTML = '';
        const uniqueValues = [...new Set(StateManager.testData.map(item => item[filterType]))];
        
        this.addFilterItem(dropdown, '全部', '', filterType);
        uniqueValues.forEach(value => {
            this.addFilterItem(dropdown, value, value, filterType);
        });
    },

    /**
     * 添加筛选选项
     * @param {HTMLElement} dropdown - 下拉框元素
     * @param {string} text - 显示文本
     * @param {string} value - 选项值
     * @param {string} filterType - 筛选类型
     */
    addFilterItem(dropdown, text, value, filterType) {
        const item = document.createElement('div');
        item.className = 'filter-item' + (StateManager.filters[filterType] === value ? ' active' : '');
        item.textContent = text;
        item.onclick = (e) => {
            e.stopPropagation();
            StateManager.filters[filterType] = value;
            this.updateFilters(filterType);
            dropdown.classList.remove('show');
        };
        dropdown.appendChild(item);
    },

    /**
     * 更新筛选条件
     * @param {string} filterType - 筛选类型
     */
    updateFilters(filterType) {
        // 更新选中状态
        document.querySelectorAll(`.filter-dropdown[data-filter="${filterType}"] .filter-item`).forEach(item => {
            item.classList.toggle('active', item.textContent === (StateManager.filters[filterType] || '全部'));
        });

        // 重置到第一页并更新表格
        StateManager.currentPage = 1;
        this.updateTable();
    },

    /**
     * 更新表格显示
     */
    updateTable() {
        if (StateManager.testData.length === 0) {
            DOM.testTableBody.innerHTML = '<tr><td colspan="7" class="no-image">暂无测试数据</td></tr>';
            DOM.pagination.style.display = 'none';
            return;
        }

        // 应用筛选
        StateManager.filteredData = StateManager.testData.filter(item => {
            return (!StateManager.filters.language || item.language === StateManager.filters.language) &&
                   (!StateManager.filters.mode || item.mode === StateManager.filters.mode);
        });

        // 应用排序
        if (StateManager.currentSort.column === 'id') {
            StateManager.filteredData.sort((a, b) => {
                return parseInt(a.id) - parseInt(b.id);
            });
        }
        
        // 计算当前页的数据
        const start = (StateManager.currentPage - 1) * CONFIG.itemsPerPage;
        const end = start + CONFIG.itemsPerPage;
        const pageData = StateManager.filteredData.slice(start, end);

        // 更新表格内容
        DOM.testTableBody.innerHTML = pageData.map((item, index) => {
            const timestamp = new Date(item.timestamp);
            const simplifiedTime = `${timestamp.getHours().toString().padStart(2, '0')}:${timestamp.getMinutes().toString().padStart(2, '0')}:${timestamp.getSeconds().toString().padStart(2, '0')}`;
            const scenario = item.scenario.length > 50 ? 
                item.scenario.substring(0, 50) + '...' : 
                item.scenario;

            // 检查当前项是否为选中的项
            const isSelected = StateManager.currentImageIndex >= 0 && 
                             StateManager.testData[StateManager.currentImageIndex] && 
                             StateManager.testData[StateManager.currentImageIndex].id === item.id;

            return `
                <tr class="${isSelected ? 'active' : ''}" 
                    onclick="ImageManager.selectRow(${index}, '${item.id}')"
                    title="${item.scenario}">
                    <td>${item.id}</td>
                    <td><span class="badge badge-primary">EEA4</span></td>
                    <td>${item.language || '-'}</td>
                    <td>${item.mode || '-'}</td>
                    <td>${scenario}</td>
                    <td>${simplifiedTime}</td>
                    <td><span class="badge badge-success">成功</span></td>
                </tr>
            `;
        }).join('');

        // 更新分页信息
        this.updatePagination();
    },

    /**
     * 更新分页信息
     */
    updatePagination() {
        const totalPages = Math.ceil(StateManager.filteredData.length / StateManager.itemsPerPage);
        const pageInfo = document.getElementById('pageInfo');
        const totalInfo = document.getElementById('totalInfo');
        const prevButton = document.getElementById('prevPage');
        const nextButton = document.getElementById('nextPage');
        const exportButton = document.getElementById('exportReport');

        if (pageInfo) {
            pageInfo.textContent = `第 ${StateManager.currentPage} 页，共 ${totalPages} 页`;
        }
        if (totalInfo) {
            totalInfo.textContent = `共 ${StateManager.filteredData.length} 条记录`;
        }
        if (prevButton) {
            prevButton.disabled = StateManager.currentPage === 1;
        }
        if (nextButton) {
            nextButton.disabled = StateManager.currentPage === totalPages;
        }

        // 确保导出按钮始终存在
        const paginationContainer = document.querySelector('.pagination');
        if (paginationContainer) {
            // 导出离线报告按钮
            if (!exportButton) {
                const newExportButton = document.createElement('button');
                newExportButton.id = 'exportReport';
                newExportButton.className = 'export-button';
                newExportButton.textContent = '导出离线报告';
                newExportButton.onclick = () => this.exportOfflinePage();
                paginationContainer.appendChild(newExportButton);
            }
        }
    },

    /**
     * 导出离线报告
     */
    async exportOfflinePage() {
        try {
            const response = await fetch('/api/export_offline_page', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    screenshots: StateManager.testData
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `测试报告_${new Date().toISOString().split('T')[0]}.zip`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            console.error('导出离线报告失败:', error);
            alert('导出离线报告失败: ' + error.message);
        }
    },

    /**
     * 切换页面
     * @param {number} direction - 页面切换方向（-1: 上一页, 1: 下一页）
     */
    changePage(direction) {
        const totalPages = Math.ceil(StateManager.filteredData.length / CONFIG.itemsPerPage);
        const newPage = StateManager.currentPage + direction;

        if (newPage >= 1 && newPage <= totalPages) {
            StateManager.currentPage = newPage;
            this.updateTable();
        }
    },


};

/* =============== 图片管理模块 =============== */
const ImageManager = {
    /**
     * 初始化图片管理模块
     */
    init() {
        // 创建模态框
        this.createModal();
    },

    /**
     * 创建模态框
     */
    createModal() {
        const modal = document.createElement('div');
        modal.id = 'imageModal';
        modal.className = 'image-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <span class="close-modal">&times;</span>
                <img class="modal-image" src="" alt="放大图片">
            </div>
        `;
        document.body.appendChild(modal);

        // 绑定关闭事件
        modal.querySelector('.close-modal').onclick = () => {
            modal.style.display = 'none';
        };

        // 点击模态框背景关闭
        modal.onclick = (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        };

        // 添加ESC键关闭功能
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.style.display === 'block') {
                modal.style.display = 'none';
            }
        });
    },

    /**
     * 更新图片预览
     * @param {Object} data - 图片数据对象
     */
    updateImagePreview(data) {
        if (!data) return;

        StateManager.currentImageIndex = StateManager.testData.indexOf(data);

        // 构建图片URL
        let imagePath = data.image_path;
        
        // 处理图片路径
        if (imagePath) {
            if (imagePath.includes('Image_ui')) {
                // 从 Image_ui 路径中提取相对路径
                const parts = imagePath.split('Image_ui');
                if (parts.length > 1) {
                    // 确保路径使用正斜杠，并移除开头的反斜杠
                    imagePath = '/static/images/' + parts[1].replace(/\\/g, '/').replace(/^\//, '');
                }
            }
        }

        console.log('Image path:', imagePath);

        // 更新预览区域HTML
        DOM.imagePreview.innerHTML = `
            <div class="image-info">
                <h4>${data.scenario}</h4>
                <p><strong>车型:</strong> EEA4</p>
                <p><strong>语言:</strong> ${data.language || '-'}</p>
                <p><strong>模式:</strong> ${data.mode || '-'}</p>
                <p><strong>时间:</strong> ${data.timestamp}</p>
            </div>
            <div class="image-container">
                <img src="${imagePath}" alt="${data.scenario}" class="preview-image"
                     onload="this.style.display='block'; this.nextElementSibling.style.display='none';"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block'; console.log('Image load error:', this.src);">
                <div class="image-error" style="display: none;">
                    <p><strong>图片加载失败</strong></p>
                    <p>路径: ${imagePath}</p>
                    <p>请检查图片文件是否存在</p>
                </div>
            </div>
        `;

        // 绑定点击事件
        const previewImage = DOM.imagePreview.querySelector('.preview-image');
        if (previewImage) {
            previewImage.onclick = () => this.showEnlargedImage(imagePath);
        }
    },

    /**
     * 显示放大的图片
     * @param {string} imagePath - 图片路径
     */
    showEnlargedImage(imagePath) {
        console.log('显示放大图片:', imagePath); // 添加调试日志
        const modal = document.getElementById('imageModal');
        if (!modal) {
            console.error('未找到模态框元素');
            return;
        }
        const modalImage = modal.querySelector('.modal-image');
        if (!modalImage) {
            console.error('未找到模态框图片元素');
            return;
        }
        modalImage.src = imagePath;
        modal.style.display = 'block';
    },

    /**
     * 选择表格行并更新图片预览
     * @param {number} index - 选中的行索引（在筛选后数据中的索引）
     * @param {string} id - 数据项的ID
     */
    selectRow(index, id) {
        console.log('selectRow called with index:', index, 'id:', id);
        
        // 计算在筛选后数据中的实际索引
        const start = (StateManager.currentPage - 1) * CONFIG.itemsPerPage;
        const actualIndex = start + index;
        
        // 从筛选后的数据中获取对应的数据项
        if (actualIndex >= 0 && actualIndex < StateManager.filteredData.length) {
            const dataItem = StateManager.filteredData[actualIndex];
            console.log('Found data item:', dataItem);
            
            // 在原始数据中找到对应的索引
            const originalIndex = StateManager.testData.findIndex(item => item.id === dataItem.id);
            if (originalIndex !== -1) {
                StateManager.currentImageIndex = originalIndex;
                console.log('Updated currentImageIndex to:', originalIndex);
            }
            
            // 更新表格显示
            TableManager.updateTable();
            
            // 更新图片预览
            this.updateImagePreview(dataItem);
        } else {
            console.error('Invalid index:', actualIndex, 'filteredData length:', StateManager.filteredData.length);
        }
    }
};



/* =============== 测试控制模块 =============== */
const TestController = {
    /**
     * 初始化测试控制
     */
    initControls() {
        DOM.startTestBtn.addEventListener('click', () => this.startTest());
        DOM.stopTestBtn.addEventListener('click', () => this.stopTest());
        // this.initLanguageDropdown();
    },


    /**
     * 开始测试
     */
    async startTest() {
        try {
            // 获取选择的车型
            const vehicleSelect = document.getElementById('vehicleSelect');
            const selectedVehicle = vehicleSelect ? vehicleSelect.value : 'E03';
            const vehicle_Types = document.getElementById('vehicleType');
            const vehicleTypes = vehicle_Types ? vehicle_Types.value : 'PHEV';
            const vinInput = document.getElementById('vinInput');
            const vin = vinInput ? vinInput.value : '';

            const passwordInput = document.getElementById('cardKeyInput');
            const password = passwordInput ? passwordInput.value : '';

            const task1Input = document.getElementById('task1Input');
            const task1 = task1Input ? task1Input.value : '';
            const task2Input = document.getElementById('task2Input');
            const task2 = task2Input ? task2Input.value : '';
            const task3Input = document.getElementById('task3Input');
            const task3 = task3Input ? task3Input.value : '';
            const task4Input = document.getElementById('task4Input');
            const task4 = task4Input ? task4Input.value : '';

            // 获取多选下拉框中所有已选中的值
            const getSelectedLanguages = () => {
            const selected = [];
            document.querySelectorAll('.multiselect-option input:checked').forEach(checkbox => {
                selected.push(checkbox.value);
            });
            return selected;
            };
            const selectedLanguages = getSelectedLanguages();
            console.log("语言下拉框",selectedLanguages)



            console.log('开始发送启动测试请求...', '选择车型:', selectedVehicle);
            const response = await fetch('/api/start_test', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    vehicle: selectedVehicle,
                    vin: vin,
                    vehicle_type: vehicleTypes,
                    task1: task1,
                    task2: task2,
                    task3: task3,
                    task4: task4,
                    password:password,
                    languages:selectedLanguages
                })
            });
            
            console.log('收到服务器响应:', response.status, response.statusText);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            console.log('解析响应数据:', data);
            
            if (data.status === 'success') {
                console.log('测试启动成功，开始自动刷新');
                TestController.startAutoRefresh();
                // 更新UI状态
                TestController.updateTestStatus('running', '测试进行中...');
            } else {
                console.warn('服务器返回非成功状态:', data.message);
                alert(data.message || '启动测试失败');
            }
        } catch (error) {
            console.error('启动测试过程中发生错误:', error);
            if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
                alert('网络连接失败，请检查网络连接');
            } else {
                alert(`启动测试失败: ${error.message}`);
            }
        }
    },

    /**
     * 停止测试
     */
    async stopTest() {
        try {
            const response = await fetch('/api/stop_test', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            const data = await response.json();
            console.log('测试已停止');
            TestController.stopAutoRefresh();
        } catch (error) {
            console.error('停止测试失败:', error);
        }
    },

    /**
     * 开始自动刷新
     */
    startAutoRefresh() {
        console.log('开始自动刷新...');
        if (StateManager.refreshTimer) {
            clearInterval(StateManager.refreshTimer);
        }
        // 使用箭头函数保持this指向
        StateManager.refreshTimer = setInterval(() => {
            console.log('执行自动刷新...');
            this.fetchTestStatus();
        }, CONFIG.refreshInterval);
    },

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        console.log('停止自动刷新...');
        if (StateManager.refreshTimer) {
            clearInterval(StateManager.refreshTimer);
            StateManager.refreshTimer = null;
        }
    },

    /**
     * 获取测试状态
     */
    async fetchTestStatus() {
        try {
            console.log('获取测试状态...');
            const response = await fetch('/api/test_status');
            const data = await response.json();
            console.log('测试状态数据:', data);

            // 更新状态
            let statusText = '系统就绪';
            if (data.test_status === 'running') {
                statusText = '测试进行中...';
            } else if (data.test_status === 'completed') {
                statusText = '测试已完成';
            } else if (data.test_status === 'failed') {
                statusText = '测试失败';
                // 显示详细错误信息
                if (data.error_message) {
                    console.error('测试失败详情:', data.error_message);
                    if (data.error_details) {
                        console.error('错误堆栈:', data.error_details);
                    }
                    // 在页面上显示错误信息
                    this.showErrorMessage(data.error_message, data.error_details);
                }
            }

            this.updateTestStatus(data.test_status, statusText);

            // 更新测试数据
            if (data.screenshots) {
                console.log('当前截图数量:', data.screenshots.length, '已存储数量:', StateManager.testData.length);
                // 使用JSON.stringify比较数据是否真正发生变化
                if (JSON.stringify(data.screenshots) !== JSON.stringify(StateManager.testData)) {
                    console.log('更新测试数据...');
                    StateManager.testData = data.screenshots;
                    StateManager.filteredData = [...data.screenshots];
                    TableManager.updateTable();

                    // 自动显示最新截图
                    if (StateManager.testData.length > 0) {
                        const latestScreenshot = StateManager.testData[StateManager.testData.length - 1];
                        console.log('显示最新截图:', latestScreenshot);
                        ImageManager.updateImagePreview(latestScreenshot);
                    }
                }
            }

            // 更新最后更新时间
            DOM.lastUpdate.textContent = `最后更新: ${new Date().toLocaleTimeString()}`;

            // 如果测试已完成或失败，但数据还在，继续刷新
            if ((data.test_status === 'completed' || data.test_status === 'failed') && 
                data.screenshots && data.screenshots.length > 0) {
                // 保持自动刷新，但降低频率
                if (!StateManager.refreshTimer) {
                    StateManager.refreshTimer = setInterval(() => {
                        this.fetchTestStatus();
                    }, CONFIG.refreshInterval * 2); // 降低刷新频率
                }
            }
        } catch (error) {
            console.error('获取测试状态失败:', error);
        }
    },

    /**
     * 显示错误信息
     */
    showErrorMessage(message, details = null) {
        // 创建错误提示框
        let errorDiv = document.getElementById('errorMessage');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.id = 'errorMessage';
            errorDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #ff4444;
                color: white;
                padding: 15px;
                border-radius: 5px;
                max-width: 400px;
                z-index: 1000;
                box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            `;
            document.body.appendChild(errorDiv);
        }

        let errorContent = `<strong>测试失败</strong><br>${message}`;
        if (details) {
            errorContent += `<br><details style="margin-top: 10px;"><summary>查看详细错误</summary><pre style="font-size: 12px; margin-top: 5px;">${details}</pre></details>`;
        }

        errorDiv.innerHTML = errorContent;

        // 5秒后自动隐藏
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 10000);
    },

    /**
     * 更新测试状态显示
     */
    updateTestStatus(status, text) {
        console.log('更新测试状态:', status, text);
        DOM.statusDot.className = `status-dot status-${status}`;
        DOM.statusText.textContent = text;

        DOM.startTestBtn.disabled = status === 'running';
        DOM.stopTestBtn.disabled = status !== 'running';
    }
};

/* =============== 初始化应用 =============== */
document.addEventListener('DOMContentLoaded', () => {
    console.log('初始化应用...');
    //多选框初始化
    initLanguageMultiselect();
    // 初始化页面切换按钮
    // document.getElementById('switchToUI').addEventListener('click', () => {
    //     window.location.href = '/';
    // });
    // // 绑定切换按钮事件
    // document.getElementById('switchToTask').addEventListener('click', () => {
    //     window.location.href = '/task_dispatch';
    // });
    
    // 初始化表格事件监听
    TableManager.initEventListeners();
    
    // 初始化测试控制
    TestController.initControls();
    
    // 获取初始状态
    TestController.fetchTestStatus();
    
    // 如果测试正在运行，开始自动刷新
    setTimeout(() => {
        console.log('检查是否需要启动自动刷新...');
        if (DOM.statusDot.classList.contains('status-running')) {
            console.log('测试正在运行，启动自动刷新');
            TestController.startAutoRefresh();
        }
    }, 1000);

    // 初始化图片管理模块
    ImageManager.init();
    
    // 检查模态框是否正确创建
    const modal = document.getElementById('imageModal');
    if (!modal) {
        console.error('模态框创建失败，重新创建...');
        ImageManager.createModal();
    } else {
        console.log('模态框已成功创建');
    }
});
/* ===== 多选下拉框功能 ===== */
function initLanguageMultiselect() {
    const wrapper = document.querySelector('.multiselect-wrapper');
    const trigger = wrapper.querySelector('.multiselect-trigger');
    const placeholder = document.getElementById('languagePlaceholder');
    const options = wrapper.querySelectorAll('.multiselect-option input');
    let selectedCount = 0;

    // 切换下拉框显示状态
    trigger.addEventListener('click', (e) => {
        wrapper.classList.toggle('open');
        e.stopPropagation();
    });

    // 点击外部关闭下拉框
    document.addEventListener('click', (e) => {
        if (!wrapper.contains(e.target)) {
            wrapper.classList.remove('open');
        }
    });

    // 处理选项选择
    options.forEach(option => {
        option.addEventListener('change', () => {
            selectedCount = 0;
            options.forEach(opt => {
                if (opt.checked) selectedCount++;
            });

            if (selectedCount === 0) {
                placeholder.textContent = '请选择';
            } else if (selectedCount === 1) {
                const selectedLabel = option.parentElement.querySelector('label').textContent;
                placeholder.textContent = selectedLabel;
            } else {
                placeholder.textContent = `已选${selectedCount}个`;
            }
        });
    });
}
