# coding: utf-8
# Project：car_auto_test
# File：test_B001.py
# Author：杨郑健
# Date ：2025/6/11 10:13
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级包下载模块")
@AllureDecorators.title("非off档不触发")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_B001(driver, launch_app):
    """
    测试步骤
    1、车辆解防，启动车辆
    2、待车端检测到升级任务并下载完成

    预期结果
    2、非off档状态升级包下载完成后，无新版本提示框
    """
    pass

