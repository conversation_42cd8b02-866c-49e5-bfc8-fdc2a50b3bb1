// multiselect.js

class MultiSelectDropdown {
    constructor(selector, options) {
        this.$dropdown = $(selector);
        this.$button = this.$dropdown.find('.dropdown-button');
        this.$content = this.$dropdown.find('.dropdown-content');
        this.$placeholder = this.$dropdown.find('.placeholder');
        this.options = options;
        this.selectedOptions = [];

        this.init();
    }

    // 其他方法与方案一相同...
    // init()、renderOptions()、toggleOption() 等方法
}

// 初始化所有下拉框的函数
function initAllMultiSelectDropdowns() {
    // 语言下拉框
    const languageDropdown = new MultiSelectDropdown('#languageDropdown', [
        { id: 'CN', name: '中文' },
        { id: 'EN', name: '英文' },
        { id: 'DE', name: '德文' },
        { id: 'FR', name: '法文' },
        { id: 'AR', name: '阿拉伯文' },
        { id: 'ES', name: '西班牙文' },
        { id: 'IT', name: '意大利文' },
        { id: 'JA', name: '日文' }
    ]);

    // 已选标签
    let selectedTags = [];

    // 初始化下拉框
    function initDropdown() {
        // 清空现有内容
        $('#dropdownContent').empty();

        // 添加标签选项
        $.each(tags, function(index, tag) {
            const isSelected = $.grep(selectedTags, function(t) {
                return t.id === tag.id;
            }).length > 0;

            const option = $('<div>').addClass('tag-option');
            const checkbox = $('<input>', {
                type: 'checkbox',
                class: 'tag-checkbox',
                checked: isSelected,
                value: tag.id
            }).on('change', function() {
                toggleTag(tag);
            });

            const label = $('<label>').text(tag.name);

            option.append(checkbox, label);
            option.on('click', function(e) {
                // 阻止事件冒泡到父元素
                if (e.target !== checkbox[0]) {
                    checkbox.trigger('click');
                }
            });

            $('#dropdownContent').append(option);
        });
    }

    // 切换标签选择状态
    function toggleTag(tag) {
        const existingIndex = $.map(selectedTags, function(t, index) {
            return t.id === tag.id ? index : null;
        })[0];

        if (existingIndex === undefined) {
            // 添加标签
            selectedTags.push(tag);
        } else {
            // 移除标签
            selectedTags.splice(existingIndex, 1);
        }

        // 更新显示
        updateButtonDisplay();
    }

    // 更新下拉按钮中显示的已选标签
    function updateButtonDisplay() {
        const button = $('#dropdownButton');
        const placeholder = $('#placeholder');

        // 先清空按钮内容(保留占位符和箭头)
        button.contents().filter(function() {
            return !$(this).hasClass('placeholder') && !$(this).hasClass('arrow');
        }).remove();

        if (selectedTags.length === 0) {
            placeholder.show();
        } else {
            placeholder.hide();

            // 添加所有已选标签
            $.each(selectedTags, function(index, tag) {
                const tagElement = $('<span>').addClass('selected-tag').text(tag.name);
                const removeButton = $('<span>').addClass('remove-tag').text('×')
                   .data('tag-id', tag.id);

                tagElement.append(removeButton);
                tagElement.insertBefore($('.arrow'));
            });
        }
    }

    // 使用事件委托处理删除按钮点击
    $(document).on('click', '.remove-tag', function(e) {
        e.stopPropagation();
        const tagId = $(this).data('tag-id');
        selectedTags = $.grep(selectedTags, function(tag) {
            return tag.id !== tagId;
        });
        updateButtonDisplay();
        initDropdown();
    });

    // 切换下拉框显示/隐藏
    $('#dropdownButton').on('click', function() {
        const isOpen = $('#dropdownContent').is(':visible');
        $('#dropdownContent').toggle(!isOpen);
        $('.arrow').toggleClass('open', !isOpen);
    });

    // 点击页面其他区域关闭下拉框
    $(document).on('click', function(event) {
        if (!$(event.target).closest('.multiselect-dropdown').length) {
            $('#dropdownContent').hide();
            $('.arrow').removeClass('open');
        }
    });
    // 初始化
    initDropdown();
}

// 当DOM加载完成后初始化
// $(document).ready(initAllMultiSelectDropdowns);