import pymysql
from xml.dom import minidom
import re
import os
from utils.DbManage.verification_mange import VerificationMange as vfm
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import AllureSteps
class code_ver:
    def __init__(self):
        self.db = vfm()
    def get_code(self, svg_file_path):
        path_elements = self.get_path_elements(svg_file_path)
        sorted_paths = self.sort_paths_by_left_position(path_elements)
        code = ""
        for path in sorted_paths:
            d = path.getAttribute('d')
            width, height = self.get_path_dimensions(d)
            path_data = d.split(" ")
            path_num = len(path_data)
            row_list = self.db.select_code(path_num)
            # print(row_list)
            if len(row_list) == 1:
                code = code + row_list[0]["value"]
            else:
                closest_value = None
                closest_diff = float('inf')
                for item in row_list:
                    width_diff = abs(float(item['width']) - width)
                    height_diff = abs(float(item['height']) - height)
                    total_diff = width_diff + height_diff
                    if total_diff < closest_diff:
                        closest_diff = total_diff
                        closest_value = item["value"]
                code = code + closest_value
        return code

    # 通过minidom获取所有path标签
    def get_path_elements(self, svg_file_path):
        doc = minidom.parse(svg_file_path)
        path_elements = doc.getElementsByTagName('path')
        return path_elements

    def has_stroke_attribute(self, path_element):
        return path_element.hasAttribute('stroke')

    def get_path_element_left_position(self, path_element):
        d = path_element.getAttribute('d')
        coordinates = re.findall(r'[MmLlHhVvCcSsQqTtAa]\s*([-+]?\d*\.?\d+)', d)
        # 从路径数据中提取第一个坐标点
        x_values = [float(x) for x in coordinates[0::2]]
        left_position = min(x_values)
        x = left_position
        return x

    def sort_paths_by_left_position(self, path_elements):
        paths_without_stroke = []
        for path in path_elements:
            if not self.has_stroke_attribute(path):
                paths_without_stroke.append(path)
        sorted_paths = sorted(paths_without_stroke, key=lambda path: self.get_path_element_left_position(path))
        return sorted_paths

    def getMinXY(self, data):
        xs = []
        ys = []
        i = 0
        for f in re.findall('\d+\.\d+', data):
            if i % 2:
                ys.append(float(f))
            else:
                xs.append(float(f))
            i = i + 1
        xs.sort()
        ys.sort()
        return [xs[0], ys[0]]

    def get_path_dimensions(self, d):
        coordinates = re.findall(r'[-+]?\d*\.?\d+', d)
        x_values = [float(x) for x in coordinates[::2]]
        y_values = [float(y) for y in coordinates[1::2]]
        min_x = min(x_values)
        max_x = max(x_values)
        min_y = min(y_values)
        max_y = max(y_values)
        width = round(max_x - min_x, 2)
        height = round(max_y - min_y, 2)
        return width, height

@AllureSteps.log_step("验证码识别")
def ocr_image(img):
    try:
        code = code_ver().get_code(img)
        logger.info(f"验证码识别结果：{code}")
        return code
    except:
        return False

# 使用示例
if __name__ == "__main__":
    ocr_image(r"C:\Users\<USER>\Downloads\svg-captcha-recognize-python\captcha_svgs\captcha_113.svg")
