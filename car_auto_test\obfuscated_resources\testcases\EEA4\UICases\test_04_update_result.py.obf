'''
升级结果类
'''

import time
import pytest
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.ImageRecognition.Image_comparison import *
from utils.SerialTool.actuator import act
from utils.AdbPort.Adb_Port import AdbExec

class Test_Bench_Update_Result:
    mode_value: int = None
    mode_dict: dict = None
    vehicle_flag: int = None

    # 使用mode fixture获取当前的模式值
    @pytest.fixture(autouse=True)
    def setup_ui_mode(self, mode, mode_dict, vehicle_flag):
        """设置当前的UI模式和mode_dict以及车型标志位"""
        self.mode_value = mode
        self.mode_dict = mode_dict
        self.vehicle_flag = vehicle_flag
        logger.info(f"当前测试环境: 模式值={self.mode_value}, 车型标志位={self.vehicle_flag}")

    # @pytest.mark.skip
    def test_update_result_001(self, driver):
        """
        升级成功
        """
        logger.info('------------------ 开始制造:升级成功---------------------')
        # 配置文件恢复初始状态
        act.set_file('speed', 0)
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        # objects.get_task()
        objects.update_success(take_screenshot=True)
        time.sleep(22)
        if act.manufacture ==1:
            act.power_on()
        objects.screen_image('升级成功，请体验新版本吧',self.mode_value)
        time.sleep(0.5)
        objects.click_element('success_btn')
        # 等待 退ota下电
        time.sleep(8)
        objects.screen_image('升级成功，主页面显示',self.mode_value)

    # @pytest.mark.skip
    def test_update_result_002(self,driver,ui_eea4_app):
        '''
       升级成功、退出ota模式失败
        Returns:

        '''
        logger.info('------------------ 开始制造:升级成功、退出ota模式失败---------------------')
        act.set_file('speed',0)
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.get_task()
        objects.update_success_otamodeout_fail()
        act.set_file('speed',0)
        time.sleep(1)
        act.ota_mode_out()


    # @pytest.mark.skip
    def test_bench_update_result_003(self,driver,ui_eea4_app):
        '''
         升级成功、下电失败
        Returns:

        '''
        logger.info('------------------ 开始制造:升级成功、下电失败---------------------')
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.get_task()
        objects.update_success_power_fail()


    # @pytest.mark.skip
    def test_update_result_004(self,driver,ui_eea4_app):
        '''
        升级失败、回滚成功
        Returns:

        '''
        # 配置文件恢复初始状态
        logger.info('------------------ 开始制造:升级失败、回滚成功---------------------')
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.get_task()
        act.set_file('speed', 0)
        objects.rollback_success(take_screenshot=True)
        time.sleep(22)
        if act.manufacture == 1:
            act.power_on()
        objects.screen_image('升级失败、回滚成功、确定按钮',self.mode_value)

        objects.click_element('success_btn')
        # 等待退ota下电
        time.sleep(8)

    # @pytest.mark.skip
    def test_update_result_005(self,driver,ui_eea4_app):
        '''
        升级失败、回滚成功、退出ota模式失败
        Returns:

        '''
        logger.info('------------------ 开始制造:升级失败、回滚成功、退出ota模式失败---------------------')
        # act.power_on()
        act.set_file('speed', 0)
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.get_task()
        objects.rollback_success_otamodeout_fail()
        act.set_file('speed', 0)
        # time.sleep(1)
        act.ota_mode_out()

    # @pytest.mark.skip
    def test_bench_update_result_006(self,driver,ui_eea4_app):
        '''
         升级失败、回滚成功、下电失败
        Returns:

        '''
        logger.info('------------------ 开始制造:升级失败、回滚成功、下电失败---------------------')
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.get_task()
        objects.rollback_success_poweroff_fail()






