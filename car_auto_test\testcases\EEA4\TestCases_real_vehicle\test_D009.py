# coding: utf-8
# Project：car_auto_test
# File：test_D009.py
# Author：杨郑健
# Date ：2025/6/12 15:12
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("前置条件检测")
@AllureDecorators.title("手刹-拉起检测")
def test_D009(driver, launch_app):
    """
    测试步骤
    1、后台“升级策略”配置前置条件“手刹-拉起”
    2、车端检测到升级任务并下载完成"

    预期结果
    1、手刹释放状态，查看“检测升级前置条件”
    2、手刹拉起状态，查看“检测升级前置条件”"
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        "tbox_status": "NOTREAD",
        'handbrake': ['OFF', 'ON'],  # 将挡位放在列表中
        "task": "normal"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'OFF':
            logger.info("后刹OFF，检测前置条件不过")
            objects.get_task()
            objects.disclaimer_click()
            sleep(5)
            result = objects.check_element_exists("precondition")
            assert result
        elif current_state == 'ON':
            logger.info("手刹ON，检测前置条件通过")
            sleep(5)
            result = objects.check_element_exists("precondition")
            assert result == False
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()