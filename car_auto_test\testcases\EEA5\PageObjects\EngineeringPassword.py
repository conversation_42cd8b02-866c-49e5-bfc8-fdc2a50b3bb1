
import re
import datetime
from config import Config
from utils.LoggingSystem.Logger import logger

class EngineeringPassword:
    def __init__(self):
        # self.vin = Config.EEA5_VIN
        self.vin = Config.EE4_VIN
        # self.vin = 'LNNAJDDS3RD000414'
        self.wv = 123456

    @staticmethod
    def format_date(date):
        """日期格式化"""
        return date.strftime("%y%m%d")

    def extract_vin_digits(self):
        """VIN处理强"""
        try:
            # 截取最后6字符并提取数字
            last_six = self.vin[-6:]
            digits = re.findall(r'\d', last_six)

            # 返回数字组合或0
            return int(''.join(digits)) if digits else 0

        except (IndexError, TypeError) as e:
            logger.error(f"VIN号处理异常: {str(e)}")
            return 0

    def generate(self):
        """密码生成"""
        try:
            current_date = datetime.datetime.now()
            date_str = self.format_date(current_date)

            base = self.wv + self.extract_vin_digits()
            calculation = base * int(date_str)
            final_password = calculation % 1000000

            return f"{final_password:06d}"

        except Exception as e:
            logger.error(f"密码生成失败: {str(e)}")
            return "000000"

if __name__ == "__main__":
    generator = EngineeringPassword()
    print(f"当前工程密码: {generator.generate()}")
