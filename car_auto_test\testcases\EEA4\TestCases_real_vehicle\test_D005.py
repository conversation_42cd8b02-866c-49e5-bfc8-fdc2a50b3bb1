# coding: utf-8
# Project：car_auto_test
# File：test_D005.py
# Author：杨郑健
# Date ：2025/6/12 14:08
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("前置条件检测")
@AllureDecorators.title("前置条件检测:档位检测(无m档车型无忽略m档检测)")
def test_D005(driver, launch_app):
    """
    测试步骤
    1、切换车辆档位至d档，查看“检测升级前置条件
    2、切换车辆档位至r档，查看“检测升级前置条件
    3、切换车辆档位至n档，查看“检测升级前置条件
    4、切换车辆档位至m档，查看“检测升级前置条件
    5、切换车辆档位至p档，查看“检测升级前置条件”

    预期结果
    1、“档位p档”检测不通过
    2、“档位p档”检测不通过
    3、“档位p档”检测不通过
    4、“档位p档”检测不通过
    5、“档位p档”检测通过
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        "tbox_status": "NOTREAD",
        "gear": ["P", "R", "N", "D"],
        "task": "normal"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")
        is_x_mark = objects.check_mark()
        # 根据当前挡位进行不同的断言
        if current_state == 'D':
            assert is_x_mark == False, "D挡时应该显示X标记"
            logger.info("D挡验证完成")
        elif current_state == 'R':
            assert is_x_mark == False, "R挡时应该显示X标记"
            logger.info("R挡验证完成")
        elif current_state == 'N':
            assert is_x_mark == False, "N挡时应该显示X标记"
            logger.info("N挡验证完成")
        elif current_state == 'P':

            assert is_x_mark == True, "P挡时应该显示√标记"
            logger.info("P挡验证完成")
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()

