'''
统计文件夹下语言的数量 以及 缺失的页面
'''

import os
from collections import defaultdict

# 路径根据你的项目结构调整
base_dir = os.path.join('T26PHEV')

# 1. 获取所有语言文件夹
language_folders = [
    f for f in os.listdir(base_dir)
    if os.path.isdir(os.path.join(base_dir, f))
]

# 2. 统计每个语言下的图片文件名
lang2pics = {}
all_pic_names = set()
for lang in language_folders:
    lang_path = os.path.join(base_dir, lang)
    pics = [f for f in os.listdir(lang_path) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp'))]
    lang2pics[lang] = set(pics)
    all_pic_names.update(pics)

# 3. 统计数量
for lang, pics in lang2pics.items():
    print(f"{lang}: {len(pics)} 张图片")

# 4. 检查是否有不一致
counts = [len(pics) for pics in lang2pics.values()]
if len(set(counts)) == 1:
    print("所有语言文件夹的图片数量一致。")
else:
    print("图片数量不一致，缺失图片如下：")
    for lang, pics in lang2pics.items():
        missing = all_pic_names - pics
        if missing:
            print(f"{lang} 缺失：{', '.join(sorted(missing))}")
