"""
全局夹具文件
"""
import pytest
import uiautomator2 as u2
import time
import json
from utils.AdbPort.Adb_Port import AdbExec
from config.Config import *
from datetime import datetime
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import AllureAttachments

# ui截图使用
def pytest_addoption(parser):
    """添加命令行参数"""
    parser.addoption("--mode", action="store", default="0")
    parser.addoption("--mode_dict_path", action="store", default=None)
    parser.addoption("--vehicle_flag", action="store", default="1")
    parser.addoption("--language", action="store", default=None)




@pytest.fixture
def mode(request):
    """返回当前的模式值"""
    return request.config.getoption("--mode")

@pytest.fixture
def mode_dict(request):
    """返回mode_dict字典"""
    mode_dict_path = request.config.getoption("--mode_dict_path")
    logger.info(f"获取到mode_dict_path参数: {mode_dict_path}")
    if mode_dict_path and os.path.exists(mode_dict_path):
        try:
            with open(mode_dict_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"读取mode_dict_path文件失败: {e}")
    return {}

@pytest.fixture
def vehicle_flag(request):
    """返回车型标志位"""
    return request.config.getoption("--vehicle_flag")

@pytest.fixture
def language(request):
    """返回语言标志位"""
    return request.config.getoption("--language")



@pytest.fixture(scope='session')
def driver():
    """
    初始化设备连接
    Returns:driver 对象
    """
    while True:
        if AdbExec().devices:  # 持续检查设备是否连接
            break
        time.sleep(10)  # 每10秒检查一次

    driver = u2.connect()  # 连接设备
    driver.implicitly_wait(5)  # 隐式等待
    return driver


@pytest.fixture(scope="function")
def launch_app(driver, request):
    """
    Args:
        driver: 启动对象
        request: pytest请求对象，用于获取测试用例信息
    Returns:
    """

    # 获取当前测试用例的名称
    test_name = request.node.name
    driver.app_clear(FOUR_APP_PACKAGE)
    driver.app_start(FOUR_APP_PACKAGE)
    time.sleep(3)
    start_time = datetime.now()
    yield
    end_time = datetime.now()
    time_diff = end_time - start_time
    seconds = time_diff.total_seconds()
    logger.info(f"测试用例 {test_name} 执行完毕、耗时{seconds:.2f}秒")
    AllureAttachments.attach_screenshot(test_name + "-RESULTS_SCREENSHOT")
    AllureAttachments.attach_log_file(test_name + "-OTA_LOG")
    AllureAttachments.attach_zlglog_file(test_name)
    driver.app_clear(FOUR_APP_PACKAGE)
    # AllureAttachments.attach_screenshot(test_name + "-RESULTS_SCREENSHOT")
    # AllureAttachments.attach_log_file(test_name + "-OTA_LOG")
    # driver.app_clear(FIVE_APP_PACKAGE)


@pytest.fixture(scope="function")
def ui_app(driver, request):
    """
       Args:
           driver: 启动对象
           request: pytest请求对象，用于获取测试用例信息
       Returns:
       """
 # 获取当前测试用例的名称
    driver.app_clear(FIVE_APP_PACKAGE)
    driver.app_start(FIVE_APP_PACKAGE)
    time.sleep(3)



@pytest.fixture(scope="function")
def ui_eea4_app(driver, request):
    # pass
    """
       Args:
           driver: 启动对象
           request: pytest请求对象，用于获取测试用例信息
       Returns:
       """

    driver.app_stop(FOUR_APP_PACKAGE)
    time.sleep(2)
    driver.app_start(FOUR_APP_PACKAGE)
    time.sleep(2)






