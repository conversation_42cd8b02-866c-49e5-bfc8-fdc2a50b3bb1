# coding: utf-8
# Project：car_auto_test
# File：test_A028.py
# Author：杨郑健
# Date ：2025/6/10 17:11
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("单向认证")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_A028(driver, launch_app):
    """
    操作步骤
    1、访问https://api-fota-uat.mychery.com:8443/v0/echo，查询云端证书有效时间
    2、将车端系统时间修改为超过云端证书有效时间
    3、车辆休眠后解防，启动车辆，查看是否可请求到升级任务

    预期结果
    3、无法请求到升级任务
    """
    pass