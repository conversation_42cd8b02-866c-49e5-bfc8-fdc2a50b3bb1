# coding: utf-8
# Project：car_auto_test
# File：test_B016.py
# Author：杨郑健
# Date ：2025/6/11 14:38
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级包下载模块")
@AllureDecorators.title("车端升级事件上报:完成下载升级包")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_B004(driver, launch_app):
    """
    测试步骤
    1.检测到升级任务并下载完成
    2.检查云端日志中是否有 3、Clientresponseisclm:总状态[完成下载升级包]状态:[完成下载升级包] 展示

    预期结果
    3、Clientresponseisclm:总状态[完成下载升级包]状态:[完成下载升级包]
    """
    objects = Step_object(driver)
    objects.get_task()
    result = False
    info = Cloud("奇瑞汽车").get_carinfo()
    for i in info:
        if i["message"].rfind("完成下载升级包") != -1:
            result = True
    assert result