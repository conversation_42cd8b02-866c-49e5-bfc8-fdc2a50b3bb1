# coding: utf-8
# Project：car_auto_test
# File：test_C002.py
# Author：杨郑健
# Date ：2025/6/11 15:14
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级主页面/小红点")
@AllureDecorators.title("出厂状态：主页面显示")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_C002(driver, launch_app):
    """
    测试步骤
    1、工程模式点击“清除数据”
    2、车端未检测到升级任务
    3、查看“升级主页面”

    预期结果
    3、车端页面仅显示“当前系统已是最新版本”，“预约升级”、“立即升级”置灰不可点击
    """
    pass