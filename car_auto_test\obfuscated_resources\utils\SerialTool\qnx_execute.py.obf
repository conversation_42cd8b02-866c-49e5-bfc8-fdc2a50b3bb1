from utils.LoggingSystem.Logger import logger
from utils.AdbPort.Adb_Port import adb
import subprocess
import time
import json
from utils.LoggingSystem.Logger import logger

def QNX_execute(
    base_command: str = "adb shell telnet cdc-qnx",
    username: str = "root",
    password: str = "False",
    command: str = "ls",
    timeout: int = 10
):
    proc = subprocess.Popen(
        base_command,
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        bufsize=1,  # 行缓冲
        shell=True
    )
    output = []
    stat = 0
    success = False
    max_time = time.time() + timeout
    try:
        # 实时交互循环
        while True:
            if time.time() > max_time:
                raise TimeoutError(f"连接超时 ({timeout}s)")

            # 读取一行输出 (非阻塞)
            line = proc.stdout.readline()
            if not line:
                if proc.poll() is not None:
                    break  # 进程已退出
                time.sleep(0.1)
                continue
            output.append(line)
            # sys.stdout.write(line)  # 可选：实时显示输出
            time.sleep(0.1)

            if "QNX" in line:
                proc.stdin.write(f"{username}\n")
                proc.stdin.flush()
                time.sleep(0.1)
                if password != "False":
                    proc.stdin.write("%s\n" %password)
                    proc.stdin.flush()

            # 检测登录成功提示 (如 $ 或 #)
            if "No home" in line or "# " in line:
                success = True
                break

        if success:
            # 执行命令
            proc.stdin.write(f"{command}\n")
            proc.stdin.write("exit\n")  # 退出会话
            proc.stdin.flush()
            stat = 1

        # 读取剩余输出
        remaining_output = proc.stdout.read()
        output.append(remaining_output)
        logger.info(output)
    except Exception as e:
        proc.kill()
        logger.info(f"错误: {str(e)}")
    finally:
        proc.stdin.close()
        proc.stdout.close()
        proc.stderr.close()

    return "".join(output), stat

def ordinary_command_mega(command):
    reverse, stat = QNX_execute(password="Mega#ICC%0630", command=command)
    # reverse, stat = QNX_execute(command=command)
    if stat == 0:
        logger.info('1')
        return False, reverse
    # return True, reverse
    logger.info('2')
    return True

def find_de_with_mega():
    res = ordinary_command_mega("find / -name uds_shell")
    paths = []
    for i in res[1].split("#"):
        if i.rfind("/uds_shell") != -1:
            for k in i.split("\n\n"):
                if k.rfind("/uds_shell") != -1:
                    paths.append(k.replace("\n", "").replace("uds_shell", "vehicle_info_config.json"))
    return paths

def push_config_with_mega():
    de_path = find_de_with_mega()
    logger.info(de_path)
    logger.info(adb.execute(r"adb root&adb push .\vehicle_info_config.json /sdcard"))
    logger.info(adb.execute("adb shell cp /sdcard/vehicle_info_config.json /qlog/vehicle_info_config.json"))
    if len(de_path) > 1:
        ordinary_command_mega(f"echo {'./'.join(de_path)} | xargs -n 1 cp /log/qlog/vehicle_info_config.json")
    ordinary_command_mega(f"cp /log/qlog/vehicle_info_config.json {de_path[0]}&reset")

def delete_config_with_mega():
    de_path = find_de_with_mega()
    logger.info(de_path)
    if len(de_path) > 1:
        ordinary_command_mega(f"rm {' '.join(de_path)}")
    ordinary_command_mega(f"rm {de_path[0]}&reset")

def set_file(*args):
    """
    设置配置文件，支持传递多个键值对
    直接在QNX目标路径下创建vehicle_info_config.json文件
    """
    from config.Config import eea4_qnx_config_file
    c_json = eea4_qnx_config_file.copy()

    # 处理参数
    if len(args) == 2:
        # 单个键值对
        key, value = args
        logger.info(f"设置单个键值对: {key}={value}")
        c_json[key] = value
    elif len(args) > 2 and len(args) % 2 == 0:
        # 多个键值对
        for i in range(0, len(args), 2):
            key = args[i]
            value = args[i+1]
            logger.info(f"设置键值对: {key}={value}")
            c_json[key] = value
    else:
        logger.error("参数格式错误，应为(key, value)或(key1, value1, key2, value2, ...)")
        return False

    logger.info(f"更新后的配置: {c_json}")
    config_file_json = json.dumps(c_json)

    try:

        echo_cmd = f"echo '{config_file_json}' > /usr/bin/vehicle_info_config.json"
        # logger.info(f"执行命令: {echo_cmd}")
        result = ordinary_command_mega(echo_cmd)
        logger.info(f"创建配置文件结果: {result}")
        # reboot_master()

        return True

    except Exception as e:
        logger.error(f"设置配置文件时发生错误: {str(e)}")
        return False

#
#
# def reboot_master():
#     ordinary_command_mega('kill -9 `cat /ota/android/wayos/com4de/otamaster.pid`')
    # time.sleep(2)


def power_on():
    cmd = ("on -T carota_subde_t -u carota_subde /usr/bin/vehicle_info "
           "--remote-power-on --log-level 5 -l ./test.log -q 0")
    ordinary_command_mega(cmd)


def ota_mode_out():
    logger.info('主动请求退出ota模式')
    cmd = ("on -T carota_subde_t -u carota_subde /usr/bin/vehicle_info "
           "--ota-mode-out --log-level 5 -l ./test.log -q 0")
    ordinary_command_mega(cmd)
    time.sleep(3)

class cmd_execute_with_maga:
    def set_file(self, *args):
        """
        设置配置文件，支持传递多个键值对
        直接在QNX目标路径下创建vehicle_info_config.json文件
        """
        from config.Config import eea4_qnx_config_file
        c_json = eea4_qnx_config_file.copy()

        # 处理参数
        if len(args) == 2:
            # 单个键值对
            key, value = args
            logger.info(f"设置单个键值对: {key}={value}")
            c_json[key] = value
        elif len(args) > 2 and len(args) % 2 == 0:
            # 多个键值对
            for i in range(0, len(args), 2):
                key = args[i]
                value = args[i + 1]
                logger.info(f"设置键值对: {key}={value}")
                c_json[key] = value
        else:
            logger.error("参数格式错误，应为(key, value)或(key1, value1, key2, value2, ...)")
            return False

        logger.info(f"更新后的配置: {c_json}")
        config_file_json = json.dumps(c_json)

        try:

            echo_cmd = f"echo '{config_file_json}' > /usr/bin/vehicle_info_config.json"
            # logger.info(f"执行命令: {echo_cmd}")
            result = ordinary_command_mega(echo_cmd)
            logger.info(f"创建配置文件结果: {result}")
            # reboot_master()

            return True

        except Exception as e:
            logger.error(f"设置配置文件时发生错误: {str(e)}")
            return False
    def power_on(self):
        cmd = ("on -T carota_subde_t -u carota_subde /usr/bin/vehicle_info "
               "--remote-power-on --log-level 5 -l ./test.log -q 0")
        ordinary_command_mega(cmd)

    def power_off(self):
        cmd = ("on -T carota_subde_t -u carota_subde /usr/bin/vehicle_info "
               "--remote-power-off --log-level 5 -l ./test.log -q 0")
        ordinary_command_mega(cmd)

    def ota_mode_out(self):
        logger.info('主动请求退出ota模式')
        cmd = ("on -T carota_subde_t -u carota_subde /usr/bin/vehicle_info "
               "--ota-mode-out --log-level 5 -l ./test.log -q 0")
        ordinary_command_mega(cmd)
        time.sleep(3)

    def rm_package(self):
        cmd = "rm /ota/android/wayos/com4de/dm/*"
        ordinary_command_mega(cmd)
        
class cmd_execute_with_neusoft:
    def __init__(self):
        self.base = "adb shell busybox telnet 172.20.1.1"

    def set_file(self, *args):
        """
        设置配置文件，支持传递多个键值对
        直接在QNX目标路径下创建vehicle_info_config.json文件
        """
        from config.Config import eea4_qnx_config_file
        c_json = eea4_qnx_config_file.copy()

        # 处理参数
        if len(args) == 2:
            # 单个键值对
            key, value = args
            logger.info(f"设置单个键值对: {key}={value}")
            c_json[key] = value
        elif len(args) > 2 and len(args) % 2 == 0:
            # 多个键值对
            for i in range(0, len(args), 2):
                key = args[i]
                value = args[i + 1]
                logger.info(f"设置键值对: {key}={value}")
                c_json[key] = value
        else:
            logger.error("参数格式错误，应为(key, value)或(key1, value1, key2, value2, ...)")
            return False

        logger.info(f"更新后的配置: {c_json}")
        config_file_json = json.dumps(c_json)

        try:

            echo_cmd = f"echo '{config_file_json}' > /usr/bin/vehicle_info_config.json"
            # logger.info(f"执行命令: {echo_cmd}")
            result = ordinary_command_mega(echo_cmd)
            logger.info(f"创建配置文件结果: {result}")
            # reboot_master()

            return True

        except Exception as e:
            logger.error(f"设置配置文件时发生错误: {str(e)}")
            return False
    def power_on(self):
        cmd = ("on -T carota_subde_t -u carota_subde /usr/bin/vehicle_info "
               "--remote-power-on --log-level 5 -l ./test.log -q 0")
        QNX_execute(base_command=self.base, command=cmd)

    def power_off(self):
        cmd = ("on -T carota_subde_t -u carota_subde /usr/bin/vehicle_info "
               "--remote-power-off --log-level 5 -l ./test.log -q 0")
        QNX_execute(base_command=self.base, command=cmd)