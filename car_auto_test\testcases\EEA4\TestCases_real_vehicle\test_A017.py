# coding: utf-8
# Project：car_auto_test
# File：test_A017.py
# Author：杨郑健
# Date ：2025/6/10 16:08
# coding: utf-8
# Project：car_auto_test
# File：test_A016.py
# Author：杨郑健
# Date ：2025/6/10 16:04
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from time import sleep
import pytest

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("wifi网络下获取车辆信息")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_A017(driver, launch_app):
    """
    测试步骤
    1、电源挡位为READY
    2、待TBOX自诊断结束后进行任务检查（tel-diagnose: TEL_DIAGNOSE_NOT_READ）
    3、查看后台活动日志中的零件信息

    预期结果
    3、根据后台车型bom读取上传ecu信息，ecu信息可按对应电检参数配置显示，硬件零件号、
    硬件版本号、软件版本号、软件零件号、供应商代码、产品序列号、Bootloader版本号、标定版本号
    （当前逻辑是判断其中几个一定不为空的值存在即整个零件信息正确，待优化）
    """
    objects = Step_object(driver)
    objects.get_task()
    info = Cloud("奇瑞汽车").get_carinfo()
    for i in info:
        if i["message"] == "3.查询车型零件":
            for k in i["data"]["result"].keys():
                assert len(i["data"]["result"][k]["name"]) > 1
                assert len(i["data"]["result"][k]["sv"]) > 1
                assert len(i["data"]["result"][k]["hv"]) > 1