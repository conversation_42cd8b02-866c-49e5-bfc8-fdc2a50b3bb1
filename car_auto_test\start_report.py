#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动测试报告的独立脚本
解决 allure 命令和编码问题
"""
import os
import sys
import time
import shutil
import subprocess
import webbrowser
from pathlib import Path

def start_allure_report():
    """启动 Allure 测试报告"""
    
    # 设置路径
    base_dir = Path(__file__).parent
    tmp_path = base_dir / "testreport" / "Report" / "tmp"
    allure_http_exe = base_dir / "testreport" / "Report" / "allure-http.exe"
    
    # 创建报告目录
    timestamp = int(time.time())
    report_path = base_dir / "testreport" / "Report" / str(timestamp)
    report_path.mkdir(parents=True, exist_ok=True)
    
    print(f"报告将生成到: {report_path}")
    
    # 检查测试结果
    if not tmp_path.exists() or not any(tmp_path.iterdir()):
        print("❌ 没有找到测试结果文件")
        print(f"请确保在 {tmp_path} 目录下有测试结果")
        return False
    
    # 方法1：尝试使用系统 allure 命令
    allure_success = False
    try:
        print("🔄 尝试使用系统 allure 命令生成报告...")
        
        # 设置环境变量
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['LANG'] = 'en_US.UTF-8'
        
        result = subprocess.run(
            ["allure", "generate", str(tmp_path), "-o", str(report_path), "--clean"],
            capture_output=True, 
            text=True, 
            encoding='utf-8', 
            env=env,
            timeout=60  # 60秒超时
        )
        
        if result.returncode == 0:
            print("✅ 使用系统 allure 命令生成报告成功")
            allure_success = True
        else:
            print(f"⚠️ 系统 allure 命令执行失败:")
            print(f"错误输出: {result.stderr}")
            
    except FileNotFoundError:
        print("ℹ️ 系统未安装 allure 命令行工具")
    except subprocess.TimeoutExpired:
        print("⚠️ allure 命令执行超时")
    except Exception as e:
        print(f"⚠️ 使用系统 allure 命令失败: {e}")
    
    # 方法2：直接复制文件
    if not allure_success:
        print("🔄 尝试直接复制测试结果文件...")
        try:
            if report_path.exists():
                shutil.rmtree(report_path)
            shutil.copytree(tmp_path, report_path)
            print("✅ 测试结果文件复制成功")
        except Exception as e:
            print(f"❌ 复制测试结果文件失败: {e}")
            return False
    
    # 启动 allure-http.exe
    if allure_http_exe.exists():
        try:
            # 复制 allure-http.exe 到报告目录
            allure_http_dst = report_path / "allure-http.exe"
            shutil.copy2(allure_http_exe, allure_http_dst)
            print("✅ allure-http.exe 复制成功")
            
            # 启动服务器
            print("🚀 启动 allure-http.exe 服务器...")
            
            if os.name == 'nt':  # Windows
                # 在新的控制台窗口中启动
                subprocess.Popen(
                    [str(allure_http_dst)],
                    cwd=str(report_path),
                    creationflags=subprocess.CREATE_NEW_CONSOLE
                )
            else:  # Linux/Mac
                subprocess.Popen(
                    [str(allure_http_dst)],
                    cwd=str(report_path)
                )
            
            # 等待服务器启动
            print("⏳ 等待服务器启动...")
            time.sleep(5)
            
            # 打开浏览器
            print("🌐 打开浏览器...")
            webbrowser.open("http://127.0.0.1:8080/")
            print("✅ 测试报告已在浏览器中打开: http://127.0.0.1:8080/")
            
            return True
            
        except Exception as e:
            print(f"❌ 启动 allure-http.exe 失败: {e}")
            
    else:
        print("⚠️ allure-http.exe 文件不存在")
        print(f"报告已生成在: {report_path.absolute()}")
        print("请手动打开 index.html 文件查看报告")
        
        # 尝试打开 index.html
        index_file = report_path / "index.html"
        if index_file.exists():
            try:
                webbrowser.open(f"file://{index_file.absolute()}")
                print("✅ 已在浏览器中打开报告")
                return True
            except Exception as e:
                print(f"⚠️ 无法自动打开浏览器: {e}")
    
    return False

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 Allure 测试报告启动器")
    print("=" * 50)
    
    try:
        success = start_allure_report()
        if success:
            print("\n🎉 报告启动成功！")
            print("💡 提示：关闭此窗口不会影响报告服务器运行")
        else:
            print("\n❌ 报告启动失败")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户取消操作")
        return 1
    except Exception as e:
        print(f"\n💥 发生未知错误: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
