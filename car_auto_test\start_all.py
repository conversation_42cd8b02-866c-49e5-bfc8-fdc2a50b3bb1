import subprocess
import time
import webbrowser
import os

def main():
    """主函数"""
    try:
        # 获取当前目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 启动Flask服务器（使用新窗口）
        print("正在启动Flask服务器...")
        flask_cmd = f'start cmd /k "cd /d {current_dir} && python app.py"'
        subprocess.run(flask_cmd, shell=True)
        time.sleep(3)
        
        # 启动GUI程序（使用新窗口）
        print("正在启动GUI程序...")
        gui_cmd = f'start cmd /k "cd /d {current_dir} && python gui.py"'
        subprocess.run(gui_cmd, shell=True)
        
        # 打开浏览器
        print("正在打开浏览器...")
        webbrowser.open('http://localhost:5000')

        
    except Exception as e:
        print(f"启动过程中出错: {str(e)}")

if __name__ == "__main__":
    main() 