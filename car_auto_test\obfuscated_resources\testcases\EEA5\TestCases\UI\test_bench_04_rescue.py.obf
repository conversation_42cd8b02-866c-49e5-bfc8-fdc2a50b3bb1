'''
紧急救援
'''

import time

import pytest
import functools

from testcases.EEA5.PageObjects.TestObject import Step_object
from utils.ImageRecognition.Image_comparison import *
from utils.SerialTool.vcc_tool import vcc
from utils.CloudOperation.chery_operation import Cloud



class Test_Bench_Rescue:
    # 使用mode fixture获取当前的模式值
    @pytest.fixture(autouse=True)
    def setup_ui_mode(self, mode, mode_dict):
        """设置当前的UI模式和mode_dict"""
        self.mode_value = mode
        self.mode_dict = mode_dict
        logger.info(f"当前测试环境: 模式值={self.mode_value}")

    @pytest.mark.skip
    def test_bench_rescue_001(self, driver, ui_app):
        '''
        正常救援任务  - 升级成功
        Args:
            driver:
            launch_app:

        Returns:

        '''

        Cloud("奇瑞欧盟").sync_rescue_task([26011])
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.remote_resuce_upgrade_success()

    @pytest.mark.skip
    def test_bench_rescue_002(self, driver, ui_app):
        '''
        正常救援任务  - 前置不过
        Args:
            driver:
            launch_app:

        Returns:

        '''

        objects = Step_object(driver, self.mode_value, self.mode_dict)
        vcc.set_file('epb_status', 0)
        objects.remote_resuce_predition_fail()
        vcc.set_file('epb_status', 1)

    # @pytest.mark.skip
    def test_bench_rescue_003(self, driver, ui_app):
        '''
        正常救援任务  - 下载失败
        Args:
            driver:
            launch_app:

        Returns:

        '''

        objects = Step_object(driver, self.mode_value, self.mode_dict)

        objects.remote_resuce_download_fail()
        logger.info('等待tbox网络恢复')
        time.sleep(60)


    @pytest.mark.skip
    def test_bench_rescue_004(self, driver, ui_app):
        '''
        正常救援任务  - 校验ECU失败
        Args:
            driver:
            launch_app:

        Returns:

        '''
        Cloud("奇瑞欧盟").sync_rescue_task([26012])
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.remote_resuce_check_ecu_fail()

    @pytest.mark.skip
    def test_bench_rescue_005(self, driver, ui_app):
        '''
        正常救援任务  - 升级失败
        Args:
            driver:
            launch_app:

        Returns:
        '''
        task = Cloud("奇瑞欧盟")
        task.sync_rescue_task([26013])
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.remote_resuce_upgrade_fail()
        task.close_rescue_task([26013])
