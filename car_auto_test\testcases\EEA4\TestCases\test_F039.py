# coding: utf-8
# Project：car_auto_test
# File：test_F039.py
# Author：杨郑健
# Date ：2025/6/24 16:42
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from utils.AdbPort.Adb_Port import adb
from config.Config import *
from time import sleep, time
import pytest

@AllureDecorators.epic("升级结果")
@AllureDecorators.title("“升级失败，车辆已恢复至升级前状态”不点击“确定”")
def test_F039(driver, launch_app):
    """
    测试步骤
    1、篡改ecu升级包重新上传至后台
    2、“更新进行中”待升级完成
    3、“升级失败，车辆已恢复至升级前状态”不点击“确定”，车辆休眠后上电

    预期结果
    3、车端页面继续弹框显示“升级失败，车辆已恢复至升级前状态”，“请联系售后检测您的车辆”，“确定”
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        "tbox_status": "NOTREAD",
        "power": ["READY", "OFF"],
        "task": "normal",
        "gear": "P",
        "handbrake": "OFF"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'READY':
            objects.get_task()
            logger.info("READY挡验证完成")
            assert True
        elif current_state == 'OFF':
            logger.info("OFF挡升级失败显示，查看埋点数据")
            objects.rollback_success()
            serial_tool.reboot()
            result = False
            start_time = time()
            while True:
                if (time() - start_time) > 60:
                    break
                if objects.check_element_exists("success_btn"):
                    result = True
                    break
                else:
                    sleep(5)
            assert result
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()