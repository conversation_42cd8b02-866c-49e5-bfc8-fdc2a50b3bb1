# coding: utf-8
# Project：car_auto_test
# File：test_C009.py
# Author：杨郑健
# Date ：2025/6/11 17:47
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级主页面/小红点")
@AllureDecorators.title("发行说明：替换发行说明")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_C009(driver, launch_app):
    """
    测试步骤
    1、替换发行说明检测到升级任务并下载完成
    2、检查车机中的发行说明和云端配置的发行说明是否一致，比对两个发行说明的hash值

    预期结果
    2、上个升级任务的“发行说明”被替换，显示新任务的“发行说明”，内容与后台配置的一致
    """
    objects = Step_object(driver)
    objects.check_home_button_nothing()
    car_hash = objects.get_release_note()
    cloud_hash = Cloud("奇瑞汽车").get_curr_release_note()
    assert car_hash == cloud_hash