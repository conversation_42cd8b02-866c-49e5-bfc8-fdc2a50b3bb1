Qg0NCgAAAABaooBokQQAAOMAAAAAAAAAAAAAAAADAAAAQAAAAHMeAAAAZABaAGQBZAJsAVoBRwBkA2QEhABkBIMCWgJkAlMAKQV1EQAAAEZsYXNr6YWN572u5paH5Lu26QAAAABOYwAAAAAAAAAAAAAAAA8AAABAAAAAc5QAAABlAFoBZABaAmQBWgNkAloEZANaBWUGagegCGUGagegCGUGagegCWUKoQGhAaEBWgtlBmoHoAxlC2QCoQJaDWUGagegDGULZAShAloOZQZqD2UOZAVkBo0CAQBkB1oQZAhaEWQJWhJkBVoTaQBaFGQKZwBkC2QMZAxkCmQKZAdkDGQMZAxkDGQMZAxkDZwOWhVkClMAKQ7aCUFQSUNvbmZpZ3oPeW91ci1zZWNyZXQta2V5WgZzdGF0aWNaCXRlbXBsYXRlc9oEdGVtcFQpAdoIZXhpc3Rfb2tGegcwLjAuMC4waYgTAABO2gRpZGxl2gApDloMY3VycmVudF90ZXN0WgtzY3JlZW5zaG90c1oLdGVzdF9zdGF0dXNaEGN1cnJlbnRfbGFuZ3VhZ2VaDGN1cnJlbnRfbW9kZdoKc3RhcnRfdGltZdoIZW5kX3RpbWVaEWlzX3Rlc3RfY29tcGxldGVk2gN2aW5aBXRhc2sxWgV0YXNrMloFdGFzazNaBXRhc2s02ghwYXNzd29yZCkW2ghfX25hbWVfX9oKX19tb2R1bGVfX9oMX19xdWFsbmFtZV9fWgpTRUNSRVRfS0VZWg1TVEFUSUNfRk9MREVSWg9URU1QTEFURV9GT0xERVLaAm9z2gRwYXRo2gdkaXJuYW1l2gdhYnNwYXRo2ghfX2ZpbGVfX9oIQkFTRV9ESVLaBGpvaW5aD0lNQUdFX0JBU0VfUEFUSFoIVEVNUF9ESVLaCG1ha2VkaXJz2gVERUJVR9oESE9TVNoEUE9SVNoIVEhSRUFERURaC2Nsb3VkX3Rhc2tz2g90ZXN0X2RhdGFfc3RvcmWpAHIbAAAAchsAAAD6SEM6XFVzZXJzXGNhcm90YS51c2VyXERlc2t0b3Bcd3VodWNvZGVcY2FyX2F1dG9fdGVzdFxjb25maWdcYXBpX2NvbmZpZy5weXICAAAABQAAAHM0AAAACAIEAQQBBAMcAQ4CDgMOAwQBBAEEAQQDBAQCAQIBAgECAQIBAgECAQIBAgECAQIBAgECAXICAAAAKQPaB19fZG9jX19yDgAAAHICAAAAchsAAAByGwAAAHIbAAAAchwAAADaCDxtb2R1bGU+AQAAAHMEAAAABAIIAg==