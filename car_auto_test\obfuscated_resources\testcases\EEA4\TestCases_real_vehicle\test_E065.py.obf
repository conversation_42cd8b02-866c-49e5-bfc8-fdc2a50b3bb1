# coding: utf-8
# Project：car_auto_test
# File：test_E065.py
# Author：杨郑健
# Date ：2025/6/20 15:26
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("数据埋点")
@AllureDecorators.title("预约时间到，车辆未在off档")
@static_condition(conditions=
{
        "power": "READY",
        "tbox_status": "NOTREAD",
        "task": "normal"
}
)
def test_E065(driver, launch_app):
    """
    测试步骤
    1、进入“升级主页面”，“预约时间设置”设置预约升级
    2、车辆充电状态，待预约时间到，进入“预约时间到，车辆不满足升级条件，处于充电状态”，查看后台埋点数据

    预期结果
    2、（code80）“预约升级时间到，条件不满足显示”
    """
    objects = Step_object(driver)
    objects.get_task()
    serial_tool.set_file("vehicle_lock_door_status", 0, "anti_theft_status", 0)
    objects.tomorrow_time_set_next_pass()
    objects.tomorrow_predition_fail()

    burying_points = Cloud("奇瑞汽车").get_burying_point()
    result = False
    for burying_point in burying_points:
        if burying_point["msg"] == '(code 80) 预约升级时间到，条件不满足显示':
            result = True
    assert result