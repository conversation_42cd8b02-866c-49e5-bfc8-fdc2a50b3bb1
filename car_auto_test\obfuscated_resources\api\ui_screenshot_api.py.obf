'''UI截图测试API'''
import os
from datetime import datetime
from config.api_config import APIConfig
from flask import Blueprint, jsonify, request, send_from_directory, abort
from utils.LoggingSystem.Logger import logger

screenshot_bp = Blueprint('screenshot', __name__)

# 配置图片文件路由，映射到实际的图片目录
@screenshot_bp.route('/static/images/<path:filename>')
def image_files(filename):
    """处理图片文件请求，映射到实际的图片目录"""
    try:
        # 构建实际的图片路径
        ui_dir = os.path.join(APIConfig.BASE_DIR, "static", "images")
        image_path = os.path.join(ui_dir, filename.replace('/', os.sep))

        # 检查文件是否存在
        if os.path.exists(image_path):
            return send_from_directory(os.path.dirname(image_path), os.path.basename(image_path))

        logger.info(f"图片文件不存在: {image_path}")
        abort(404)
    except Exception as e:
        logger.info(f"处理图片请求时出错: {str(e)}")
        abort(500)



@screenshot_bp.route('/api/add_screenshot', methods=['POST'])
def add_screenshot():
    """添加截图数据（由测试用例调用）"""
    data = request.json
    # 获取图片路径
    image_path = data.get("image_path", "")

    # 如果路径是绝对路径，转换为相对路径
    if os.path.isabs(image_path) and APIConfig.IMAGE_BASE_PATH in image_path:
        # 提取 Image_ui 之后的相对路径
        relative_path = image_path.split('Image_ui')[-1].replace('\\', '/')
        # 确保路径以 /static/images 开头
        if not relative_path.startswith('/'):
            relative_path = '/' + relative_path
        image_path = '/static/images' + relative_path

    screenshot_data = {
        "id": len(APIConfig.test_data_store["screenshots"]) + 1,
        "language": data.get("language", ""),
        "mode": data.get("mode", ""),
        "scenario": data.get("scenario", ""),
        "image_name": data.get("image_name", ""),
        "image_path": image_path,  # 使用转换后的路径
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "status": "success"
    }

    APIConfig.test_data_store["screenshots"].append(screenshot_data)
    APIConfig.test_data_store["current_language"] = data.get("language", "")
    APIConfig.test_data_store["current_mode"] = data.get("mode", "")

    return jsonify({"status": "success"})


