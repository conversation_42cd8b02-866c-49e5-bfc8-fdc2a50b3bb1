elements = {
    # 主页面
    "home":{
        'classname':"android.widget.FrameLayout"
    },
    # 历史版本
    "history": {
        'resourceid': "com.carota.chery.v5:id/tv_version_list"
    },
    # 历史版本标题
    "history_title": {
        'resourceid': "com.carota.chery.v5:id/tv_title"
    },

    "1":{
        "resourceid": "com.carota.chery.v5:id/tv_one"
    },
    "2": {
        "resourceid": "com.carota.chery.v5:id/tv_two"
    },
    "3": {
        "resourceid": "com.carota.chery.v5:id/tv_three"
    },
    "4": {
        "resourceid": "com.carota.chery.v5:id/tv_fore"
    },
    "5": {
        "resourceid": "com.carota.chery.v5:id/tv_five"
    },
    "6": {
        "resourceid": "com.carota.chery.v5:id/tv_six"
    },
    "7": {
        "resourceid": "com.carota.chery.v5:id/tv_seven"
    },
    "8": {
        "resourceid": "com.carota.chery.v5:id/tv_eight"
    },
    "9": {
        "resourceid": "com.carota.chery.v5:id/tv_nine"
    },
    "0": {
        "resourceid": "com.carota.chery.v5:id/tv_zero"
    },


    # 当前系统已是最新版本
    "engineering_mode": {
        "resourceid": "com.carota.chery.v5:id/tv_new_version"
    },

    # 工程模式密码返回按钮
    "engineering_iv_close": {
        "resourceid": "com.carota.chery.v5:id/iv_close"
    },

    "new_version_title":{
        "resourceid": "com.carota.chery.v5:id/tv_msg"
    },

    # 密码确定按钮点击
    "password_confirm": {
        "resourceid": "com.carota.chery.v5:id/tv_enter"
            },
    # 检查更新
    "check_update": {
        "resourceid": "com.carota.chery.v5:id/btn_normal_check"
    },

    # 救援任務
    "Remote_rescue": {
        "resourceid": "com.carota.chery.v5:id/btn_rescue_check"
    },


    #清除数据
    "clear_data":{
        "resourceid": "com.carota.chery.v5:id/btn_clean_data"
    },
    # 返回按钮
    "back_button": {
        "resourceid": "com.carota.chery.v5:id/tv_update_title"
        },
    # 立即升级按钮
    "now_btn":{
        "resourceid": "com.carota.chery.v5:id/bt_upgrade_now"
    },
    # 预约升级按钮
    "schedule_btn":{
        "resourceid": "com.carota.chery.v5:id/bt_schedule"

    },
    # 预约时间设置标题
"schedule_time_set_title":{
        "resourceid": "com.carota.chery.v5:id/schedule_tv_title"
    },
    # 预约时间设置确定
"schedule_time_set_agree_btn":{
        "resourceid": "com.carota.chery.v5:id/schedule_btn_check"
    },
# 预约时间设置取消
"schedule_time_set_cancel_btn":{
        "resourceid": "com.carota.chery.v5:id/schedule_btn_cancel"
    },

# 预约中......
    "schedule_time_setting":{
        "resourceid": "com.carota.chery.v5:id/v_schedule_ing"
    },
# 预约成功标题
    "schedule_time_set_pass_title":{
        "resourceid": "com.carota.chery.v5:id/tv_content_suc"
    },
# 预约成功按钮
    "schedule_time_set_pass_btn":{
        "resourceid": "com.carota.chery.v5:id/btn_confirm"
    },

# 预约前置不满足弹标题
    "schedule_predition_fail_title": {
        "resourceid": "com.carota.chery.v5:id/tv_fail_content_line2"
    },

# 预约前置不满足弹框确定按钮
    "schedule_predition_fail_btn": {
        "resourceid": "com.carota.chery.v5:id/btn_confirm"
    },



# 预约失败
    "schedule_time_set_fail_toast":{
        "resourceid": "com.carota.chery.v5:id/tv_schedule_ing"
    },
    # 主页面取消预约按钮
    "schedule_cancel_btn": {
        "resourceid": "com.carota.chery.v5:id/bt_schedule"
    },
    # 预约设置取消预约
    "schedule_setting_cancel_btn": {
        "resourceid": "com.carota.chery.v5:id/schedule_btn_cancel"
    },

    # 取消预约的取消按钮
    "schedule_cancel_btn_cancel": {
        "resourceid": "com.carota.chery.v5:id/schedule_btn_cancel"
    },


    # 是否取消当前预约
    "schedule_cancel_ask": {
        "resourceid": "com.carota.chery.v5:id/sc_txt_loading"
    },
    # 是否取消当前预约确定按钮
    "schedule_cancel_ask_agreen_btn": {
        "resourceid": "com.carota.chery.v5:id/btn_confirm"
    },
    # 取消预约中
    "schedule_cancelling": {
        "resourceid": "com.carota.chery.v5:id/tv_canceling_small"
    },

    # 取消预约失败，请重试toast
    "schedule_cancel_fail_please_try": {
        "resourceid": "com.carota.chery.v5:id/tv_canceling_big"
    },

    # 取消预约成功按钮
    "schedule_cancelling_btn": {
        "resourceid": "com.carota.chery.v5:id/btn_confirm"
    },
    # 取消预约成功标题
    "schedule_cancelling_title": {
        "resourceid": "com.carota.chery.v5:id/tv_content"
    },

    # 预约时间到条件不满足标题
    "schedule_time_no_ok_title": {
        "resourceid": "com.carota.chery.v5:id/tv_fail_content_line1"
    },

# 先设置预约升级、预约充电冲突toast
    "Upgrade_charging_conflict_toast":{
        "resourceid":"com.mega.phev:id/tv_tip"
    },
# 预约充电确定
"Upgrade_charging_conflict_confim":{
        "resourceid":"com.mega.phev:id/tv_sure"
    },
    # 预约充电取消
"Upgrade_charging_conflict_cancel":{
        "resourceid":"com.mega.phev:id/tv_cancel"
    },
# 先设置预约充电、预约升级冲突toast

    "Upgrade_travel_conflict_toast":{
"resourceid":"com.carota.chery.v5:id/tv_schedule_ing"
    },


# 预约充电
    "mega_charging":{
        "resourceid":"android:id/summary"
    },



    # 隐私申明
    "privacy_Statement":{
        "resourceid":"com.carota.chery.v5:id/ll_privacy"
    },
    #隐私协议
    "Privacy_Agreement":{
        "resourceid":"com.mega.carsettings:id/tv_title"
    },
    # 隐私协议知道了
    "privacy_ok":{
        "resourceid":"com.mega.carsettings:id/ll_bottom"
    },
    #详情按钮
    "details_btn": {
        "resourceid": "com.carota.chery.v5:id/tv_detail_show"
    },
    # 详情标题 和 返回按钮
    "details_title": {
        "resourceid": "com.carota.chery.v5:id/tv_title"
    },
    # 注意事项继续按钮
    "matter_argee_btn": {
        "resourceid": "com.carota.chery.v5:id/btn_continue"
    },
    # 注意事项取消按钮
    "matter_cancel_btn": {
        "resourceid": "com.carota.chery.v5:id/btn_cancel"
    },
    # 免责同意按钮
    "disclaimer_argee_btn": {
        "resourceid": "com.carota.chery.v5:id/btn_agree"
    },
    # 免责取消按钮
    "disclaimer_cancel_btn": {
        "resourceid": "com.carota.chery.v5:id/btn_cancel"
    },

    # 蓄电池/动力电量不足标题
    "battery_power_title": {
            "resourceid": "com.carota.chery.v5:id/battery_tv_title"
        },
#  蓄电池/动力电量不足按钮
    "battery_power_btn": {
            "resourceid": "com.carota.chery.v5:id/incomplete_btn_confirm"
        },
    # 前置条件标题
    "precondition": {
        "resourceid": "com.carota.chery.v5:id/tv_title"
    },

    # 前置不满足小标题
    "precondition_fail": {
        "resourceid": "com.carota.chery.v5:id/tv_check_result"
    },

    # 前置不满足取消按钮
    "precondition_fail_cancelbtn": {
        "resourceid": "com.carota.chery.v5:id/btn_pre_cancel"

    },

    # 前置不满足icon按钮
    "precondition_fail_icon": {
        "resourceid": "com.carota.chery.v5:id/iv_item_fail"
    },
    # 前置不满足确定按钮
    "precondition_fail_confirbtn": {
        "resourceid": "com.carota.chery.v5:id/btn_pre_confirm"
    },

    # 前置全部通过
    "precondition_pass": {
        "resourceid": "com.carota.chery.v5:id/tv_check_suc"
    },


     # 远程上电
    "power_on": {
        "resourceid": 'com.carota.chery.v5:id/tv_update_content'
    },

    # 上下电失败/进ota失败标题
    "remote_power_on_fail_title": {
        "resourceid": 'com.carota.chery.v5:id/tv_fail_content'
    },

    # 上电失败/进ota失败确定
    "remote_power_on_fail_btn": {
        "resourceid": 'com.carota.chery.v5:id/btn_confirm'
    },

    # 第二次前置检察中
    "vehicle_upgrade_checking": {
        "resourceid": 'com.carota.chery:id/tv_content'
    },

    # 第二次前置检察中
    "vehicle_upgrade_checking_fail": {
        "resourceid": 'com.carota.chery:id/car_status_txt'
    },

    #升级任务无效，车辆正在下电
    "task_inivail_poweroff": {
        "resourceid": 'com.carota.chery.v5:id/iv_error_roll'
    },

    #任务有效性校验失败标题
    "task_inivail_title": {
        "resourceid": 'com.carota.chery:id/tv_fail_content'
    },

    # 任务有效性校验失败按钮
    "task_inivail_btn": {
        "resourceid": 'com.carota.chery.v5:id/btn_confirm'
    },


    # 下电失败确定
    "remote_power_off_fail_btn": {
        "resourceid": 'com.carota.chery:id/error_btn_confirm',
    },


    # 远程救援任务说明倒计时
    "Remote_rescue_title": {
        "resourceid": "com.carota.chery.v5:id/tv_disclaimer_title"
    },


    # 远程救援任务说明按钮
    "Remote_rescue_btn": {
        "resourceid": "com.carota.chery.v5:id/btn_agree"
    },
    # 远程救援任务升级进度
    "Remote_rescue_upgrade_title": {
        "resourceid": "com.carota.chery.v5:id/tv_tittle"
    },
    # 远程救援任务升级内容
    "Remote_rescue_upgrade_text": {
        "resourceid": "com.carota.chery.v5:id/tv_content"
    },


    # 远程救援任务icon
    "Remote_rescue_icon": {
        "resourceid": "com.carota.chery.v5:id/iv_item_suc"
    },

    # 远程救援任务失败icon
    "Remote_rescue_icon_fail": {
     "resourceid": "com.carota.chery.v5:id/iv_item_fail"
    },
    # 远程救援任务確定
    "Remote_rescue_confim_btn": {
        "resourceid": "com.carota.chery.v5:id/btn_pre_confirm"
    },


    # 更新进行中
    "update_title": {
        "resourceid": "com.carota.chery.v5:id/ins_txt_title"
    },


    # 升级成功，车辆正在下电
    "upgrade_success_poweroff": {
        "resourceid": 'com.carota.chery:id/tv_fail_service_roll'
    },
    # 升级成功，结果弹框
    "upgrade_success_page": {
        "resourceid": 'com.carota.chery:id/tv_sale_helper'
    },
    # 升级结果按钮
    "upgrade_success_page_btn": {
        "resourceid": 'com.carota.chery.v5:id/btn_confirm'
    },

    'system':{
        'classname':'android.widget.LinearLayout'
    },

    'display':{
        'classname':'android.widget.RadioButton'
    },
    # 升级失败，车辆正在下电
    'upgrade_fail_poweroff':{
        "resourceid":"com.carota.chery.v5:id/iv_error_roll"
    },
    # 升级失败，结果弹框
    'upgrade_fail_page':{
        "resourceid":"com.carota.chery:id/tv_sale_helper"
    },
    # 升级失败，结果按钮
    'upgrade_fail_page_btn':{
        "resourceid":"com.carota.chery.v5:id/btn_confirm"
    }
}