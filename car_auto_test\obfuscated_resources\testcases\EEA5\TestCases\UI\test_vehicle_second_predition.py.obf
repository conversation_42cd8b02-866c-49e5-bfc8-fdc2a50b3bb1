'''
第二次前置条件相关
1、第二次前置不过
2、退ota失败
3、下电失败
'''
import time

import pytest
import functools

from testcases.EEA5.PageObjects.TestObject import Step_object
from utils.ImageRecognition.Image_comparison import *
from utils.SerialTool.vcc_tool import vcc



class Test_Vehicle_Second_Predition:
    # 使用mode fixture获取当前的模式值
    @pytest.fixture(autouse=True)
    def setup_ui_mode(self, mode, mode_dict):
        """设置当前的UI模式和mode_dict"""
        self.mode_value = mode
        self.mode_dict = mode_dict
        logger.info(f"当前测试环境: 模式值={self.mode_value}")

    # @pytest.mark.skip
    def test_vehicle_second_predition_001(self, driver, ui_app):
        """
        二次条件不过
        """
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.get_task()
        objects.second_predition_check_fail()

    # @pytest.mark.skip
    def test_vehicle_second_predition_002(self, driver):
        """
        下电失败
        """
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        # objects.get_task()
        vcc.set_file('remote_poweroff', 1)
        # 等待mda和apk通讯
        time.sleep(10)
        objects.second_predition_check_fail_powerofffail()

    # @pytest.mark.skip
    def test_vehicle_second_predition_003(self, driver):
        """
        退ota失败
        """
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        vcc.set_file('otamodeout', 1)
        # 等待mda和apk通讯
        time.sleep(10)
        objects.second_predition_check_fail_otaoutfail()
        # 恢复配置文件
        vcc.set_file('otamodeout', 0)
        # 等待mda和apk通讯
        time.sleep(10)
