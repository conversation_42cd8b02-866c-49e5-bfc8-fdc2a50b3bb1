# import time
#
# import pytest
#
# from testcases.EEA4.PageObjects.TestObject import Step_object
# from utils.ImageRecognition.Image_comparison import *
# from utils.SerialTool.serial_tool import serial_tool
#
#
# class Test_UI:
#     # 使用mode fixture获取当前的模式值
#     @pytest.fixture(autouse=True)
#     def setup_ui_mode(self, mode):
#         """设置当前的UI模式"""
#         self.mode_value = mode
#         logger.info(f"当前测试环境: 模式值={self.mode_value}")
#
#
#     def test_ui_000(self,driver):
#         objects = Step_object(driver, self.mode_value)
#         print(self.mode_value)
#         if self.mode_value == '1':
#             driver.app_start(package_name='com.desaysv.setting',activity='.ui.activity.SettingActivity')
#             objects = Step_object(driver, self.mode_value)
#             objects.click_element_classname('system')[12].click()
#             objects.click_element_classname('display')[0].click()
#
#             objects.click_element_classname('system')[11].click()
#             objects.click_element_classname('display')[1].click()
#         elif self.mode_value == '2':
#             driver.app_start(package_name='com.desaysv.setting', activity='.ui.activity.SettingActivity')
#             objects = Step_object(driver, self.mode_value)
#             objects.click_element_classname('system')[12].click()
#             objects.click_element_classname('display')[0].click()
#
#             objects.click_element_classname('system')[11].click()
#             objects.click_element_classname('display')[2].click()
#
#         elif self.mode_value == '3':
#             driver.app_start(package_name='com.desaysv.setting', activity='.ui.activity.SettingActivity')
#             objects = Step_object(driver, self.mode_value)
#             objects.click_element_classname('system')[12].click()
#             objects.click_element_classname('display')[1].click()
#
#             objects.click_element_classname('system')[11].click()
#             objects.click_element_classname('display')[1].click()
#
#         elif self.mode_value == '4':
#             driver.app_start(package_name='com.desaysv.setting', activity='.ui.activity.SettingActivity')
#             objects = Step_object(driver, self.mode_value)
#             objects.click_element_classname('system')[12].click()
#             objects.click_element_classname('display')[1].click()
#
#             objects.click_element_classname('system')[11].click()
#             objects.click_element_classname('display')[2].click()
#
#     # @pytest.mark.skip
#     def test_ui_001(self, driver, launch_app):
#         """
#         正常流程
#         """
#
#         objects = Step_object(driver,self.mode_value)
#         # 拿到升级任务
#         objects.get_task()
#         # 返回主页面截图
#         objects.back_home()
#         # 查看详情
#         # objects.details_click()
#         # 正常流程
#         objects.update_success()
#     # @pytest.mark.skip
#
#     # @pytest.mark.skip
#     def test_ui_002(self, driver, launch_app):
#         """
#         正常流程
#         """
#
#         objects = Step_object(driver,self.mode_value)
#
#         # 拿到升级任务
#         objects.get_task()
#
#         time.sleep(1)
#         # 请将车辆熄火
#         serial_tool.set_file('power_gear', 1)
#         objects.disclaimer_click()
#         objects.check_element_exists('vehicle_acc_btn')
#         time.sleep(1)
#         objects.screen_image('请将车辆熄火', self.mode_value)
#         time.sleep(1)
#         # 点击取消按钮
#         objects.click_element_id_text('vehicle_acc_btn')
#         logger.info('点击取消按钮')
#         time.sleep(1)
#         serial_tool.set_file('power_gear', 0)
#
#         time.sleep(1)
#         # 上电失败
#         serial_tool.set_file('remote_poweron', 1)
#         objects.disclaimer_click()
#         # 传递mode参数给screen_image方法
#         objects.check_element_exists('remote_power_on_fail_btn')
#         time.sleep(1)
#         objects.screen_image('上电失败', self.mode_value)
#         # 点击确定按钮
#         objects.click_element_id_text('remote_power_on_fail_btn')
#         logger.info('点击确定按钮')
#         time.sleep(1)
#         serial_tool.set_file('remote_poweron', 0)
#
#         time.sleep(1)
#         # 进入ota模式失败
#         serial_tool.set_file('otamodein', 1)
#         objects.disclaimer_click()
#         # 传递mode参数给screen_image方法
#         objects.check_element_exists('otamodein_fail_btn')
#         time.sleep(1)
#         objects.screen_image('上进入ota模式失败', self.mode_value)
#         # 点击确定按钮
#         objects.click_element_id_text('otamodein_fail_btn')
#         logger.info('点击确定按钮')
#         time.sleep(1)
#         serial_tool.set_file('otamodein', 0)
#
#         # 挡位不满足
#         serial_tool.set_file('gearbox_gear',2)
#         objects.preconditions_fail()
#         # 传递mode参数给screen_image方法
#         objects.screen_image('挡位不满足',self.mode_value)
#         # 点击不满足确定按钮
#         objects.click_element_id_text('precondition_fail_cancelbtn')
#         logger.info('点击取消按钮')
#         time.sleep(1)
#         objects.screen_image('前置不满足确定界面', self.mode_value)
#         objects.click_element_id_text('precondition_fail_confirbtn')
#         logger.info('点击确定按钮')
#         serial_tool.set_file('gearbox_gear', 1)
#
#         time.sleep(1)
#         # 手刹不满足
#         serial_tool.set_file('epb_status', 0)
#         time.sleep(1)
#         # 下电失败
#         serial_tool.set_file('remote_poweroff', 1)
#         objects.preconditions_fail()
#         # 传递mode参数给screen_image方法
#         objects.screen_image('手刹不满足',self.mode_value)
#         # 点击不满足确定按钮
#         objects.click_element_id_text('precondition_fail_cancelbtn')
#         logger.info('点击取消按钮')
#         time.sleep(1)
#         objects.click_element_id_text('precondition_fail_confirbtn')
#         logger.info('点击确定按钮')
#         serial_tool.set_file('epb_status', 1)
#         objects.check_element_exists('remote_power_off_fail_btn')
#         objects.screen_image('下电失败', self.mode_value)
#         time.sleep(1)
#         logger.info('点击确定按钮')
#         objects.click_element_id_text('remote_power_off_fail_btn')
#
#         serial_tool.set_file('remote_poweroff', 0)
#
#         time.sleep(1)
#         # 引擎不满足
#         serial_tool.set_file('pt_ready', 1)
#         time.sleep(1)
#         # 退ota失败
#         serial_tool.set_file('otamodeout', 1)
#         objects.preconditions_fail()
#         # 传递mode参数给screen_image方法
#         objects.screen_image('引擎不满足',self.mode_value)
#         # 点击不满足确定按钮
#         objects.click_element_id_text('precondition_fail_cancelbtn')
#         logger.info('点击取消按钮')
#         time.sleep(1)
#         objects.click_element_id_text('precondition_fail_confirbtn')
#         logger.info('点击确定按钮')
#         serial_tool.set_file('pt_ready', 0)
#         objects.check_element_exists('otamodeout_fail_btn')
#         objects.screen_image('升级模式退出失败', self.mode_value)
#         time.sleep(1)
#         serial_tool.set_file('otamodeout', 0)
#
#
#
#
#
#     # def test_ui_003(self,driver,launch_app):
#     #     logger.info(self.mode_value)
#     #     logger.info('执行第三条用例')
#     #     objects = Step_object(driver)
#     #     objects.screen_image('测试',self.mode_value)
#
#     #
#     # def test_ui_004(self,driver,launch_app):
#     #     logger.info(self.mode_value)
#     #     logger.info('执行第四条用例')
#     #     objects = Step_object(driver)
#     #     objects.screen_image('测试啊',self.mode_value)
#     #
#     #
#     #
#     #
