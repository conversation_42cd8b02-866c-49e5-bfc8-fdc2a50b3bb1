"""
封装u2 里面中关于元素对象的方法
元素方法名称定义：
        find.* 寻找元素
        click.* 点击元素
        check.* 检查元素
        get.* 获取元素
"""
import time
from config.Config import *
from utils.LoggingSystem.Logger import logger
from testcases.EEA4.PageObjects.Elements import elements
from utils.ImageRecognition.Image_comparison import image_recognition
from utils.AdbPort.Adb_Port import adb
from utils.SerialTool.actuator import act


class BasePage(object):
    '''基类'''

    def __init__(self, driver):
        """
        初始化，数据，需要传入device
        """
        self.driver = driver
        self.log = logger
        self.mode_dict = None  # 添加mode_dict属性

    def wait_for_element(self, element, max_wait_time=15, wait_interval=1):
        """
        等待元素出现
        Args:
            element: 元素对象
            max_wait_time: 最大等待时间（秒）
            wait_interval: 检查间隔（秒）
        Returns:
            bool: 元素是否出现
        """

        try:
            # 尝试获取元素的详细信息
            element_info = ""
            try:
                # 尝试获取常见属性
                if hasattr(element, 'selector'):
                    element_info = f"选择器={element.selector}"
                elif hasattr(element, 'info'):
                    # 获取元素信息
                    info = element.info
                    if 'resourceId' in info:
                        element_info += f" resourceId={info['resourceId']}"
                    if 'text' in info:
                        element_info += f" text={info['text']}"
                    if 'className' in info:
                        element_info += f" className={info['className']}"
            except:
                # 如果无法获取详细信息，则使用简单的字符串表示
                element_info = str(element)
        except:
            element_info = str(element)  # 备用显示方式

        elapsed_time = 0
        while elapsed_time < max_wait_time:
            if element.exists:
                return True
            time.sleep(wait_interval)
            elapsed_time += wait_interval
        # self.log.error(f"等待{max_wait_time}秒后仍未找到元素: {element_info}")
        return False

    def set_mode_dict(self, mode_dict):
        """
        设置mode_dict
        """
        self.mode_dict = mode_dict

    def screen_image(self, name, mode=0):
        """
        截取当前屏幕并保存
        """
        time.sleep(1)
        logger.info(f'截图:{name}')
        # 确保文件名有.png后缀
        if not name.lower().endswith('.png'):
            filename = f"{name}.png"
        else:
            filename = name
        if self.mode_dict and mode in self.mode_dict:
            # 统一保存到 static/images 目录下
            # 获取 mode_dict 路径的最后两级（如 T22/day_法语（法国））
            image_subdir = os.path.normpath(self.mode_dict[str(mode)])
            # 提取 T22/day_法语（法国）
            parts = image_subdir.split(os.sep)
            if len(parts) >= 2:
                sub_path = os.path.join(parts[-2], parts[-1])
            else:
                sub_path = parts[-1]
            # static/images/T22/day_法语（法国）
            image_dir = os.path.join(os.getcwd(), 'static', 'images', sub_path)
        else:
            logger.error(f"无法获取mode={mode}的路径，mode_dict可能未设置")
            return None

        # 创建目录（如果不存在）
        os.makedirs(image_dir, exist_ok=True)
        # 完整的保存路径
        save_path = os.path.join(image_dir, filename)

        # 截图并保存
        screenshot = self.driver.screenshot()
        screenshot.save(save_path)
        logger.info(f"截图已保存到: {save_path}, 使用mode: {mode}")

        # 发送截图数据到Flask应用
        self.send_screenshot_to_flask(name, save_path, mode)

        return save_path

    def parse_mode_and_language(self, path):
        """路径解析"""
        # 标准化路径并提取末级目录名
        normalized_path = os.path.normpath(path)
        folder_name = os.path.basename(normalized_path)

        # 分割名称和语言
        if '_' in folder_name:
            # 限制分割次数为1次，防止模式名中带下划线的情况
            mode_part, lang_part = folder_name.split('_', 1)
            return mode_part.strip(), lang_part.strip()
        return folder_name.strip(), None  # 默认语言为None

    def send_screenshot_to_flask(self, scenario_name, image_path, mode):
        """
        发送截图数据到Flask应用
        """
        try:
            import requests
            from datetime import datetime

            # 从mode_dict中解析语言和模式信息
            language = "未知"
            mode_name = "未知"

            if hasattr(self, 'mode_dict') and self.mode_dict and str(mode) in self.mode_dict:
                path = self.mode_dict[str(mode)]
                mode_name, language = self.parse_mode_and_language(path)

            # 统一 web 访问路径为 /static/images/T22/day_法语（法国）/xxx.png
            # 提取 static/images 后的部分
            abs_image_path = os.path.abspath(image_path)
            static_images_dir = os.path.abspath(os.path.join(os.getcwd(), 'static', 'images'))
            if abs_image_path.startswith(static_images_dir):
                web_path = '/static/images' + abs_image_path[len(static_images_dir):].replace('\\', '/').replace('\\',
                                                                                                                 '/')
            else:
                web_path = f"/static/images/{mode_name}_{language}/{os.path.basename(image_path)}"

            # 确保 web_path 以 / 开头
            if not web_path.startswith('/'):
                web_path = '/' + web_path

            # 准备发送的数据
            screenshot_data = {
                "language": language,
                "mode": mode_name,
                "scenario": scenario_name,
                "image_name": os.path.basename(image_path),
                "image_path": web_path
            }
            # 发送POST请求到Flask应用
            response = requests.post(
                'http://localhost:5000/api/add_screenshot',
                json=screenshot_data,
                timeout=5
            )

            if response.status_code == 200:
                pass
                # logger.info(f"截图数据已发送到Flask应用: {scenario_name}")
            else:
                logger.warning(f"发送截图数据失败: {response.status_code}")

        except Exception as e:
            logger.warning(f"发送截图数据到Flask应用时出错: {str(e)}")
            # 不影响主要功能，只记录警告

    def get_release_note(self):
        """获取车机中的发行说明
        Returns: hash值
        """
        adb0 = 'adb shell find /data -name releasenote.html'
        adb1 = 'adb shell find /sdcard -name releasenote.html'
        release_note_path = adb.execute(adb0).split("\n")[0]
        logger.info(release_note_path)
        if not release_note_path:
            release_note_path = adb.execute(adb1).split("\n")[0]
        adb2 = f'adb shell md5sum {release_note_path}'
        hash = adb.execute(adb2)[:32]
        return hash

    def find_element(self, element_name, max_wait_time=15):
        """ 获取并返回元素对象
        Args:
            element_name: 元素名称

        Returns: 元素对象
        """
        element_info = elements.get(element_name)
        try:
            if element_info.rfind(":id") != -1:
                element = self.driver(resourceId=element_info)
            else:
                element = self.driver(className=element_info)
            if self.wait_for_element(element, max_wait_time=max_wait_time):
                return element
        except Exception as e:
            logger.error(f"获取元素出错:{e}")
            return False

    def find_element_by_text(self, text_name, index=None, max_wait_time=15):
        """获取文本元素并返回元素对象
            text_name: 文本
        Returns: bool
        """
        try:
            if index:
                element = self.driver(text=text_name, index=index)
            else:
                element = self.driver(text=text_name)
            if self.wait_for_element(element, max_wait_time=max_wait_time):
                return element
        except Exception as e:
            logger.error(f"获取元素出错:{e}")
            return False

    def find_textContains_by_text(self, textContains):
        """查找当前页面中有没有出现指定关键字
        Returns: True/False
        """
        return self.driver(textContains=textContains).exists

    def click_element_by_text(self, text_name, max_wait_time=15):
        """点击文本
            text_name: 文本名称
        Returns: bool
        """
        element_info = elements.get(text_name)
        try:
            element = self.find_element_by_text(element_info)
            if self.wait_for_element(element, max_wait_time=max_wait_time):
                element.click()
        except Exception as e:
            logger.error(f'检查元素 {text_name} 出错: {str(e)}')

    def get_element_id(self, element_name: str, ignore=False, max_wait_time=15) -> bool:
        """
        点击元素对象
        Args:
            element_name: 需要获取的对象名称

        Returns:
            bool:
        """
        element_info = elements.get(element_name)
        try:
            element_id = self.driver(resourceId=element_info['resourceid'])
            if self.wait_for_element(element_id, max_wait_time=max_wait_time):
                # logger.info(f"通过ID找到元素: {element_info['resourceid']}")
                return element_id
            if ignore == False:
                logger.error(
                    f'未找到元素、无法点击: {element_name}, resourceid: {element_info["resourceid"]}, text: {element_info["text"]}')
            return False
        except Exception as e:
            logger.error(f'检查元素 {element_name} 出错: {str(e)}')
            return False

    def get_info_by_text(self, text_name, max_wait_time=15):
        """获取文本并返回元素信息
            text_name: 文本

        Returns:  info 元素信息
        """
        element_info = elements.get(text_name)
        try:
            element = self.find_element_by_text(element_info)
            if self.wait_for_element(element, max_wait_time=max_wait_time):
                return element.info
        except Exception as e:
            logger.error(f'检查元素 {text_name} 出错: {str(e)}')

    def click_element(self, element_name: str, index=None, max_wait_time=15) -> bool:
        """
        点击元素对象
        Args:
            element_name: 需要获取的对象名称
            index: 索引，在使用className时必须带上
        Returns:
            bool: 元素是否存在，如果通过resourceId和className都找不到，则返回False
        """
        if act.manufacture == 1:
            adb.execute("adb shell input keyevent KEYCODE_WAKEUP")
        element_info = elements.get(element_name)
        try:
            if element_info.rfind(":id") != -1:
                element = self.driver(resourceId=element_info)
            else:
                element = self.driver(className=element_info, index=index)
            if self.wait_for_element(element, max_wait_time=max_wait_time):
                logger.info(f"找到元素: {element_info}")
                element.click()
                return True
            logger.info(
                f'未找到元素: {element_name}')
            return False
        except Exception as e:
            logger.error(f'检查元素 {element_name} 出错: {str(e)}')
            return False

    def get_element_info(self, element_name: str, index=None, max_wait_time=15) -> bool:
        """
        点击元素对象
        Args:
            element_name: 需要获取的对象名称
            index: 索引，在使用className时必须带上
        Returns:
            bool: 元素info
        """
        element_info = elements.get(element_name)
        try:
            if element_info.rfind(":id") != -1:
                element = self.driver(resourceId=element_info)
            else:
                element = self.driver(className=element_info, index=index)
            if self.wait_for_element(element, max_wait_time=max_wait_time):
                logger.info(f"找到元素: {element_info}")
                return element.info
            logger.info(
                f'未找到元素: {element_name}')
            return False
        except Exception as e:
            logger.error(f'检查元素 {element_name} 出错: {str(e)}')
            return False

    def get_element_enabled(self, element_name: str, index=None, max_wait_time=15) -> bool:
        """
        获取元素对象enabled
        Args:
            element_name: 需要获取的对象名称
            index: 索引，在使用className时必须带上
        Returns:
            bool: 元素是否存在，如果通过resourceId和text和className都找不到，则返回False
        """
        element_info = elements.get(element_name)
        try:
            if element_info.rfind(":id") != -1:
                element = self.driver(resourceId=element_info)
            else:
                element = self.driver(className=element_info, index=index)
            if self.wait_for_element(element, max_wait_time=max_wait_time):
                logger.info(f"通过ID找到元素: {element_info['resourceid']}")
                # element_id.click()
                return element.info["enabled"]
            logger.info(
                f'未找到元素: {element_name}')
            return False
        except Exception as e:
            logger.error(f'检查元素 {element_name} 出错: {str(e)}')
            return False

    def send_element(self, element_name: str, send_text, index=None, max_wait_time=15):
        """
        发送文本
        Args:
            element_name:需要检查的元素名称
            index: 索引，在使用className时必须带上
        Returns:
            bool: 元素是否存在，如果通过resourceId和text都找不到，则返回False
        """

        element_info = elements.get(element_name)
        try:
            if element_info.rfind(":id") != -1:
                element = self.driver(resourceId=element_info)
            else:
                element = self.driver(className=element_info, index=index)
            if self.wait_for_element(element, max_wait_time=max_wait_time):
                element.send_keys(send_text)
                logger.info(f"写入文本: {send_text}")
                # return True

            logger.info(
                f'未找到元素: {element_name}')
        except Exception as e:
            logger.error('检查元素出错，未找到这个元素:', {e})
            # return False

    def check_element_text_exists(self, element_name: str) -> bool:
        """
        检查元素文本是否存在 针对只有文本的元素
        Args:
            element_name: 需要检查的元素名称

        Returns:
            bool: 返回元素的名称
        """
        try:
            self.driver(text=element_name)
            return True
        except Exception as e:
            logger.error('检查元素出错，未找到这个元素:', {e})
            return False

    # UI 截图不能用
    def check_element_text(self, element_name: str, max_wait_time=15) -> str:
        """
        检查元素文本内容
        Args:
            element_name: 需要检查的元素名称

        Returns:
            str: 返回元素的文本内容
        """
        element_info = elements.get(element_name)
        try:
            element_text = self.driver(text=element_info['text'])
            if self.wait_for_element(element_text, max_wait_time=max_wait_time):
                return element_text.get_text()
            return ""
        except Exception as e:
            logger.error('检查元素出错，未找到这个元素:', {e})
            return ""

    def get_element_text_by_id(self, element_name: str, max_wait_time=15) -> str:
        """
        通过元素ID获取元素文本内容
        Args:
            element_name: 需要获取文本的元素名称
            max_wait_time: 最大等待时间

        Returns:
            str: 返回元素的文本内容
        """
        element_info = elements.get(element_name)
        try:
            if element_info.rfind(":id") != -1:
                element = self.driver(resourceId=element_info)
            else:
                element = self.driver(className=element_info)

            if self.wait_for_element(element, max_wait_time=max_wait_time):
                text_content = element.get_text()
                logger.info(f"获取到元素 {element_name} 的文本内容: {text_content}")
                return text_content
            else:
                logger.warning(f"等待 {max_wait_time} 秒后未找到元素: {element_name}")
                return ""
        except Exception as e:
            logger.error(f'获取元素 {element_name} 文本内容时出错: {str(e)}')
            return ""

    def check_element_exists(self, element_name: str, index=None, max_wait_time=15) -> bool:
        """
        检查元素是否存在
        Args:
            element_name: 需要检查的元素名称

        Returns:
            bool: 元素是否存在，如果通过resourceId和text都找不到，则返回False
        """
        if act.manufacture == 1:
            adb.execute("adb shell input keyevent KEYCODE_WAKEUP")
        element_info = elements.get(element_name)
        try:
            if element_info.rfind(":id") != -1:
                element = self.driver(resourceId=element_info)
            else:
                element = self.driver(className=element_info, index=index)
            if self.wait_for_element(element, max_wait_time=max_wait_time):
                logger.info(f'检查到元素出现:{element_info}')
                return self.wait_for_element(element)
        except Exception as e:
            logger.error('检查元素出错，未找到这个元素:', {e})
            return False

    def check_element_clickable(self, element_name: str, index=None, max_wait_time=15) -> bool:
        """
        检查元素是否可点击
        Args:
            element_name: 需要检查的元素名称

        Returns:
            bool: 元素是否可点击，如果元素不存在或不可点击则返回False
        """

        element_info = elements.get(element_name)
        try:
            if element_info.rfind(":id") != -1:
                element = self.driver(resourceId=element_info)
            else:
                element = self.driver(className=element_info, index=index)

            if self.wait_for_element(element, max_wait_time=max_wait_time):
                enable = element.info.get('enabled')
                logger.info(f'找到元素的可点击属性:{enable}')
                return enable

            logger.info(
                f'未找到元素: {element_name}')
            return False
        except Exception as e:
            logger.error(f'检查元素 {element_name} 出错: {str(e)}')
            return False

    def check_mark(self):
        """
        获取前置条件icon图标
        Returns:

        """
        # 重试机制，直到获取到非加载状态的图片
        max_retries = 20  # 最大重试次数
        retry_interval = 1.5  # 重试间隔（秒）
        for attempt in range(max_retries):
            # 获取图片元素
            mark_element = self.driver(resourceId='com.carota.chery:id/item_iv_pc')[0]
            mark_image = mark_element.screenshot()

            # 保存图片到本地
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            save_path = os.path.join(save_dir, f'screenshot_{timestamp}.png')
            mark_image.save(save_path)

            # 识别图片内容
            is_x_mark = image_recognition.recognize_mark(mark_image)
            # logger.info(f"当前挡位下图像识别结果: {is_x_mark}")
            if is_x_mark is not None:  # 假设recognize_mark返回None表示加载状态
                break
            time.sleep(retry_interval)
        # 如果达到最大重试次数仍然在加载状态，记录错误
        if is_x_mark is None:
            logger.error("达到最大重试次数，图片仍处于加载状态")
            assert False, "图片一直处于加载状态，无法进行识别"

        return is_x_mark

    def swipe_down(self, start_percent=0.01, end_percent=0.8, duration=0.5, steps=10):
        """
        从窗口上方滑动到下方

        Args:
            start_percent: 起始位置占屏幕高度的百分比，默认为0.01（屏幕上方1%处）
            end_percent: 结束位置占屏幕高度的百分比，默认为0.8（屏幕下方80%处）
            duration: 滑动持续时间（秒），默认为0.5秒
            steps: 滑动过程中的步数，默认为10步

        Returns:
            bool: 滑动是否成功
        """
        try:
            # 获取屏幕尺寸
            window_size = self.driver.window_size()
            width = window_size[0]
            height = window_size[1]

            # 计算滑动的起始和结束坐标
            start_x = width * 0.5  # 水平中心
            start_y = height * start_percent
            end_x = width * 0.5  # 水平中心
            end_y = height * end_percent

            # 执行滑动
            logger.info(f"从屏幕上方({start_percent * 100}%)滑动到下方({end_percent * 100}%)")
            self.driver.swipe(start_x, start_y, end_x, end_y, duration=duration * 1000, steps=steps)
            logger.info("滑动成功")
            return True
        except Exception as e:
            logger.error(f"滑动操作失败: {str(e)}")
            return False

    def swipe_up(self, start_percent=0.8, end_percent=0.2, duration=0.5, steps=10):
        """
        从窗口下方滑动到上方

        Args:
            start_percent: 起始位置占屏幕高度的百分比，默认为0.8（屏幕下方80%处）
            end_percent: 结束位置占屏幕高度的百分比，默认为0.2（屏幕上方20%处）
            duration: 滑动持续时间（秒），默认为0.5秒
            steps: 滑动过程中的步数，默认为10步

        Returns:
            bool: 滑动是否成功
        """
        # 调用swipe_down方法，但交换起始和结束百分比
        return self.swipe_down(start_percent=start_percent, end_percent=end_percent, duration=duration, steps=steps)

    def smart_click_elements(self, element_list, max_wait_time=15):
        """
        智能点击多个元素，只要有一个成功就停止
        Args:
            element_list: 元素列表，可以是字符串列表或字典列表
            max_wait_time: 最大等待时间
        Returns:
            bool: 是否有元素点击成功
        """
        logger.info(f"开始智能点击，尝试元素: {element_list}")

        for i, element_info in enumerate(element_list):
            try:
                if isinstance(element_info, str):
                    # 直接是元素名称
                    success = self.click_element(element_info, max_wait_time=max_wait_time)
                    if success:
                        logger.info(f"元素 {element_info} 点击成功，停止尝试其他元素")
                        return True
                    else:
                        logger.info(f"元素 {element_info} 点击失败，尝试下一个")

                elif isinstance(element_info, dict):
                    # 字典格式，包含元素名称和索引
                    element_name = element_info.get('name')
                    index = element_info.get('index')
                    max_wait = element_info.get('max_wait_time', max_wait_time)

                    success = self.click_element(element_name, index=index, max_wait_time=max_wait)
                    if success:
                        logger.info(f"元素 {element_name} (索引: {index}) 点击成功，停止尝试其他元素")
                        return True
                    else:
                        logger.info(f"元素 {element_name} (索引: {index}) 点击失败，尝试下一个")

            except Exception as e:
                logger.error(f"尝试点击第 {i + 1} 个元素时出错: {str(e)}")
                continue

        logger.warning("所有元素都点击失败")
        return False

    def smart_click_with_fallback(self, primary_elements, fallback_elements=None, max_wait_time=15):
        """
        带备用方案的智能点击
        Args:
            primary_elements: 主要元素列表
            fallback_elements: 备用元素列表
            max_wait_time: 最大等待时间
        Returns:
            bool: 是否有元素点击成功
        """
        logger.info("开始带备用方案的智能点击")

        # 尝试主要元素
        if self.smart_click_elements(primary_elements, max_wait_time):
            return True

        # 如果主要元素都失败，尝试备用元素
        if fallback_elements:
            logger.info("主要元素点击失败，尝试备用元素")
            return self.smart_click_elements(fallback_elements, max_wait_time)

        return False
