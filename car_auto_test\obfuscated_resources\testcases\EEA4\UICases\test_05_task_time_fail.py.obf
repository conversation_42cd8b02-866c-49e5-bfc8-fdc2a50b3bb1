import time
import pytest
import functools
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.ImageRecognition.Image_comparison import *
from utils.SerialTool.upper_control import vehicle_control
from utils.AdbPort.Adb_Port import adb
from utils.CloudOperation.chery_operation import Cloud
from config.api_config import APIConfig
from utils.SerialTool.actuator import act
from utils.AdbPort.Adb_Port import AdbExec

class Test_Task_TIME_FAIL:
    mode_value: int = None
    mode_dict: dict = None
    vehicle_flag: int = None

    # 使用mode fixture获取当前的模式值
    @pytest.fixture(autouse=True)
    def setup_ui_mode(self, mode, mode_dict, vehicle_flag):
        """设置当前的UI模式和mode_dict以及车型标志位"""
        self.mode_value = mode
        self.mode_dict = mode_dict
        self.vehicle_flag = vehicle_flag
        logger.info(f"当前测试环境: 模式值={self.mode_value}, 车型标志位={self.vehicle_flag}")

    def test_task_time_fail(self, driver, ui_eea4_app):
        task1 = int(APIConfig.test_data_store.get("task1", ""))
        task2 = int(APIConfig.test_data_store.get("task2", ""))
        task3 = int(APIConfig.test_data_store.get("task3", ""))
        task4 = int(APIConfig.test_data_store.get("task4", ""))

        # 任务失效
        logger.info('------------------ 开始制造:普通任务失效---------------------')
        cloud = Cloud()
        # 打开升降级任务
        cloud.sync_task([task1, task2])
        objects = Step_object(driver, self.mode_value, self.mode_dict, self.vehicle_flag)
        objects.get_task()
        # 关闭该VIN下的所有任务
        cloud.sync_task([])
        objects.driver.app_start(FOUR_APP_PACKAGE)
        objects.disclaimer_click(take_screenshot=False)
        objects.check_element_exists("invalid_task",max_wait_time=30)
        objects.screen_image('任务无效倒计时按钮', self.mode_value)
        time.sleep(22)
        if act.manufacture == 1:
            act.power_on()
            AdbExec().execute("adb shell input keyevent KEYCODE_WAKEUP")
        time.sleep(3)
        objects.screen_image('任务无效确认按钮按钮', self.mode_value)
        logger.info('点击任务无效确定按钮')
        objects.click_element('invalid_btn_confirm')
        #
        logger.info('------------------ 开始制造:不可预约任务---------------------')
        cloud.sync_task([task3, task4])
        objects.get_task()
        objects.screen_image('不可预约升级主页面显示', self.mode_value)
        # 打开升降级任务
        cloud.sync_task([task1, task2])



