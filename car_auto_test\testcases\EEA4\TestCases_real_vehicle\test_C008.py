# coding: utf-8
# Project：car_auto_test
# File：test_C008.py
# Author：杨郑健
# Date ：2025/6/11 17:45
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级主页面/小红点")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
@AllureDecorators.title("升级主页面：频繁点击升级主页面立即升级按钮")
def test_C008(driver, launch_app):
    """  目前频繁点击的逻辑没有走通
    测试步骤
    1、检测到升级任务并下载完成
    2、频繁点击升级主页面立即升级按钮

    预期结果
    2、“升级主页面”显示无异常，apk不会闪退
     """
    objects = Step_object(driver)
    objects.get_task()