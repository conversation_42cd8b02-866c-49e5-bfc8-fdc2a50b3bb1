# coding: utf-8
# Project：car_auto_test
# File：test_C005.py
# Author：杨郑健
# Date ：2025/6/11 15:47
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级主页面/小红点")
@AllureDecorators.title("埋点数据：关闭版本详情")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_C005(driver, launch_app):
    """
    测试步骤
    1、检测到升级任务并下载完成
    2、点击发行说明详情后，返回并查看后台埋点是否有显示 升级策略页面"关闭"按钮点击

    预期结果
    2、“版本详情页面“关闭”按钮点击”
     """
    objects = Step_object(driver)
    objects.get_task()
    objects.check_details_home()
    burying_point = Cloud("奇瑞汽车").get_burying_point()
    result = False
    for i in burying_point:
        if i["msg"] == '升级策略页面"关闭"按钮点击':
            result = True
    assert result