# coding: utf-8
# Project：car_auto_test
# File：test_A022.py
# Author：杨郑健
# Date ：2025/6/10 16:34
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from time import sleep
import pytest

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("根据bom进行任务检测")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_A022(driver, launch_app):
    """
    测试步骤
    1、后台配置“用户确认”升级任务
    2、车辆休眠后解防，启动车辆，查看车端是否仅检测本次任务中的ecu信息
    3、待车端检测到任务后，车辆再次休眠解防，切换电源至on档，查看车端是否仍仅检测本次任务中的ecu信息

    预期结果
    2、除智能件外，仅检测上报本次任务中的ecu信息
    3、除智能件外，仍检测上报本次任务中的ecu信息，可正常检测到升级任务
    """
    pass