# coding: utf-8
# Project：car_auto_test
# File：test_C007.py
# Author：杨郑健
# Date ：2025/6/11 17:28
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级主页面/小红点")
@AllureDecorators.title("升级主页面：反复进退升级主页面")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_C007(driver, launch_app):
    """
    测试步骤
    1、检测到升级任务并下载完成
    2、重复打开关闭apk 10次

    预期结果
    2、“升级主页面”显示无异常，apk不会闪退
     """
    objects = Step_object(driver)
    objects.get_task()
    adb = AdbExec()
    for i in range(10):
        adb.home()
        adb.execute(f"adb shell am start {FOUR_APP_PACKAGE}/{FOUR_APP_PACKAGE_ACTIVITY}")

    result = adb.execute("adb shell ps -ef | findstr carota").rfind(FOUR_APP_PACKAGE) != -1
    assert result