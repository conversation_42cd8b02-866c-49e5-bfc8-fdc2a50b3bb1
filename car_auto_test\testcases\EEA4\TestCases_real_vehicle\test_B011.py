# coding: utf-8
# Project：car_auto_test
# File：test_B011.py
# Author：杨郑健
# Date ：2025/6/11 14:13
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级包下载模块")
@AllureDecorators.title("新版本提示框:acc挡切换off挡触发")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_B011(driver, launch_app):
    """
    测试步骤
    1、车辆解防，切换电源档位至on档，待车端检测到升级任务
    2、升级包下载过程中切换电源至acc档
    3、待升级包下载完成后，切换车辆电源至off档

    预期结果
    3、“发现新版本，是否立即查看”，“查看“
    """
    pass