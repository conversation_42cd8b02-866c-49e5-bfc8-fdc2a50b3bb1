# coding: utf-8
# Project：car_auto_test
# File：test_A031.py
# Author：杨郑健
# Date ：2025/6/10 17:22
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("篡改pki证书key")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_A031(driver, launch_app):
    """
    测试步骤
    1、data/data/com.carota.chery/scm/key篡改pki证书key值
    2、车辆休眠后解防，切换电源至on档
    3、查看车端是否可重新获取pki证书

    预期结果
    3、自动下载pki证书
    """

    pass