# coding: utf-8
# Project：car_auto_test
# File：test_A001.py
# Author：杨郑健
# Date ：2025/6/10 13:48
from testcases.EEA4.PageObjects.TestObject import Step_object
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from utils.LoggingSystem.Logger import logger
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.ReportGenerator.Allure import *
from utils.SerialTool.actuator import act


@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("监听电源挡位以及tbox诊断状态：电源OFF挡")
@static_condition(conditions=
{
    "tbox_status": "READ",
    "task": "normal",
}
)
def test_A001(driver, launch_app):
    """
    测试步骤
    1、切换电源挡位至OFF挡
    2、进入工程模式点击任务检测按钮
    3、任务检测结束后寻找OFF挡无法拿到任务的状态（power : PWR_OFF, tel-diagnose: TEL_DIAGNOSE_READ）

    预期结果
    3、电源off档无法发起检测，监听电源档位及tbox诊断状态，实时日志中有电源档位状态power:off，tbox诊断状态tel_diagnose_read状态输出
    """
    # act.power_on()
    # act.power_off()
    objects = Step_object(driver)
    get_task_result = objects.get_task()
    results = otaLogAnalysis.find_keywords(["power : PWR_OFF", "tel-diagnose: TEL_DIAGNOSE_READ"])
    logger.info(f"实时日志检测结果:{results}")
    assert results
<<<<<<<< HEAD:car_auto_test/testcases/EEA4/TestCases_real_vehicle/test_A001.py
    assert get_task_result == False

========
    assert get_task_result == False
>>>>>>>> master:car_auto_test/testcases/EEA4/TestCases/test_A001.py
