'''
正常流程
'''

import time
import pytest
import functools
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.ImageRecognition.Image_comparison import *
from utils.AdbPort.Adb_Port import adb
from utils.SerialTool.actuator import act


class Test_Bench_Preditions:
    mode_value: int = None
    mode_dict: dict = None
    vehicle_flag: int = None
    language: int = None

    # 使用mode fixture获取当前的模式值
    @pytest.fixture(autouse=True)
    def setup_ui_mode(self, mode, mode_dict, vehicle_flag):
        """设置当前的UI模式和mode_dict以及车型标志位"""
        self.mode_value = mode
        self.mode_dict = mode_dict
        self.vehicle_flag = vehicle_flag
        logger.info(f"当前测试环境: 模式值={self.mode_value}, 车型标志位={self.vehicle_flag}")

    # @pytest.mark.skip
    def test_preditions_001(self, driver):
        objects = Step_object(driver, self.mode_value, self.mode_dict)

        # # 软开关下电失败、车速不满足
        logger.info('-----------------------开始制造软下电失败、车速不满足---------------------------------')
        if act.manufacture == 1:
            act.power_on()
        act.set_file('vehicle_speed', 30)
        objects.first_prepare_update_fail(take_screenshot=True)
        time.sleep(10)
        objects.screen_image('软开关下电失败、车速不满足', self.mode_value)
        logger.info('点击确定按钮')
        # 使用智能点击，尝试多个可能的按钮
        objects.smart_click_elements([
            'update_conditions_not_meet_btn',
            {'name': 'update_conditions_not_meet_btn_class', 'index': 2},
            't22_btn'
        ], max_wait_time=2)
        time.sleep(3)
        act.set_file('vehicle_speed', 0)

        # 软开关下电失败、车速不满足
        logger.info('-----------------------开始制造软下电失败---------------------------------')
        act.set_apk_file('vcu_power_hook', 1)
        # time.sleep(60)
        act.set_file('remote_poweroff', 1)
        objects.first_prepare_update_fail()
        time.sleep(30)
        objects.screen_image('软开关下电失败', self.mode_value)
        logger.info('点击确定按钮')
        # 使用智能点击，尝试多个可能的按钮
        objects.smart_click_elements([
            'now_btn',
            {'name': 'update_conditions_not_meet_btn_class', 'index': 2},
            't22_btn'
        ], max_wait_time=2)
        act.set_apk_file('vcu_power_hook', 0)

        # 进ota模式失败
        logger.info('-----------------------开始制造进ota模式失败---------------------------------')
        if act.manufacture == 1:
            act.power_on()
        act.set_file('otamodein', 1)
        objects.enter_ota_mode_fail()
        objects.click_element('otamodein_fail_btn')
        objects.smart_click_elements(
            [
                'otamodein_fail_btn',
                'otamodein_fail_btn2'
            ],max_wait_time=2
        )
        time.sleep(3)

        # 上电失败
        logger.info('-----------------------开始制造上电失败---------------------------------')
        if act.manufacture == 1:
            act.power_on()
        act.set_file('remote_poweron', 1)
        objects.power_on_fail()
        time.sleep(0.5)
        objects.screen_image('远程上电失败、升级条件不满足', self.mode_value)
        objects.click_element('remote_power_on_fail_btn')
        time.sleep(3)
        act.set_file('vehicle_speed', 0)

        # 上高压失败
        logger.info('-----------------------开始制造上高压失败---------------------------------')
        if act.manufacture == 1:
            act.power_on()
        act.set_file('hvoup', 1)
        objects.enter_hvo_up_fail()
        objects.click_element('hvo_on_fail_btn')
        time.sleep(3)

        logger.info('-----------------------开始制造蓄电池点电量不满足---------------------------------')
        act.set_file('ibs_soc', 1)
        objects.ibs_soc_fail()
        objects.click_element('ibs_soc_fail_btn')
        time.sleep(3)

    # @pytest.mark.skip
    def test_preditions_002(self, driver):
        objects = Step_object(driver, self.mode_value, self.mode_dict)

        logger.info('-----------------------开始制造手刹不满足---------------------------------')
        if act.manufacture == 1:
            act.power_on()
        act.set_file('epb_status', 0)
        objects.preconditions_fail(take_screenshot=True)
        objects.screen_image('手刹不满足', self.mode_value)
        objects.click_element('precondition_fail_cancelbtn')
        time.sleep(0.5)
        objects.click_element('precondition_fail_confirbtn')
        time.sleep(3)

        logger.info('-----------------------开始制造挡位不满足---------------------------------')
        if act.manufacture == 1:
            act.power_on()
        act.set_file('gearbox_gear', 2)
        objects.preconditions_fail()
        objects.screen_image('挡位不满足', self.mode_value)
        objects.click_element('precondition_fail_cancelbtn')
        time.sleep(0.5)
        objects.click_element('precondition_fail_confirbtn')
        time.sleep(3)

        logger.info('-----------------------开始制造电压不满足---------------------------------')
        if act.manufacture == 1:
            act.power_on()
        act.set_file('vehicle_speed', 0)
        act.set_apk_file('battery_voltage', 11000)
        objects.preconditions_fail()
        objects.screen_image('电压不满足', self.mode_value)
        objects.click_element('precondition_fail_cancelbtn')
        time.sleep(0.5)
        objects.click_element('precondition_fail_confirbtn')
        act.set_apk_file('battery_voltage', 14000)


        logger.info('-----------------------开始制造电源on档不满足---------------------------------')
        if act.manufacture == 1:
            act.power_on()
        act.set_file('power_gear', 0)
        objects.preconditions_fail()
        objects.screen_image('电源on档不满足', self.mode_value)
        objects.click_element('precondition_fail_cancelbtn')
        time.sleep(0.5)
        objects.click_element('precondition_fail_confirbtn')
        time.sleep(3)

        logger.info('-----------------------开始制造OBD不满足、下电失败---------------------------------')
        if act.manufacture == 1:
            act.power_on()
        act.set_file('obd_status', 1, 'remote_poweroff', 1)
        objects.preconditions_fail()
        objects.screen_image('OBD不满足', self.mode_value)
        act.set_apk_file('vcu_power_hook', 1)
        time.sleep(10)
        objects.click_element('precondition_fail_cancelbtn')
        time.sleep(0.5)
        objects.click_element('precondition_fail_confirbtn')
        # 下电失败
        objects.power_off_fail()
        act.set_apk_file('vcu_power_hook', 0)
        act.set_file('vehicle_speed', 0)

        time.sleep(3)

        # 车速不能是最后一个
        logger.info('-----------------------开始制造车速不满足---------------------------------')
        if act.manufacture == 1:
            act.power_on()
        act.set_file('vehicle_speed', 0)
        objects.power_on()
        act.set_file('vehicle_speed', 30)
        objects.check_element_exists('precondition_fail')
        logger.info('前置条件不通过')
        objects.screen_image('车速不满足', self.mode_value)
        objects.click_element('precondition_fail_cancelbtn')
        time.sleep(0.5)
        objects.click_element('precondition_fail_confirbtn')
        time.sleep(3)

        # 车速不能是最后一个
        logger.info('-----------------------开始制造充电不满足---------------------------------')
        if act.manufacture == 1:
            act.power_on()
        act.set_file('vehicle_speed', 0)
        act.set_apk_file('charge_state', 2)
        time.sleep(10)
        objects.preconditions_fail()
        objects.check_element_exists('precondition_fail')
        logger.info('前置条件不通过')
        objects.screen_image('充电不满足', self.mode_value)
        objects.click_element('precondition_fail_cancelbtn')
        time.sleep(0.5)
        objects.click_element('precondition_fail_confirbtn')
        act.set_apk_file('charge_state', 0)
        time.sleep(3)

        logger.info('-----------------------开始制造动力电池电量不满足---------------------------------')
        if act.manufacture == 1:
            act.power_on()
        act.set_file('battery_soc', 10)
        objects.preconditions_fail()
        objects.screen_image('动力电池电量不满足', self.mode_value)
        objects.screen_image('前置条件不满足120S倒计时', self.mode_value)
        time.sleep(1)
        objects.click_element('precondition_fail_cancelbtn')
        time.sleep(1)
        objects.screen_image('前置条件不满足确定按钮20S倒计时', self.mode_value)
        time.sleep(22)
        if act.manufacture == 1:
            adb.execute("adb shell input keyevent KEYCODE_WAKEUP")
        objects.screen_image('前置条件不满足确定按钮', self.mode_value)
        objects.click_element('precondition_fail_confirbtn')

        # 德赛的这个有问题
        logger.info('-----------------------开始制造放电不满足、退出ota模式失败---------------------------------')
        if act.manufacture == 1:
            act.power_on()
            act.set_file('otamodeout', 1)
            act.set_apk_file('vtol_state', 1)
        else:
            act.set_file('obc_chg_sts', 1, 'otamodeout', 1)
        objects.preconditions_fail()
        objects.screen_image('放电不满足', self.mode_value)
        objects.click_element('precondition_fail_cancelbtn')
        time.sleep(0.5)
        objects.click_element('precondition_fail_confirbtn')
        # 退ota模式失败
        objects.otamode_out_fail()
        act.set_file('speed', 0)
        act.set_apk_file('tel_diagnose_state',0)
        time.sleep(1)
        act.ota_mode_out()
