# coding: utf-8
# Project：car_auto_test
# File：test_A020.py
# Author：杨郑健
# Date ：2025/6/10 16:28
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from time import sleep
import pytest

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("ota诊断的前置条件：车辆处于运输模式")
@static_condition(conditions=
{
    "vehicle_mode": "TRANSPORT",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_A020(driver, launch_app):
    """
    测试步骤
    1、切换为运输模式
    2、上READY检测任务，车辆处于运输模式、obd口未被占用、车辆需要有tbox
    3、任务检测结束后检查拿到任务的状态

    预期结果
    3、正常发起检测
    """
    objects = Step_object(driver)
    get_task_result = objects.get_task()
    assert get_task_result