'''
vcc串口工具 通过ssh连接
'''
import time
import paramiko
from utils.LoggingSystem.Logger import logger
import json


class vcctool:
    def __init__(self):
        self.ssh_press = paramiko.SSHClient()
        self.ssh_press.set_missing_host_key_policy(paramiko.AutoAddPolicy)
        # 初始化连接
        try:
            self.ssh_press.connect('************', username='root', password='Chery12#$', port=22)
            # self.ssh_press.connect('************', username='root', password='Huawei12#$', port=22)
            logger.info('连接VCC成功')
        except Exception as e:
            logger.error('连接VCC失败')

    def connect_vcc(self, max_retries=1, retry_interval=2):
        """
        连接VCC，支持重试

        Args:
            max_retries: 最大重试次数
            retry_interval: 重试间隔（秒）

        Returns:
            bool: 连接是否成功
        """
        for attempt in range(max_retries):
            try:
                self.ssh_press = paramiko.SSHClient()
                self.ssh_press.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                self.ssh_press.connect('************', username='root', password='Chery12#$', port=22)
                logger.info('连接VCC成功')
                return True
            except Exception as e:
                logger.error(f'连接VCC失败，尝试 {attempt + 1}/{max_retries}: {str(e)}')
                if attempt < max_retries - 1:
                    time.sleep(retry_interval)

        logger.error(f'连接VCC失败，已达到最大重试次数 {max_retries}')
        return False

    def vcc_execute(self, cmd):
        """
        执行VCC命令 等待全部回调结束 才会继续往下走
        """
        if not self.ssh_press:
            logger.error("SSH连接未建立，尝试重新连接")
            if not self.connect_vcc():
                logger.error("重新连接失败，无法执行命令")
                return False
        try:
            # 处理后台运行命令
            is_background = cmd.strip().endswith('&')
            if is_background:
                # 移除末尾的 &
                cmd = cmd.strip()[:-1].strip()
                # 使用 nohup 和重定向来实现后台运行
                cmd = f"nohup {cmd} > /dev/null 2>&1 &"

            stdin, stdout, stderr = self.ssh_press.exec_command(cmd, timeout=10)
            error = stderr.read().decode(encoding='utf-8')

            if error == '':
                logger.info(f"执行VCC命令成功: {cmd}")
                return True
            else:
                logger.error(f"执行VCC命令 {cmd} 错误: {error}")
                return False
        except AttributeError as e:
            if "'NoneType' object has no attribute 'open_session'" in str(e):
                logger.error("SSH连接已断开，尝试重新连接")
                if self.connect_vcc():
                    # 重新连接成功，再次尝试执行命令
                    return self.vcc_execute(cmd)
                else:
                    logger.error("重新连接失败，无法执行命令")
                    return False
            else:
                logger.error(f"执行VCC命令 {cmd} 异常: {str(e)}")
                return False
        except Exception as e:
            logger.error(f"执行VCC命令 {cmd} 异常: {str(e)}")
            return False

    def vcc_execute_nonblocking(self, cmd):
        """
        非阻塞执行VCC命令，不等待命令完成就返回
        """
        if not self.ssh_press:
            logger.error("SSH连接未建立，尝试重新连接")
            if not self.connect_vcc():
                logger.error("重新连接失败，无法执行命令")
                return False

        try:
            # 获取传输通道
            transport = self.ssh_press.get_transport()
            if not transport:
                logger.error("SSH传输通道未建立")
                return False

            # 打开会话
            channel = transport.open_session()

            # 执行命令但不等待结果
            channel.exec_command(cmd)

            logger.info(f"已发送命令(非阻塞): {cmd}")
            return True
        except Exception as e:
            logger.error(f"发送命令时出错: {str(e)}")
            return False

    def set_file(self, *args):
        """
        设置配置文件，支持传递多个键值对
        Returns:
            bool: 操作是否成功
        """
        from config.Config import eea5_config_file
        c_json = eea5_config_file.copy()

        # 处理参数
        if len(args) == 2:
            # 单个键值对
            key, value = args
            logger.info(f"设置单个键值对: {key}={value}")
            c_json[key] = value
        elif len(args) > 2 and len(args) % 2 == 0:
            # 多个键值对
            for i in range(0, len(args), 2):
                key = args[i]
                value = args[i + 1]
                logger.info(f"设置键值对: {key}={value}")
                c_json[key] = value
        else:
            logger.error("参数格式错误，应为(key, value)或(key1, value1, key2, value2, ...)")
            return False

        logger.info(f"更新后的配置: {c_json}")

        c_json = json.dumps(c_json)

        # 构建命令
        cmd = f"echo '{c_json}' > /opt/ota_pkg/vehicle_info_config.json"

        # 执行命令
        stdin, stdout, stderr = self.ssh_press.exec_command(cmd)

        # 检查是否有错误
        error = stderr.read().decode('utf-8')
        if error:
            logger.error(f"写入配置文件失败: {error}")
            return False

        logger.info("配置文件已成功更新")

        self.reboot_master()
        return True

    def reboot_master(self):
        self.vcc_execute('pkill -9 ota')
        logger.info('重启vcc中的master进程')
        time.sleep(2)
        self.vcc_execute('/opt/oem_app/otamaster &')
        time.sleep(15)


# vcc = vcctool()
# vcc.set_file('epb_status', 1)
# vcc.set_file('remote_poweroff', 1)
# vcc.vcc_execute('rm /opt/ota_pkg/dm/')
# vcc.vcc_execute('/opt/ota_pkg/oem_app/uds_shell --flash_data_config /opt/ota_pkg/output/flash.info  --log  /opt/ota_pkg/uds_shell.log --mask 0x0000 --channel 123 --level 5 --log_size 5*1024*1024&')

# vcc.set_file('hv_battle_soc', 100)
# for i in range(100):
#     vcc.vcc_execute('/opt/ota_pkg/oem_app/uds_shell --flash_data_config /opt/ota_pkg/output/flash.info  --log  /opt/ota_pkg/uds_shell.log --mask 0x0000 --channel 123 --level 5 --log_size 5*1024*1024')
# vcc.vcc_execute(
#     'cd /opt/ota_pkg/oem_app &&./up_parser  --source_path /opt/ota_pkg/ftp/BNCM0834.tar.gz --out putcheckab /opt/ota_pkg/out/checkab.out  --target_path /opt/ota_pkg/test_dir/  --log  /opt/ota_pkg/logs/up_parser.log --flash_info_path /opt/ota_pkg/output/flash.info --level 4')
#
# vcc.vcc_execute(
#             '/opt/ota_pkg/oem_app/uds_shell --flash_data_config /opt/ota_pkg/output/flash.info  --log  /opt/ota_pkg/uds_shell.log --mask 0x0000 --channel 123 --level 5 --log_size 5*1024*1024')

# vcc.set_file('remote_poweroff', 1)
# vcc.set_file('otamodeout', 1)
