# coding: utf-8
# Project：car_auto_test
# File：test_A004.py
# Author：杨郑健
# Date ：2025/6/10 14:26
from testcases.EEA4.PageObjects.TestObject import Step_object
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from utils.LoggingSystem.Logger import logger
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.SerialTool.serial_tool import serial_tool
from utils.CloudOperation.chery_operation import Cloud
from utils.ReportGenerator.Allure import *

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("ota诊断的前置条件：车辆处于工厂暂停模式")
@static_condition(conditions=
{
    "vehicle_mode": "FACTORY_PAUSE",
    "task": "normal"
}
)
def test_A004(driver, launch_app):
    """
    测试步骤
    1、切换至工厂模式暂停模式
    2、进入工程模式点击任务检测按钮
    3、任务检测结束后寻找工厂暂停模式和无法拿到任务的状态（vehicle mode: FACTORY_PAUSED）

    预期结果
    3、工厂模式无法发起检测，监听车辆模式，实时日志中有车辆模式vehicle mode:FACTORY_PAUSED相关信息输出
    """
    objects = Step_object(driver)
    get_task_result = objects.get_task()
    results = otaLogAnalysis.find_keywords(["vehicle mode: FACTORY_PAUSED"])
    logger.info(f"实时日志检测结果:{results}")
    assert results
    assert not get_task_result