from utils.SerialTool.serial_tool import SerialTool
from utils.SerialTool.vcc_tool import vcctool
from utils.SerialTool.qnx_execute import cmd_execute_with_maga, QNX_execute, cmd_execute_with_neusoft
from utils.AdbPort.Adb_Port import adb
from utils.LoggingSystem.Logger import logger


class actuator:
    def __init__(self, board=False):
        self.manufacture = None
        self.info = {1: "德赛", 2: "伯泰克镁佳", 3: "东软", 4: "讯飞"}
        self.Neusoft = "adb shell busybox telnet 172.20.1.1"
        if board:
            self.check_car_type()

    def check_car_type(self, ):
        # 检查车机类型，更新配置标志位
        self.manufacture = adb.get_manufacture()
        logger.info(f"当前车机为：{self.info[self.manufacture]}")
        return self.manufacture

    def set_file(self, *args):
        logger.info(f"执行{self.info[self.manufacture]}主机推文件操作")
        if self.manufacture == 1:
            SerialTool().set_file(*args)

        if self.manufacture == 2:
            cmd_execute_with_maga().set_file(*args)

        if self.manufacture == 3:
            cmd_execute_with_neusoft().set_file(*args)
            # QNX_execute(base_command=self.Neusoft, command="")
        if self.manufacture == 4:
            adb.set_file()

    def power_on(self):
        logger.info(f"执行{self.info[self.manufacture]}主机上电操作")
        if self.manufacture == 1:
            SerialTool().power_on()
        if self.manufacture == 2:
            cmd_execute_with_maga().power_on()
        if self.manufacture == 3:
            cmd_execute_with_neusoft().power_on()
            # QNX_execute(base_command=self.Neusoft, command="")
        if self.manufacture == 4:
            pass

    def power_off(self):
        logger.info(f"执行{self.info[self.manufacture]}主机下电操作")
        if self.manufacture == 1:
            SerialTool().power_off()
        if self.manufacture == 2:
            cmd_execute_with_maga().power_off()
        if self.manufacture == 3:
            cmd_execute_with_neusoft().power_off()
            # QNX_execute(base_command=self.Neusoft, command="")
        if self.manufacture == 4:
            pass

    def set_apk_file(self, *args):
        adb.set_file(*args)

    def ota_mode_out(self):
        logger.info(f"执行{self.info[self.manufacture]}主机退OTA操作")
        if self.manufacture == 1:
            SerialTool().ota_out()
        if self.manufacture == 2:
            cmd_execute_with_maga().ota_mode_out()
        if self.manufacture == 3:
            cmd_execute_with_neusoft().power_off()
            # QNX_execute(base_command=self.Neusoft, command="")
        if self.manufacture == 4:
            pass

    def rm_package(self):
        logger.info(f"执行{self.info[self.manufacture]}主机删除package操作")
        if self.manufacture == 1:
            SerialTool().rm_package()
        if self.manufacture == 2:
            cmd_execute_with_maga().rm_package()
        if self.manufacture == 3:
            cmd_execute_with_maga().rm_package()
            # QNX_execute(base_command=self.Neusoft, command="")
        if self.manufacture == 4:
            pass

act = actuator(board=True)
# act.set_file('vehicle_speed',25,'obd_status',1)
# act.power_on()
# act.set_file('vehicle_lock_door_status', 0,'charge_state', 2,)
# act.set_apk_file('TEL_DIAGNOSE_READ', 0)
# act.set_file('remote_poweroff', 1)
# act.ota_mode_out()
# act.set_apk_file('tel_diagnose_state',0)
# act.set_file('battery_soc', 99)
# act.rm_package()
# act.set_file('ibs_soc', 1)

# if __name__ == "__main__":
    # act.set_file('gearbox_gear', 2)
#     act.set_apk_file('vcu_power_hook', 0,'battery_voltage',14000)
# act.set_apk_file('vcu_power_hook', 0)
# act.check_car_type()
# act.power_on()
# act.set_apk_file('charge_state', 0)
# act.power_off()
