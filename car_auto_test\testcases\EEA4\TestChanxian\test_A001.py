from testcases.EEA4.PageObjects.TestObject import Step_object
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from utils.ReportGenerator.Allure import *

# @AllureDecorators.epic("任务检测模块")
# @AllureDecorators.title("监听电源挡位以及tbox诊断状态：电源ACC挡")
@static_condition(conditions=
{
        # "power": "ACC",
        "tbox_status": "READ",
        # "task": "normal"
}
)
def test_A001(driver, launch_app):

    objects = Step_object(driver)
    # get_task_result = objects.get_task()
    results = otaLogAnalysis.find_keywords(["tel-diagnose: TEL_DIAGNOSE_READ"])
    logger.info(f"实时日志检测结果:{results}")
    assert results
    # assert not get_task_result