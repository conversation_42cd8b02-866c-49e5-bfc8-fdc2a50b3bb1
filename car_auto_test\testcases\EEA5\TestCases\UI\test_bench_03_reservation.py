import time

import pytest
import functools

from testcases.EEA5.PageObjects.TestObject import Step_object
from utils.ImageRecognition.Image_comparison import *
from utils.SerialTool.vcc_tool import vcc

class Test_Bench_Reservation:
    # 使用mode fixture获取当前的模式值
    @pytest.fixture(autouse=True)
    def setup_ui_mode(self, mode, mode_dict):
        """设置当前的UI模式和mode_dict"""
        self.mode_value = mode
        self.mode_dict = mode_dict
        logger.info(f"当前测试环境: 模式值={self.mode_value}")

    @pytest.mark.skip
    def test_bench_reservation_001(self, driver,ui_app):
        '''
        预约设置正向流程
        Args:
            driver:
            launch_app:

        Returns:

        '''
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        # objects.get_task()


        # 预约升级任务
        objects.tomorrow_time_set_pass()

        # 取消预约流程
        objects.tomorrow_time_set_cancel()

    # @pytest.mark.skip
    def test_bench_reservation_002(self, driver,ui_app):
        '''
        预约失败
        Args:
            driver:
            launch_app:

        Returns:

        '''
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.tomorrow_time_set_fail()
        logger.info("等待tbo恢复")
        time.sleep(60)


    # @pytest.mark.skip
    def test_bench_reservation_003(self, driver,ui_app):
        '''
        取消预约失败
        Args:
            driver:
            launch_app:

        Returns:

        '''
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        try:
            # 执行测试步骤
            objects.tomorrow_time_set_pass(take_screenshot=False)
            objects.tomorrow_time_set_cancel_fail()
            logger.info("测试步骤执行完成")
        finally:
            # 测试完成后的清理操作，不使用yield
            logger.info("执行测试清理操作")
            logger.info("等待30s后tbox网络恢复")
            time.sleep(60)

            objects.tomorrow_time_set_cancel(take_screenshot=False)

        time.sleep(20)

    @pytest.mark.skip
    def test_bench_reservation_004(self, driver):
        '''
        预约时间到条件不满足、 未再OFF档
        Args:
            driver:
            launch_app:

        Returns:

        '''
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.tomorrow_predition_fail()
        objects.screen_image('预约不满足-未在设防、OFF档', self.mode_value)
        objects.click_element('schedule_predition_fail_btn')

    @pytest.mark.skip
    def test_bench_reservation_005(self, driver):
        '''
        预约时间到条件不满足、 未在未充电
        Args:
            driver:
            launch_app:

        Returns:

        '''
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        vcc.set_file('power_mode', 0,'pwron_status',0,'vehicle_charge_leg_status',6)
        objects.tomorrow_predition_fail()
        objects.screen_image('预约不满足-未在设防、未充电', self.mode_value)
        objects.click_element('schedule_predition_fail_btn')

    @pytest.mark.skip
    def test_bench_reservation_006(self, driver):
        '''
        预约时间到条件不满足、 未再OFF档、未在未充电
        Args:
            driver:
            launch_app:

        Returns:

        '''
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        vcc.set_file('vehicle_charge_leg_status', 6)
        objects.tomorrow_predition_fail()
        objects.screen_image('预约不满足-未在设防、未在OFF挡、未充电', self.mode_value)
        objects.click_element('schedule_predition_fail_btn')

    @pytest.mark.skip
    def test_bench_reservation_007(self, driver):
        '''
        先设置预约升级、预约充电冲突
        Args:
            driver:
            launch_app:

        Returns:

        '''
        objects = Step_object(driver, self.mode_value, self.mode_dict)

        try:
            # 执行测试步骤
            objects.Upgrade_charging_conflict()
            logger.info("测试步骤执行完成")
        finally:
            # 测试完成后的清理操作，不使用yield
            logger.info("执行测试清理操作")
            driver.app_start('com.carota.chery.v5')
            objects.tomorrow_time_set_cancel(take_screenshot=False)

    @pytest.mark.skip
    def test_bench_reservation_008(self, driver):
        '''
        先设置预约充电、预约升级冲突
        Args:
            driver:
            launch_app:

        Returns:

        '''
        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.Upgrade_travel_conflict()
