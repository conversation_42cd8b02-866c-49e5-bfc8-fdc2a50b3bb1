# coding: utf-8
# Project：car_auto_test
# File：test_E022.py
# Author：杨郑健
# Date ：2025/6/13 17:20
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("埋点数据")
@AllureDecorators.title("进入升级模式失败点击确定按钮")
def test_E022(driver, launch_app):
    """
    测试步骤
    1、“升级主页面”，点击“立即升级”，“免责声明”点击“同意”
    2、“车况检查中”下电成功，进入“车辆即将进入on档升级”，制造进ota失败，进入“进入升级模式失败”，查看后台埋点数据

    预期结果
    2、“进入ota模式失败页面显示”
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        "tbox_status": "NOTREAD",
        "power": ["READY", "OFF"],
        "task": "normal",
        "gear": "P",
        "handbrake": "ON"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'READY':
            objects.get_task()
            logger.info("READY挡验证完成")
            assert True
        elif current_state == 'OFF':
            logger.info("OFF挡制造进OTA失败，查看埋点数据")
            serial_tool.set_file("otamodein", 1)
            objects.disclaimer_click()
            objects.check_element_exists("otamodein_fail_btn")
            objects.click_element("otamodein_fail_btn")
            serial_tool.set_file("otamodein", 0)
            burying_points = Cloud("奇瑞汽车").get_burying_point()
            result = False
            for burying_point in burying_points:
                if burying_point["msg"] == '进入ota模式失败“确认”按钮':
                    result = True
            assert result
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()