'''Flask配置文件'''

import os


# 基础配置
class APIConfig:
    # Flask基础配置
    SECRET_KEY = 'your-secret-key'
    STATIC_FOLDER = 'static'
    TEMPLATE_FOLDER = 'templates'

    # 基础路径配置
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    IMAGE_BASE_PATH = os.path.join(BASE_DIR, 'static')
    # IMAGE_BASE_PATH = os.path.join(BASE_DIR, 'static', 'images')
    TEMP_DIR = os.path.join(BASE_DIR, 'temp')  # 添加临时目录配置

    # 确保临时目录存在
    os.makedirs(TEMP_DIR, exist_ok=True)

    # 其他全局配置
    DEBUG = False
    HOST = '0.0.0.0'
    PORT = 5000
    THREADED = True

    # 云端任务存放列表
    cloud_tasks = {}

    # 测试状态
    test_data_store = {
        "current_test": None,
        "screenshots": [],
        "test_status": "idle",
        "current_language": "",
        "current_mode": "",
        "start_time": None,
        "end_time": None,
        "is_test_completed": False,
        "vin": "",
        "task1": "",
        "task2": "",
        "task3": "",
        "task4": "",
        "password": ""
    }
