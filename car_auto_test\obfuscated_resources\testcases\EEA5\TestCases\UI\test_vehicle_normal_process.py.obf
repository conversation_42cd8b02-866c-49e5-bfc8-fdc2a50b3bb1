'''
正常流程 4.5h
'''

import time
import pytest
import functools
from testcases.EEA5.PageObjects.TestObject import Step_object
from utils.ImageRecognition.Image_comparison import *
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.vcc_tool import vcc

class Test_Vehicle_Normal_Process:
    # 使用mode fixture获取当前的模式值
    @pytest.fixture(autouse=True)
    def setup_ui_mode(self, mode, mode_dict):
        """设置当前的UI模式和mode_dict"""
        self.mode_value = mode
        self.mode_dict = mode_dict
        logger.info(f"当前测试环境: 模式值={self.mode_value}")

    @pytest.mark.skip
    def test_vehicle_normal_process_001(self, driver, ui_app):
        """
        正常流程
        """
        #
        objects = Step_object(driver, self.mode_value, self.mode_dict)

        # 拿到升级任务
        objects.get_task()

        # 升级成功
        objects.upgrade_success_page()

    # @pytest.mark.skip
    def test_eea5_ui_lose_efficacy(self, driver, ui_app):
        """
        任务失效 ，可以放在最后面，待验证
        """
        page = Cloud("奇瑞欧盟")

        objects = Step_object(driver, self.mode_value, self.mode_dict)
        objects.get_task()
        page.close_task(name=26940, id=26940)
        page.close_task(name=26939, id=26939)
        objects.task_inivail()
        page.open_task(name=26940, id=26940)
        page.open_task(name=26940, id=26940)

