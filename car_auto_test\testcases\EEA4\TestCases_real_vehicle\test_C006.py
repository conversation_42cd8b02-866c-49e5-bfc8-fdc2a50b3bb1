# coding: utf-8
# Project：car_auto_test
# File：test_C006.py
# Author：杨郑健
# Date ：2025/6/11 17:26
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级主页面/小红点")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
@AllureDecorators.title("升级主页面")
def test_C006(driver, launch_app):
    """
    测试步骤
    1、检测到升级任务并下载完成
    2、查看升级主页面按钮是否可以点击

    预期结果
    2、“发现新版本”：升级时长、发行说明，点击“详情”可查阅全文，“预约升级”(高亮显示)、“立即升级”
     """
    objects = Step_object(driver)
    objects.get_task()
    check_result = objects.check_schedule_button()
    assert check_result