# coding: utf-8
# Project：car_auto_test
# File：test_F022.py
# Author：杨郑健
# Date ：2025/6/24 11:34
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级结果")
@AllureDecorators.title("升级完成，下电失败”点击“确定”")
def test_F022(driver, launch_app):
    """
    测试步骤
    1、后台配置“用户确认”升级任务
    2、车端检测到升级任务并下载完成
    3、模拟下电失败

    预期结果
    1、“更新进行中”待升级完成，升级成功，执行下电失败
    2、“升级完成，下电失败”点击“确定”
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        "tbox_status": "NOTREAD",
        "power": ["READY", "OFF"],
        "task": "normal",
        "gear": "P",
        "handbrake": "OFF"
    },
        delay=None  # 不设置固定延迟，等待断言完成
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'READY':
            objects.get_task()
            logger.info("READY挡验证完成")
            assert True
        elif current_state == 'OFF':
            logger.info("OFF挡退OTA失败，查看埋点数据")
            objects.switch_night()
            objects.update_success_power_fail()
            result = objects.check_element_exists("remote_power_off_fail_btn")
            objects.click_element("remote_power_off_fail_btn")
            assert result
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()