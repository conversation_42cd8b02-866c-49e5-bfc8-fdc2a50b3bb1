# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],  # 入口脚本
    pathex=[],  # 额外的搜索路径（可留空）
    binaries=[],  # 需要打包的二进制文件
    datas=[
    ('templates','templates'),
    ('static','static'),
    ('config','config'),
    ('utils','utils'),
    ('api','api'),
    ('testcases','testcases'),
    ('pytest.ini','.'),
    ('conftest.py','.'),
    ('core','core'),

    ],  # 需要打包的数据文件
     hiddenimports=[
        'uiautomator2',
        'packaging',
        'simple_deobfuscator',
        'win32timezone'

    ],  # 隐式导入的模块
    hookspath=[],  # 自定义 hook 路径
    hooksconfig={},  # hook 配置
    runtime_hooks=[],  # 运行时 hook
    excludes=[],  # 排除的模块
    win_no_prefer_redirects=False,  # Windows 特定设置
    win_private_assemblies=False,  # Windows 特定设置
    cipher=block_cipher,  # 加密设置

    noarchive=False,  # 是否不使用归档
)
pyz = PYZ(
    a.pure,  # 纯 Python 模块
    a.zipped_data,  # 压缩数据
    cipher=block_cipher,  # 加密设置
)

exe = EXE(
    pyz,  # 上一步生成的 PYZ 对象
    a.scripts,  # 脚本入口
    [],  # 额外参数
    exclude_binaries=True,  # 是否排除二进制
    name='ota_ui',  # 生成的 exe 名称
    debug=False,  # 是否开启调试
    bootloader_ignore_signals=False,  # 是否忽略信号
    strip=False,  # 是否去除符号表
    upx=True,  # 是否使用 upx 压缩
    console=True,  # 是否显示控制台（cmd窗口）
    disable_windowed_traceback=False,  # 是否禁用窗口化回溯
    argv_emulation=False,  # MacOS 参数模拟
    target_arch=None,  # 目标架构
    codesign_identity=None,  # 代码签名
    entitlements_file=None,  # 权限文件
    icon ='ota.ico',
   # 指定图标文件
)

coll = COLLECT(
    exe,  # 上一步生成的 EXE 对象
    a.binaries,  # 二进制文件
    a.zipfiles,  # zip 文件
    a.datas,  # 数据文件
    strip=False,  # 是否去除符号表
    upx=True,  # 是否使用 upx 压缩
    upx_exclude=[],  # 不使用 upx 压缩的文件
    name='main',  # 最终生成的文件夹名称
)
