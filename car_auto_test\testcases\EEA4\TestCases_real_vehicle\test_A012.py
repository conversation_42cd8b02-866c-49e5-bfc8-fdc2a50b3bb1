# coding: utf-8
# Project：car_auto_test
# File：test_A012.py
# Author：杨郑健
# Date ：2025/6/10 15:46
from testcases.EEA4.PageObjects.TestObject import Step_object
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from utils.LoggingSystem.Logger import logger
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.SerialTool.serial_tool import serial_tool
from utils.CloudOperation.chery_operation import Cloud
from time import sleep
from utils.ReportGenerator.Allure import *

@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("维持电源off档10分钟后踩刹车上ready")
def test_A012(driver, launch_app):
    """
    测试步骤
    1、off挡位维持10分钟
    2、点击检测更新
    3、检查是否能检测到任务以及Tbox诊断关键字（tel-diagnose: TEL_DIAGNOSE_NOT_READ）

    预期结果
    3、待tbox诊断完成，正常发起检测
    """
    objects = Step_object(driver)

    @dynamic_condition(conditions=
    {
        'power': ['OFF', 'READY'],  # 将挡位放在列表中
        "tbox_status": "NOTREAD",
        "task": "normal"
    },
        delay=None
    )
    def verify_conditions(**kwargs):
        # 获取当前挡位信息
        current_index = kwargs.get('current_index', 0)
        current_state = kwargs.get('current_state', 'Unknown')
        logger.info(f"当前状态{current_state}挡状态")

        # 根据当前挡位进行不同的断言
        if current_state == 'OFF':
            logger.info("OFF挡时强制等待10分钟")
            sleep(600)
            assert True
        elif current_state == 'READY':
            get_task_result = objects.get_task()
            result = otaLogAnalysis.find_keywords(["el-diagnose: TEL_DIAGNOSE_NOT_READ"])
            logger.info("READY验证任务检测状态和tbox诊断关键字")

            serial_tool.set_file("obd_status", 0)
            assert result and get_task_result
        else:
            logger.warning(f"未知挡位: {current_state}")

    # 执行测试
    verify_conditions()