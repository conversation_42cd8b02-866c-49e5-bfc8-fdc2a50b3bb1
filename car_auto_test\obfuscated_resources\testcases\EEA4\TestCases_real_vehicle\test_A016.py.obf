# coding: utf-8
# Project：car_auto_test
# File：test_A016.py
# Author：杨郑健
# Date ：2025/6/10 16:04
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from time import sleep
import pytest

@pytest.mark.skip
@AllureDecorators.epic("任务检测模块")
@AllureDecorators.title("检测过程中发起obd诊断")
@static_condition(conditions=
{  # 缺少日志关键字，用例未写完，暂不执行
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"

}
)
def test_A013(driver, launch_app):
    """
    测试步骤
    1、车辆休眠后解防，启动车辆，待tbox诊断完成，发起检测
    2、检测过程中使用诊断仪发起obd诊断
    3、保持obd诊断状态，待第一次检测重试发起
    4、中断obd口设备连接，待第二次检测重试发起"

    预期结果
    2、检测中断，1分钟后发起第一次检测重试
    3、检测中断，1分钟后发起第二次检测重试
    4、正常发起检测"
    """
    objects = Step_object(driver)
    tbox_results = otaLogAnalysis.find_keywords_with_timeout(["TEL_DIAGNOSE_NOT_READ"], 20)
    objects.get_task_noback()
    serial_tool.set_file("obd_status", 1)
    if tbox_results:
        retry_results = otaLogAnalysis.find_keywords_with_timeout([""], 120)
        task_result = objects.check_update_status()
        serial_tool.set_file("obd_status", 0)
        assert task_result and retry_results
    serial_tool.set_file("obd_status", 0)
    assert tbox_results