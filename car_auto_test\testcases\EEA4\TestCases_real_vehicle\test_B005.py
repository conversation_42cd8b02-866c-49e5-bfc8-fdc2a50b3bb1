# coding: utf-8
# Project：car_auto_test
# File：test_B005.py
# Author：杨郑健
# Date ：2025/6/11 13:50
from testcases.EEA4.PageObjects.Condition import static_condition, dynamic_condition
from testcases.EEA4.PageObjects.TestObject import Step_object
from utils.LogAnalysis.OtaLogAnalysis import otaLogAnalysis
from utils.CloudOperation.chery_operation import Cloud
from utils.SerialTool.serial_tool import serial_tool
from utils.LoggingSystem.Logger import logger
from utils.ReportGenerator.Allure import *
from config.Config import *
from time import sleep
import pytest

@AllureDecorators.epic("升级包下载模块")
@AllureDecorators.title("新版本提示框点击查看")
@static_condition(conditions=
{
    "power": "READY",
    "tbox_status": "NOTREAD",
    "task": "normal"
}
)
def test_B005(driver, launch_app):
    """
    测试步骤
    1、车辆解防，启动车辆，待车端检测到升级任务并下载完成
    2、切换电源至off档，触发新版本提示框
    3、“发现新版本，是否立即查看”点击“查看”，查看后台埋点数据

    预期结果
    3、（code 52）“升级推送页面，点击查看按钮”
    """
    pass